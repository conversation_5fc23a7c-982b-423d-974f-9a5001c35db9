<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { searchSkuListV2 } from '@/api/order';
import { isPro } from '@/utils/index';
import type { SearchItemType } from '@/types/item';

const props = defineProps({
  customerNo: { type: String, default: '' },
  customerMaterialNo: { type: String, default: '' },
  selectedSku: { type: Object, default: () => {} },
  disabled: { type: Boolean, default: false },
});

const emit = defineEmits(['update:selectedSku', 'handleSelectSku']);
const select = ref(null);
const skuList = ref<SearchItemType[]>([]);
const isLoading = ref(false);
const currentSelectSku = ref<Partial<SearchItemType>>();
const searchKeyWord = ref('');
const defaultOption = {
  skuNo: '商品编号',
  materialDescribe: '商品描述',
  customerSkuNo: '客户物料号',
  customerSkuName: '客户物料名称',
  customerSkuUnitCount: '客户物料数量',
  customerSkuUnit: '客户物料数量单位',
  customerSkuSpecification: '客户物料规格型号',
  dataSource: '',
  index: 0,
};

onMounted(() => {
  // 传入sku的时候初始化
  if (props.selectedSku && props.selectedSku.skuNo) {
    currentSelectSku.value = props.selectedSku as SearchItemType;
    skuList.value.push(defaultOption, props.selectedSku as SearchItemType);
  }
});

watch(
  () => props.selectedSku.skuNo,
  (newVal) => {
    if (newVal) {
      currentSelectSku.value = props.selectedSku as SearchItemType;
      skuList.value = [defaultOption, props.selectedSku as SearchItemType];
    } else {
      currentSelectSku.value = {};
    }
  }
);

const searchSkuList = (val: string, customerMaterialNo: string) => {
  if (val) {
    isLoading.value = true;
    searchKeyWord.value = val;
    const data = {
      vague: val,
      customerCode: props.customerNo,
    };
    searchSkuListV2(data).then((res) => {
      if (res.code === 200) {
        if (Array.isArray(res.data) && res.data.length > 0) {
          skuList.value = res.data.map(
            (item: SearchItemType, index: number) => ({
              index: index + 1,
              ...item,
            })
          );
          skuList.value.unshift(defaultOption);
          if (customerMaterialNo) {
            skuList.value = skuList.value.filter(
              (item, index) =>
                index === 0 || item.customerSkuNo === customerMaterialNo
            );
          }
        }
      } else {
        skuList.value = [];
      }
      isLoading.value = false;
    });
  }
};

const addSkuToTable = () => {
  emit('handleSelectSku', currentSelectSku.value, searchKeyWord.value);
};

const toDataSource = (item: any) => {
  if (item.dataSource) {
    switch (item.dataSource) {
      case 'QTS':
        window.open(
          `https://${isPro() ? 'qts' : 'qts-uat'}.zkh360.com/sales/inquiry/detail/${item.referenceNo}`
        );
        break;
      case 'SO':
        window.open(
          `/orderSale/formal/detail/${item.referenceNo}?soNo=${item.referenceNo}`
        );
        break;
      case 'BOSS':
      case 'BOSS_OCR':
        window.open(`/insteadOrder/maintainmentV3?id=${item.id}`);
        break;
    }
  }
};

const handleFocus = () => {
  // 如果存在客户物料号，则基于客户物料号匹配关联的震坤行sku
  if (
    props.customerMaterialNo &&
    (!currentSelectSku.value || JSON.stringify(currentSelectSku.value) === '{}')
  ) {
    searchSkuList(props.customerMaterialNo, props.customerMaterialNo);
  }
};

defineExpose({
  select,
});
</script>

<template>
  <el-select
    ref="select"
    v-model="currentSelectSku"
    class="search"
    popper-class="scroll-option"
    filterable
    clearable
    remote
    reserve-keyword
    value-key="skuNo"
    placeholder="请输入商品编号/名称"
    :remote-method="searchSkuList"
    :loading="isLoading"
    :disabled="disabled"
    @change="addSkuToTable"
    @focus="handleFocus"
  >
    <el-option
      v-for="(item, index) in skuList"
      :key="`${index}_${item.skuNo}`"
      :label="item.skuNo"
      :value="item"
      :disabled="index === 0"
    >
      <div
        class="ba-row-start selectSkuItem"
        :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
      >
        <div>{{ item.skuNo }}</div>
        <div>{{ `${item.materialDescribe || ''}` }}</div>
        <div>{{ `${item.customerSkuNo || ''}` }}</div>
        <div>{{ `${item.customerSkuName || ''}` }}</div>
        <div>{{ `${item.customerSkuUnitCount || ''}` }}</div>
        <div>{{ `${item.customerSkuUnit || ''}` }}</div>
        <div>{{ `${item.customerSkuSpecification || ''}` }}</div>
        <el-button text @click="toDataSource(item)">{{
          `${item.dataSource || ''}`
        }}</el-button>
      </div>
    </el-option>
  </el-select>
</template>

<style lang="scss" scoped>
.search {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
    flex-shrink: 0;
  }
  div:nth-child(2) {
    width: 400px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    flex-shrink: 0;
  }
  div:nth-child(3),
  div:nth-child(4),
  div:nth-child(5) {
    width: 90px;
    flex-shrink: 0;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  div:nth-child(6) {
    width: 120px;
  }
  div:nth-child(7) {
    width: 90px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>

<style lang="scss">
.scroll-option.el-select-dropdown {
  max-width: 1200px;

  .el-select-dropdown__item {
    width: fit-content !important;
  }
}
</style>
