<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElMessage, type UploadFiles } from 'element-plus';
import { saveDocumentMetaData } from '@/api/order';
import UploadFileComponent from './UploadFiles.vue';

const props = defineProps({
  uploadDialogVisible: { required: true, type: Boolean },
  businessId: { required: true, type: String },
});

const emit = defineEmits(['update:uploadDialogVisible']);
const showDlg = computed({
  get: () => props.uploadDialogVisible,
  set: (val) => emit('update:uploadDialogVisible', val),
});

const closeDialog = () => {
  showDlg.value = false;
  uploadList.value = [];
};

const uploadLoading = ref(false);

const uploadList = ref<UploadFiles>([]);

const submitUpload = () => {
  if (uploadList.value.length === 0) {
    return ElMessage.error('上传文件不能为空！');
  }
  // if (uploadList.value.some((file) => file.status !== 'success')) {
  //   return ElMessage.error('请等待文件上传完成！');
  // }
  uploadLoading.value = true;
  const docMetaDataList = uploadList.value.map((upload: any) => ({
    bucketName: upload.bucketName,
    fileName: upload.fileName,
    ossKey: upload.ossKey,
    upUserName: upload.upUserName,
    attachmentType: upload.attachmentType,
    uploadTime: upload.uploadTime,
  }));
  const queryData = {
    source: 'BOSS',
    dimension: 'sketch',
    businessId: props.businessId,
    docMetaDataList,
    docUploadScene: 'existSketch',
  };
  saveDocumentMetaData(queryData)
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success('上传成功');
        closeDialog();
      } else {
        ElMessage.error(res.msg || '上传失败');
      }
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
</script>

<template>
  <el-dialog
    v-model="showDlg"
    title="上传附件"
    :before-close="closeDialog"
    destroy-on-close
    :show-close="false"
    width="600px"
  >
    <div
      v-loading="uploadLoading"
      class="dialog-body"
      style="display: flex; align-items: center; flex-direction: column"
    >
      <span style="margin: 10px; color: #597bee">
        可将文件直接拖拽到该区域，或者点击上传按钮
      </span>
      <UploadFileComponent v-model="uploadList" attachment-type="general" />
    </div>
    <template #footer>
      <div class="flex justify-center">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitUpload">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>
