<script lang="ts" setup>
import { ref } from 'vue';

const props = defineProps({
  fold: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['fold']);
const foldStatus = ref(props.fold);
const handleToggle = () => {
  foldStatus.value = !foldStatus.value;
  emit('fold', foldStatus.value);
};
</script>

<template>
  <div class="color-#597bee cursor-pointer m-r-10px" @click="handleToggle()">
    查看更多
    <el-icon v-if="foldStatus"><ArrowDown /></el-icon>
    <el-icon v-else><ArrowUp /></el-icon>
  </div>
</template>
