<!--
 * @Author: luozhikai
 * @Date: 2024-12-19 14:21:29
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-05-12 10:32:14
 * @Description: 附件上传组件
-->
<script setup lang="ts">
import {
  ElMessage,
  type UploadFile,
  type UploadFiles,
  type UploadInstance,
  type UploadProps,
  type UploadRawFile,
  dayjs,
} from 'element-plus';
import { computed, ref } from 'vue';
import { useCommonStore } from '@/stores/common';
import { getFileUrl } from '@/api/order';

interface FileResponse {
  name: string;
  objectKey: string;
  bucketName: string;
}

const props = defineProps<{
  modelValue: any[];
  disabled?: boolean;
  attachmentType?: string;
  text?: string;
  accept?: string;
  change?: () => void;
  limit?: number;
  multiple?: boolean;
}>();

const emit = defineEmits(['update:modelValue']);

const commonStore = useCommonStore();
const acceptFileType = computed(() => commonStore.acceptFileType || {});
const uploadRef = ref<UploadInstance>();
const omsAppName = computed(() => {
  return /boss-uat|local/.test(window.location.href)
    ? 'ecorp-uat'
    : 'ecorp-pro';
});

const attachmentList = computed({
  get: () => {
    return (props.modelValue || []).map((file: any) => ({
      ...file,
      name: file.name || file.fileName,
    }));
  },
  set: (val) => {
    props.change && props.change();
    emit('update:modelValue', val);
  },
});

const handleUpload = (modelValue: (UploadFile & Record<string, any>)[]) => {
  if (modelValue.every((file) => file.status === 'success')) {
    const list = modelValue.map((file) => {
      const res = file.response as FileResponse[];
      return {
        // ...file,
        fileName: file.name || file.fileName || (res && res[0] && res[0].name),
        ossKey: file.ossKey || (res && res[0] && res[0].objectKey),
        bucketName: file.bucketName || (res && res[0] && res[0].bucketName),
        printDirection: 'cross',
        upUserName: commonStore.userName,
        attachmentType: props.attachmentType || '',
        uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };
    });
    attachmentList.value = list;
  }
};

const handleUploadSuccess: UploadProps['onSuccess'] = (
  _res,
  _file,
  modelValue
): void => {
  console.log(_res, _file, modelValue);
  handleUpload(modelValue);
};

const handleUploadRemove: UploadProps['onRemove'] = (
  _file: UploadFile,
  modelValue: UploadFiles
) => {
  handleUpload(modelValue);
};

const handleUploadError: UploadProps['onError'] = (err: Error) => {
  ElMessage.error(err.message || '上传失败');
};
const handleUploadExceed: UploadProps['onExceed'] = () => {
  ElMessage.error(`文件最多上传${props.limit || 5}个！`);
};

const getFileType = (file: UploadRawFile) => {
  const type = file.name.split('.').pop();
  return (type || '').toLowerCase();
};
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  const accept = props.accept || acceptFileType.value.soCommonType;
  const type = getFileType(file);
  if (!accept.includes(type)) {
    ElMessage.error('文件类型不支持！');
    return false;
  }
  if (attachmentList.value.some((item: any) => item.name === file.name)) {
    ElMessage.error({
      message: '文件已存在',
    });
    return false;
  }
  const size = file.size / 1024 / 1024;
  const isGtLimit = size > 10;
  let msg = '';
  let pass = true;
  if (isGtLimit) {
    pass = false;
    msg += `【${file.name}】大小：${size}M，上传文件不能超过10MB！<br/>`;
  }
  if (!pass) {
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: msg,
    });
  }
  return pass;
};

const onPreview: UploadProps['onPreview'] = async (file: any) => {
  let url = '';
  if (file.url) {
    url = file.url;
  } else {
    // 调接口获取url，然后再跳转
    const ossKey = file?.ossKey || file?.response[0]?.objectKey;
    if (ossKey) {
      const res = await getFileUrl(ossKey);
      console.log('preview', res);
      url = res.data;
    }
  }

  if (url) {
    try {
      window.open(url.replace(/http(s)?/, 'https'));
    } catch (error) {
      console.log(error);
    }
  } else ElMessage.error('无法获取文件url，预览失败');
};
</script>

<template>
  <el-upload
    ref="uploadRef"
    action="/ali-upload"
    class="inline-block w-100%"
    :class="{ 'disabled-upload': props.disabled }"
    :show-file-list="true"
    :multiple="props.multiple || true"
    drag
    :with-credentials="true"
    :disabled="props.disabled || false"
    :limit="props.limit || 5"
    :data="{ appName: omsAppName }"
    :on-success="handleUploadSuccess"
    :on-remove="handleUploadRemove"
    :on-error="handleUploadError"
    :on-exceed="handleUploadExceed"
    :before-upload="handleBeforeUpload"
    :on-preview="onPreview"
    :file-list="attachmentList"
    :accept="props.accept || ''"
    list-type="text"
  >
    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
    <div class="el-upload__text">
      <span>{{ props.text || '请上传附件' }}</span>
      ,
      <em>点击或拖拽上传</em>
    </div>
  </el-upload>
</template>

<style lang="scss">
.disabled-upload {
  .el-upload-dragger {
    border-color: var(--el-disabled-border-color) !important;
    cursor: not-allowed;
    &:hover {
      border-color: var(--el-disabled-border-color);
    }
    .el-upload__text {
      color: var(--el-text-color-disabled);
    }
    em {
      color: var(--el-text-color-disabled);
    }
  }
}
</style>
