<!--
 * @Author: l<PERSON>zhikai
 * @Date: 2024-12-18 13:58:41
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-12-23 11:33:29
 * @Description: 查询客户
-->
<script setup lang="ts">
import axios from 'axios';
import { debounce } from 'lodash';
import { type PropType, computed, ref } from 'vue';
import type { Customer } from './type';

const props = defineProps({
  modelValue: {
    type: Object as PropType<Customer>,
    default: () => {},
  },
  disabled: {
    type: Boolean,
    default: () => false,
  },
  multiple: {
    type: Boolean,
    default: () => false,
  },
  style: {
    type: String,
    default: () => 'width: 100%',
  },
  placeholder: { type: String, default: () => '请输入客户编码/名称' },
  valueProp: { type: String, default: () => 'value' },
  checkSalesForce: { type: Boolean, default: () => false }, // 是否只查询当前销售名下的客户
});

const emits = defineEmits(['update:modelValue', 'change']);

const customerList = ref<Customer[]>([]);

const loading = ref(false);

const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits('update:modelValue', value);
  },
});

// 查全量客户（默认）
const remoteMethod = debounce(async (query: string) => {
  if (query) {
    loading.value = true;
    try {
      const res = await axios.get(
        `/api-opc/v1/so/template/customer/like?value=${query}&checkSalesForce=${props.checkSalesForce}`
      );
      if (res?.data.code === 200) {
        customerList.value = [
          {
            customerNumber: '客户编码',
            customerName: '客户名称',
            cityName: '城市',
            canChoose: false,
          },
          ...res.data.data,
        ];
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
}, 800);

const handleChange = (value: Customer) => {
  emits('change', value);
};
</script>

<template>
  <el-select
    v-model="value"
    filterable
    clearable
    remote
    reserve-keyword
    :style="style"
    :placeholder="placeholder"
    value-key="customerNumber"
    :remote-method="remoteMethod"
    :loading="loading"
    @change="(val: Customer) => handleChange(val)"
  >
    <el-option
      v-for="(item, index) in customerList"
      :key="item.customerId"
      :label="item.customerName"
      :value="item"
      :disabled="item.canChoose === false"
    >
      <div class="flex" :class="index === 0 ? 'font-bold' : 'font-normal'">
        <div class="w-120px">{{ item.customerNumber }}</div>
        <div class="w-120px">{{ item.cityName }}</div>
        <div class="w-300px overflow-hidden text-ellipsis">
          {{ item.customerName }}
        </div>
      </div>
    </el-option>
  </el-select>
</template>
