<script setup lang="ts">
import { ref } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
import type { ListItem } from '@/constants';

const props = defineProps({
  tableList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['change']);

const visible = ref(false);

const handleClick = (row: Partial<ListItem>) => {
  emit('change', row);
  visible.value = false;
};

const onClickOutside = () => {
  visible.value = false;
};
</script>

<template>
  <el-popover
    :visible="visible"
    trigger="click"
    placement="bottom"
    width="550px"
  >
    <el-table :data="props.tableList" @row-click="handleClick">
      <el-table-column type="index" label="序号" width="50px" />
      <el-table-column label="客户物料号" prop="customerMaterialNo" />
      <el-table-column
        label="客户规格型号"
        prop="customerMaterialSpecification"
        width="100px"
      />
      <el-table-column
        label="客户物料名称"
        prop="customerMaterialName"
        width="100px"
      />
      <el-table-column
        label="客户物料数量"
        prop="customerMaterialStandardQuantity"
        width="100px"
      />
      <el-table-column
        label="客户物料单位"
        prop="customerMaterialUnit"
        width="100px"
      />
    </el-table>
    <template #reference>
      <span
        v-click-outside="onClickOutside"
        class="custom-tag"
        @click="visible = !visible"
      >
        多
      </span>
    </template>
  </el-popover>
</template>

<style scoped>
.custom-tag {
  position: absolute;
  top: -6px;
  right: -9px;
  width: 15px;
  height: 15px;
  font-size: 12px;
  border-radius: 50%;
  background-color: #ca0b0b;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
</style>
