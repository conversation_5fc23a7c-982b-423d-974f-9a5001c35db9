<script setup lang="ts">
import { computed } from 'vue';

type KeyProp =
  | 'customerMaterialNo'
  | 'customerMaterialName'
  | 'customerSpecificationModel'
  | 'rate'
  | 'customerMaterialUnit';
interface TableData {
  key: keyof KeyProp;
  result: string;
  input: string;
}
const props = defineProps({
  showDialog: { type: Boolean, default: false },
  resultMaterialObj: { type: Object, default: () => {} },
  inputMaterialObj: { type: Object, default: () => {} },
});

const emit = defineEmits(['update:showDialog', 'submit']);
const dlgVisible = computed({
  get: () => props.showDialog,
  set: (val: boolean) => emit('update:showDialog', val),
});

const tableData = computed(() => {
  const keyObj: any = {
    customerMaterialNo: '客户物料号',
    customerMaterialName: '客户物料名称',
    customerSpecificationModel: '客户规格型号',
    customerMaterialUnit: '客户物料单位',
  };
  const res: TableData[] = [];
  if (props.resultMaterialObj && props.inputMaterialObj) {
    const keys = Object.keys(keyObj);
    for (const key of keys) {
      const result = props.resultMaterialObj[key] || '';
      const input = props.inputMaterialObj[key] || '';
      const item: TableData = {
        key: keyObj[key as keyof KeyProp],
        result,
        input,
      };
      res.push(item);
    }
  }
  return res;
});

const tryTrim = (str: string) => {
  return (str && str.trim && str.trim()) || str;
};
</script>

<template>
  <el-dialog
    v-model="dlgVisible"
    title="客户物料关系信息确认"
    width="600px"
    append-to-body
    center
  >
    <p class="m-b font-size">
      当前选中sku及物料关系的客户物料信息与页面输入不一致，是否选择覆盖？
    </p>
    <el-table
      :data="tableData"
      border
      highlight-current-row
      style="width: 100%"
      class="m-b"
    >
      <el-table-column prop="key" label="字段" width="200" align="center" />
      <el-table-column
        prop="result"
        label="客户物料关系"
        width="200"
        align="center"
      >
        <template #default="{ row }">
          <span :class="{ red: tryTrim(row.result) !== tryTrim(row.input) }">{{
            row.result
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="input" label="页面输入" align="center" />
    </el-table>
    <div class="red">
      <p>
        覆盖：若选择覆盖，则系统会根据选择sku物料关系信息替换当前页面的客户物料信息；
      </p>
      <p>
        不覆盖：若选择不覆盖，则系统会保存当前页面的客户物料信息，并产生一条新的客户物料关系信息；
      </p>
      <p>取消：还没想好，返回重新确认选择sku</p>
    </div>
    <template #footer>
      <el-button
        type="primary"
        @click="emit('submit', 'yes', resultMaterialObj)"
        >覆盖</el-button
      >
      <el-button type="primary" @click="emit('submit', 'no', inputMaterialObj)"
        >不覆盖</el-button
      >
      <el-button type="default" @click="emit('submit', 'cancel')"
        >取消</el-button
      >
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.red {
  color: #ca0b0b;
}

.m-b {
  margin-bottom: 20px;
}

.font-size {
  font-size: 15px;
}
</style>
