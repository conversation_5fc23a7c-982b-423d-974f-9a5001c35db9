<script lang="ts" setup>
import { ref } from 'vue';
import { debounce } from 'lodash';
import { ElMessage } from 'element-plus';
import * as createOrder from '@/api/order';
import { initFieldsShow, isPro } from '@/utils/index';
import { getButtonAuth } from '@/utils/auth';
import type { SearchItemType } from '@/types/item';

const props = defineProps({
  hasUnderApproval: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
  required: { type: Boolean, default: false },
  customerCode: { type: String, default: '' },
  source: { type: String, default: '' },
});

const emits = defineEmits(['addSkuToTable', 'addNullSkuToTable']);

const currentSelectSku = ref<SearchItemType | null>(null);
const itemList = ref<SearchItemType[]>([]);
const loading = ref(false);
const searchKeyWord = ref('');

const handleSelectChange = (value: SearchItemType) => {
  console.log('handleSelectChange', value);
  currentSelectSku.value = value;
};

const searchSkuList = debounce(function (search) {
  searchKeyWord.value = search;
  loading.value = true;
  if (search) {
    const data = {
      vague: search,
      customerCode: props.customerCode,
    };
    createOrder.searchSkuListV2(data).then((result) => {
      if (result.code === 200) {
        if (Array.isArray(result.data) && result.data.length > 0) {
          itemList.value = result.data.map(
            (item: SearchItemType, index: number) => {
              return {
                index: index + 1,
                ...item,
              };
            }
          );
          itemList.value.unshift({
            index: 0,
            skuNo: '商品编号',
            materialDescribe: '商品描述',
            customerSkuNo: '客户物料号',
            customerSkuName: '客户物料名称',
            customerSkuUnitCount: '客户物料数量',
            customerSkuUnit: '客户物料数量单位',
            customerSkuSpecification: '客户物料规格型号',
            dataSource: '',
          });
        } else {
          itemList.value = [];
        }
      } else {
        itemList.value = [];
        ElMessage.error({
          message: result.msg,
        });
      }
      loading.value = false;
    });
  }
}, 1000);

const addSkuToTable = () => {
  emits('addSkuToTable', currentSelectSku.value, searchKeyWord.value);
};
const addNullSkuToTable = () => {
  emits('addNullSkuToTable');
};

const toDataSource = (item: SearchItemType) => {
  if (item.dataSource) {
    switch (item.dataSource) {
      case 'QTS':
        window.open(
          `https://${isPro() ? 'qts' : 'qts-uat'}.zkh360.com/sales/inquiry/detail/${item.referenceNo}`
        );
        break;
      case 'SO':
        window.open(
          `/orderSale/formal/detail/${item.referenceNo}?soNo=${item.referenceNo}`
        );
        break;
      case 'BOSS':
      case 'BOSS_OCR':
        window.open(`/insteadOrder/maintainmentV3?id=${item.id}`);
        break;
    }
  }
};

const optionKeys = [
  'skuNo',
  'materialDescribe',
  'customerSkuNo',
  'customerSkuName',
  'customerSkuUnitCount',
  'customerSkuUnit',
  'customerSkuSpecification',
];
type TooltipKeys = (typeof optionKeys)[number];

const showTooltip = ref<Record<TooltipKeys, boolean>>({
  skuNo: false,
  materialDescribe: false,
  customerSkuNo: false,
  customerSkuName: false,
  customerSkuUnitCount: false,
  customerSkuUnit: false,
  customerSkuSpecification: false,
});

const handleMouseEnter = (e: any, type: TooltipKeys) => {
  showTooltip.value[type] = e.target.scrollWidth <= e.target.offsetWidth;
};

const sourceMap: Record<string, string> = {
  draft: '草稿',
  create: '创建',
  edit: '修改',
};
const showButton = (prop?: string, buttonName?: string) => {
  const sourceName = sourceMap[props.source];
  return (
    initFieldsShow('skuInfo', prop)?.visible &&
    getButtonAuth('销售跟单', `${sourceName}_${buttonName}`)
  );
};
</script>

<template>
  <el-form-item class="search-row" :required="props.required" label="选择商品">
    <el-select
      :model-value="currentSelectSku as SearchItemType"
      class="search-input"
      popper-class="scroll-option"
      filterable
      clearable
      remote
      reserve-keyword
      value-key="index"
      placeholder="请输入商品编号/名称"
      :remote-method="searchSkuList"
      :loading="loading"
      @change="handleSelectChange"
    >
      <el-option
        v-for="(item, index) in itemList"
        :key="index"
        :label="`【${item.skuNo}】${item.materialDescribe}`"
        :value="item"
        :disabled="index === 0"
      >
        <div
          class="ba-row-start selectSkuItem"
          :class="index === 0 ? 'font-bold' : 'font-normal'"
        >
          <template v-for="optionKey in optionKeys" :key="optionKey">
            <el-tooltip
              :disabled="showTooltip[optionKey]"
              effect="dark"
              :content="String((item as any)[optionKey])"
              placement="top-start"
            >
              <div @mouseenter.stop="(e) => handleMouseEnter(e, optionKey)">
                {{ `${(item as any)[optionKey] || ''}` }}
              </div>
            </el-tooltip>
          </template>
          <el-button type="primary" text @click="toDataSource(item)">
            {{ `${item.dataSource || ''}` }}
          </el-button>
        </div>
      </el-option>
    </el-select>
    <el-popover
      v-if="props.hasUnderApproval && showButton('addSkuToTable', '确认添加')"
      placement="top"
      title=""
      width="400"
      trigger="hover"
    >
      <span>存在正在审批中的审批单，不支持新增行！</span>
      <template #reference>
        <el-button type="default">确认添加</el-button>
      </template>
    </el-popover>
    <el-button
      v-else-if="showButton('addSkuToTable', '确认添加')"
      type="primary"
      :disabled="!currentSelectSku?.skuNo || props.disabled"
      @click="addSkuToTable"
    >
      确认添加
    </el-button>
    <el-button
      v-if="showButton('addNullSku', '新增空行')"
      type="primary"
      :disabled="props.disabled"
      @click="addNullSkuToTable"
      >新增空行</el-button
    >
  </el-form-item>
</template>

<style lang="scss">
.search-item {
  width: 130px;
  flex: none;
  .el-input {
    input {
      border: 0;
    }
  }
}

.search-input {
  flex: 1;
  margin-right: 10px;
}
</style>

<style lang="scss" scoped>
.search-row {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .discount-input {
    .el-input__inner {
      width: 240px;
    }
  }
}
.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  div:nth-child(3),
  div:nth-child(4),
  div:nth-child(5) {
    width: 90px;
    flex-shrink: 0;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  div:nth-child(6) {
    width: 120px;
  }
  div:nth-child(7) {
    width: 90px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.selectCustomerItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 90px;
  }
  div:nth-child(3) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>

<style lang="scss">
.scroll-option.el-select-dropdown {
  max-width: 1200px;

  .el-select-dropdown__item {
    width: fit-content !important;
  }
}
</style>
