export type SaleMap = {
  [prop: string]: string;
};
export type SaleOrgList = {
  salesOrganizationName: string;
  salesOrganization: string;
  distributionChannel: string;
  distributionChannelName: string;
  productGroup: string;
  productGroupName: string;
};
export type SaleOrgOptions = {
  data: SaleOrgList[] & { idx: string };
  key: string;
  value: string;
};

export interface Customer {
  customerId?: string;
  customerNumber?: string;
  customerName?: string;
  cityName?: string;
  saleMap?: SaleMap;
  saleOrgList?: Array<SaleOrgList>;
  vflag?: boolean;
  currency?: string;
  currencySymbol?: string;
  exchangeRate?: number;
  customerNo?: string;
  canChoose?: boolean; // 接口入参传了checkSalesForce，则返回canChoose，表示是否在当前销售名下
  unChooseReason?: string;
}
