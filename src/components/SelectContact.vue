<script setup lang="ts">
import { computed, ref } from 'vue';
import { contactHeader } from '@/constants';
import type { ContactType } from '@/types/common';

export type ContactData = Partial<Pick<ContactType, 'contactId'>>;

interface Props {
  title: string;
  modelValue: ContactData;
  disabled?: boolean;
  contactList: ContactType[];
  loading: boolean;
  remoteMethod: (name: string) => void;
  isAddress?: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits(['update:modelValue', 'change']);
const value = computed(() => props.modelValue?.contactId || '');

const changeContact = (val: string) => {
  const data = props.contactList.find((item) => item.contactId === val);
  emits('update:modelValue', data);
  emits('change', data);
};

const searchContact = (val: string) => {
  if (!val) return;
  props.remoteMethod(val);
};

const disabledTooltip = ref(true);
const handleMouseEnter = (e: any) => {
  disabledTooltip.value = e.target.scrollWidth <= e.target.offsetWidth;
};
</script>

<template>
  <el-select
    :model-value="value"
    :placeholder="`选择${title}`"
    filterable
    remote
    clearable
    class="w-full"
    :disabled="disabled"
    :remote-method="searchContact"
    :loading="loading"
    @change="changeContact"
  >
    <el-option
      v-show="contactList && contactList.length >= 20"
      :key="-1"
      class="color-#ccc"
      disabled
      :value="-1"
    >
      已展示部分联系人，其他联系人请输入字符进行查询
    </el-option>
    <el-option :key="-2" :value="-2" disabled>
      <div class="ba-row-start selectClientItem font-bold">
        <div>{{ contactHeader.contactName }}</div>
        <div>{{ contactHeader.contactId }}</div>
        <div>{{ contactHeader.contactPhone }}</div>
        <div>{{ contactHeader.address }}</div>
      </div>
    </el-option>
    <el-option
      v-for="item in contactList"
      :key="item.contactId"
      :label="isAddress ? item.address : item.contactName"
      :value="item.contactId"
    >
      <div class="ba-row-start selectClientItem">
        <div>{{ item.contactName }}</div>
        <div>{{ item.contactId }}</div>
        <div>{{ item.contactPhone || '--' }}</div>
        <el-tooltip
          :disabled="disabledTooltip"
          effect="dark"
          :content="item.address"
          placement="top-start"
        >
          <div @mouseenter.stop="handleMouseEnter">
            {{ item.address || '--' }}
          </div>
        </el-tooltip>
      </div>
    </el-option>
  </el-select>
</template>

<style scoped lang="scss">
.ba-row-start {
  display: flex;
  font-weight: normal;
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem div:nth-child(1),
.selectClientItem div:nth-child(2),
.selectClientItem div:nth-child(3) {
  width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selectClientItem div:nth-child(4) {
  width: 700px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
