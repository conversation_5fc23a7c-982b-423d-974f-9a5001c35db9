<script setup lang="ts">
import { computed, ref } from 'vue';
import { TransferKey } from 'element-plus';
import Transfer from '@kun-plus/transfer';
// import Transfer from './Transfer.vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';

const commonStore = useCommonStore();

interface Rules {
  operations: string[];
}
interface Option {
  key: number;
  code: string;
  typeCode: string;
  typeName: string;
  label: string;
  disabled?: boolean;
  checked?: boolean;
  tagType?: string;
  rules?: Partial<Rules>;
}

const props = defineProps({
  showDialog: { type: Boolean, default: false },
  pendingType: { type: String, default: '' },
  checkedNotAcceptDemandReasons: { type: Array, default: () => [] },
  checkedDnOrderPendingReasons: { type: Array, default: () => [] },
});

const emit = defineEmits(['update:showDialog', 'submit']);

const { dictList } = storeToRefs(commonStore);

const dlgVisible = computed({
  get: () => props.showDialog,
  set: (val: boolean) => emit('update:showDialog', val),
});

const generateData = () => {
  const data: Option[] = [];
  const fields = ['notAcceptDemandReason', 'dnOrderPendingReason'];
  // 订单挂起展示全量挂起原因
  fields.forEach((field) => {
    dictList.value[field].forEach((item, index) => {
      // 全量原因
      if (item.status !== 'stop') {
        data.push({
          key: item.id || index, // 唯一标识
          code: item.code,
          typeCode: item.listKey || '', // 挂起类型key
          typeName: item.listName || '', // 挂起类型name
          label: item.name, // 挂起具体原因
          tagType: field === 'notAcceptDemandReason' ? 'success' : '', // 挂起类型el-tag颜色
          checked: false,
          rules: item.rules ? JSON.parse(item.rules) : null,
        });
      }
    });
  });
  // 取消挂起展示 选中行已有&支持取消的原因
  if (props.pendingType === 'cancel') {
    const _data = data.filter((item) => {
      return (
        ((!item.rules || item.rules.operations?.includes('cancel')) &&
          item.typeCode === 'notAcceptDemandReason' &&
          props.checkedNotAcceptDemandReasons.includes(item.code)) ||
        (item.typeCode === 'dnOrderPendingReason' &&
          props.checkedDnOrderPendingReasons.includes(item.code))
      );
    });
    return _data;
  } else {
    const _data = data.filter(
      (item) => !item.rules || item.rules.operations?.includes('add')
    );
    return _data;
  }
};

const title = computed(() =>
  props.pendingType === 'cancel' ? '取消挂起' : '订单挂起'
);

const checkedKeys = ref<TransferKey[]>([]);
const data = ref<Option[]>(generateData());
const notAcceptDemandReasons = ref<string[]>([]);
const dnOrderPendingReasons = ref<string[]>([]);
const handleTransferChange = (keys: TransferKey[]) => {
  checkedKeys.value = keys;
};

const submit = () => {
  checkedKeys.value.forEach((key) => {
    const item = data.value.find((item) => item.key === key) as Option;
    switch (item.typeCode) {
      // 不纳入需求挂起
      case 'notAcceptDemandReason':
        notAcceptDemandReasons.value.push(item.code);
        break;
      // 交货挂起
      case 'dnOrderPendingReason':
        dnOrderPendingReasons.value.push(item.code);
        break;
    }
  });
  dlgVisible.value = false;
  emit('submit', notAcceptDemandReasons.value, dnOrderPendingReasons.value);
};
</script>

<template>
  <el-dialog v-model="dlgVisible" :title="title" width="900px" append-to-body>
    <Transfer
      :data="data"
      :titles="[
        '挂起类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;挂起原因',
        '挂起类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;挂起原因',
      ]"
      @change="handleTransferChange"
    />
    <template #footer>
      <div class="flex justify-center">
        <el-button type="default" @click="dlgVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.red {
  color: #ca0b0b;
}

.m-b {
  margin-bottom: 20px;
}

.font-size {
  font-size: 15px;
}
</style>
