import axios from 'axios';
import OSS from 'ali-oss';
import type { UploadRawFile } from 'element-plus/lib/components/upload';

const isPro = /boss.zkh360.com/.test(location.href);

const region = 'oss-cn-beijing';
const postfix = isPro ? '' : '-uat';
const bucket = `zkh360-boss${postfix}`;
const fileDomain = `https://files${postfix}.zkh360.com`;

// 阿里云oss client
let client: OSS | null = null;
export async function getClient() {
  try {
    if (client) {
      return client;
    }
    const result = await axios.get('/api-oss/sts');
    if (result && result.data) {
      const { AccessKeyId, AccessKeySecret, SecurityToken } =
        result.data as Record<string, string>;
      client = new OSS({
        region,
        bucket,
        accessKeyId: AccessKeyId,
        accessKeySecret: AccessKeySecret,
        stsToken: SecurityToken,
        refreshSTSToken: async () => {
          const refreshSTSResponse = await axios.get('/api-oss/sts');
          if (refreshSTSResponse && refreshSTSResponse.data) {
            const { AccessKeyId, AccessKeySecret, SecurityToken } =
              refreshSTSResponse.data as Record<string, string>;
            return {
              accessKeyId: AccessKeyId,
              accessKeySecret: AccessKeySecret,
              stsToken: SecurityToken,
            };
          }
          throw new Error('fail to get oss sts token!');
        },
      });
      return client;
    }
  } catch (error) {
    console.log('getClient error', error);
  }
  return null;
}
/**
 * @param {*} path 上传文件路径
 * @param {*} file 上传文件
 * @returns 上传成功后可预览路径
 */
export async function upload(path: string, file: UploadRawFile) {
  const arr = file.name.split('.');
  const len = arr.length;
  let url = '';
  if (len >= 2) {
    const postfix = arr[len - 1];
    arr.pop();
    const name = arr.join('');
    const fileName = `${name}_${file.uid}.${postfix}`;
    const client = await getClient();
    const headers = {
      // 指定该Object被下载时的名称
      'Content-Disposition': `filename=${encodeURIComponent(file.name)}`,
    };
    const response = await client?.put(`${path}/${fileName}`, file, {
      headers,
    });
    if (response?.url) {
      const urlPath = new URL(response.url);
      const path = urlPath.pathname;
      url = fileDomain + path;
      console.log('upload success', url);
    }
  }
  return {
    url,
  };
}
