/**
 * @Author: luozhi<PERSON>
 * @Date: 2024-04-03 14:11:40
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-04-03 14:11:42
 * @Description: file content
 */

import sensors from 'sa-sdk-javascript';
import Cookies from 'js-cookie';

const host = window.location.host;

// const isProd = host.startsWith('boss.') || host.startsWith('boss-pre.');

const isUat = host.startsWith('boss-uat') || host.startsWith('local.');

// const showLog = process.env.NODE_ENV !== 'production';
// console.log('showLog', showLog);

sensors.init({
  server_url: isUat
    ? 'https://tracking.zkh.com/sa?project=abroad'
    : 'https://tracking.zkh.com/sa?project=production',
  show_log: false,
  distinct_id: '',
  cross_subdomain: false,
  heatmap: {
    clickmap: 'default',
    scroll_notice_map: 'default',
    collect_tags: {
      div: {
        max_level: 3,
      },
      li: true,
      img: true,
      svg: true,
    },
  },
  is_track_single_page: true,
  use_client_time: true,
  send_type: 'beacon',
});

sensors.registerPage({
  business_role() {
    console.log('sensors business_role', Cookies.get('bizRoleCode'));
    return Cookies.get('bizRoleCode');
  },
});
sensors.quick('autoTrack');
sensors.use('PageLoad', {});
sensors.use('PageLeave', {});

const track = (name: string, data: any) => {
  sensors.track(name, data);
};

export { track };
// 导出埋点配置
// eslint-disable-next-line import/no-default-export
export default sensors;
