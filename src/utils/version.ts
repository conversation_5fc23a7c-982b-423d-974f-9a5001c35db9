import axios from 'axios';
(function () {
  setInterval(getHash, 600000);
  async function getHash() {
    if (/local/.test(location.href)) {
      return;
    }
    // 当前版本的 hash
    const curScript = document
      .querySelectorAll('head')[0]
      .querySelectorAll('script');
    let curHashObj = null;
    for (const item of curScript) {
      if (item.src && item.src.includes('assets/index')) {
        curHashObj = item;
        break;
      }
    }

    const curHashSrc = curHashObj?.src?.split('/');
    const curHash = curHashSrc?.at(-1);
    // 在 js 中请求首页地址不会更新页面
    const response = await axios.get(
      `${window.location.origin}${window.location.pathname}`
    );
    // 返回的是字符串，需要转换为 html
    const el = document.createElement('html');
    el.innerHTML = response.data;
    // 拿到 hash
    if (
      el.querySelectorAll('script') &&
      el.querySelectorAll('script').length > 0
    ) {
      const newScript = el.querySelectorAll('script');
      let newHashObj = null;
      for (const item of newScript) {
        if (item.src && item.src.includes('assets/index')) {
          newHashObj = item;
          break;
        }
      }
      const newHashSrc = newHashObj?.src?.split('/');
      const newHash = newHashSrc?.at(-1);
      console.log('newHash', curHash, newHash);
      if (newHash && curHash && newHash !== curHash) {
        alert('版本更新，请刷新页面！');
      }
    }
  }
})();
