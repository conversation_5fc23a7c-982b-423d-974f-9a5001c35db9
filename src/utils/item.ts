import { isSupportedAutoPosition } from '@/utils/orderType';
// import { TaxEnum } from '@/types/common';
import { getDeliveryWarehouse } from '@/api/order';
// import { DirectDeliverySupplierEnum } from '@/types/item';
import {
  CompanyType,
  DictList,
  OrderType,
  SalesDictType,
  TaxEnum,
} from '@/types/common';
import {
  CommonPosition,
  CommonPositionMap,
  DirectDeliverySupplierEnum,
  ItemAmountType,
  ItemType,
  SearchItemType,
} from '@/types/item';
import { type SaleOrgList, useCustomerStore } from '@/stores/customer';
import { track } from './sensors';
import { Z003Transform } from '.';
// import { formatPrice } from '.';

export const findLastLineNo = (currentSkuList: ItemType[]) => {
  let currentLineNo = 10;
  const itemLen = (currentSkuList || []).length;
  if (itemLen > 0) {
    const lastItem = currentSkuList[itemLen - 1];
    if (lastItem && lastItem.idx) {
      currentLineNo = Number(lastItem.idx) + 10;
    }
  }
  return currentLineNo;
};

// 注入工厂信息
export const getFactoryList = (
  selectedSalesRange: Pick<
    SaleOrgList,
    'salesOrganization' | 'distributionChannel'
  >,
  dictList: DictList
) => {
  if (selectedSalesRange) {
    const { distributionChannel, salesOrganization } = selectedSalesRange;
    if (distributionChannel && salesOrganization && dictList.factory) {
      const f = (dictList.factory as any).filter((item: any) => {
        return (
          item.parentCode === `${salesOrganization}-${distributionChannel}`
        );
      });
      return f;
    } else {
      return [];
    }
  }
  return [];
};

export function getDirectDeliverySupplierList(
  dictList: DictList,
  orderType: OrderType,
  salesOrganization: string
) {
  if (orderType && salesOrganization) {
    const code = `${orderType}_${salesOrganization}`;
    if (
      !dictList ||
      !Array.isArray(dictList.orderTypeOfDirectDeliverySupplierList)
    )
      return [];
    const dict = dictList.orderTypeOfDirectDeliverySupplierList.find(
      (item: SalesDictType) => {
        return item.parentCode === code;
      }
    );
    if (dict && dict.code) {
      return dictList.directDeliverySupplier.filter((item: SalesDictType) =>
        dict.code.split(',').includes(item.code)
      );
    }
  }
  return (
    dictList.directDeliverySupplier?.filter(
      (item: SalesDictType) => Number.parseInt(item.code, 10) < 2
    ) || []
  );
}

export const getDefaultDirectDeliverySupplier = (
  salesOrganization: string,
  orderType: string,
  dictList: DictList
) => {
  if (
    salesOrganization &&
    orderType &&
    dictList &&
    dictList.orderTypeOfDirectDeliverySupplierList
  ) {
    const code = `${orderType}_${salesOrganization}`;
    const dict = dictList.orderTypeOfDirectDeliverySupplierList.find(
      (item: SalesDictType) => {
        return item.parentCode === code;
      }
    );
    if (dict) {
      return DirectDeliverySupplierEnum.SYS;
    }
  }
  return DirectDeliverySupplierEnum.ZKH;
};

export const getPositionList = (
  item: ItemType,
  factoryCode: CompanyType,
  orderType: OrderType,
  positionListMap?: CommonPositionMap
) => {
  const isSupportedAuto = isSupportedAutoPosition(factoryCode, orderType);
  let positionList = positionListMap
    ? positionListMap[`${item.skuNo}_${item.factory}`] || []
    : [];
  positionList = positionList?.filter((item) => item.code !== '-1');
  // 供应商直发
  if (item?.directDeliverySupplier === '1') {
    positionList = positionList?.filter(
      (item) => item.code && (item.code as string).endsWith('04')
    );
  }
  // 系统自动判定-自动挑仓
  if (
    item &&
    item.directDeliverySupplier === '2' &&
    isSupportedAuto &&
    positionList?.[0]?.code !== '-1'
  ) {
    positionList?.unshift({
      code: '-1',
      name: '自动挑仓',
    });
  }
  return positionList;
};

export function setAggreementPrice(sku: ItemType) {
  const { customerPriceTax, customerPrice } = sku;
  if (customerPriceTax != null) {
    if (customerPriceTax === '0' || customerPriceTax === 0) {
      sku.freeTaxPrice = customerPrice;
      sku.taxPrice = customerPrice / (1 + sku.taxRate);
    } else {
      sku.taxPrice = customerPrice;
      sku.freeTaxPrice = customerPrice * (1 + sku.taxRate);
    }
  }
}

// function updateItemPrice(
//   item: ItemType,
//   exchangeRate: number,
//   currency: string
// ) {
//   if (!item._taxPrice) {
//     if (currency !== 'CNY') item._taxPrice = item.taxPrice * exchangeRate;
//     else item._taxPrice = item.taxPrice;
//   }
//   if (!item._freeTaxPrice) {
//     if (currency !== 'CNY')
//       item._freeTaxPrice = item.freeTaxPrice * exchangeRate;
//     else item._freeTaxPrice = item.freeTaxPrice;
//   }
//   console.log(item._taxPrice, item._freeTaxPrice, exchangeRate, currency);
//   if (currency !== 'CNY') {
//     item.taxPrice = (item._taxPrice as any) / exchangeRate;
//     item.freeTaxPrice = (item._freeTaxPrice as any) / exchangeRate;
//   }
//   // 税额
//   const taxAmount =
//     item.freeTaxPrice && !Number.isNaN(item.taxRate)
//       ? formatPrice(item.freeTaxPrice * item.quantity * item.taxRate)
//       : 0;
//   item.taxAmount = taxAmount;
// }

export const setPositionList = (result: Array<any>): CommonPositionMap => {
  const resultMap: CommonPositionMap = {};
  result?.forEach((item) => {
    if (item.sku && item.factory) {
      if (!resultMap[`${item.sku}_${item.factory}`]) {
        resultMap[`${item.sku}_${item.factory}`] = item.allPosition.map(
          (item: CommonPosition) => item
        );
      } else {
        resultMap[`${item.sku}_${item.factory}`].push(
          ...item.allPosition.map((item: CommonPosition) => item)
        );
      }
    }
  });
  return resultMap;
};

export const getPositionByItem = async (records: Array<ItemType>) => {
  const dataSet: any = {
    skuSet: [],
    factorySet: [],
    positionScope: 1,
  };
  for (const record of records) {
    const { skuNo, factory } = record;
    if (!dataSet.skuSet.includes(skuNo)) {
      dataSet.skuSet.push(skuNo);
    }
    if (!dataSet.factorySet.includes(factory)) {
      dataSet.factorySet.push(factory);
    }
  }
  try {
    const res = await getDeliveryWarehouse(dataSet);
    if (res?.status === 200 && res?.result?.length) {
      return setPositionList(res.result);
    }
  } catch {
    return null;
  }
};

export const updateSkuByChangeFactory = (
  item: ItemType,
  _dictList: DictList,
  _orderType: OrderType,
  currency: string,
  taxType: string,
  exchangeRate = 1,
  isTax: TaxEnum
) => {
  const customerStore = useCustomerStore();

  if (item.factory) {
    // item.getPositionList = () => getPositionList(item, dictList, item.factory, orderType, positionListMap);
    item.position =
      item.position || (item.directDeliverySupplier === '2' ? '-1' : '');
    const { salesOrganization } = customerStore.cusDetail?.saleOrgVO || {};
    const facCode = item.ifSalePriceChannelLimit
      ? Number(Z003Transform(salesOrganization))
      : Number(item.factory);
    if (facCode) {
      if (
        item.factoryProductPriceVOMap &&
        item.factoryProductPriceVOMap[facCode]
      ) {
        let { suggestPrice, taxRate, taxRateInPoint } =
          item.factoryProductPriceVOMap[facCode];
        if (!suggestPrice) {
          suggestPrice = 0;
        }
        item._suggestPrice = suggestPrice;
        item.suggestPrice =
          currency === 'CNY' ? suggestPrice : suggestPrice / exchangeRate;
        if (taxRate != null && taxRateInPoint != null) {
          item.taxRate = taxType === '0' ? 0 : taxRateInPoint;
        }
      } else {
        // throw new Error(
        //   '该商品在所选择的销售范围下未配置价格或税率，请选择其他商品或在商品中心维护该商品的销售配置'
        // );
      }
      if (item.taxPrice != null && item.freeTaxPrice != null) {
        // updateItemPrice(item, exchangeRate, currency);
      } else if (item.taxRate !== null) {
        const price = item.price;
        // 从excel导入会包含price，isTax=1为含税价格，isTax=0为未税价格
        if (price) {
          item.taxPrice = isTax === '0' ? price * (1 + item.taxRate) : price;
          item.freeTaxPrice =
            isTax === '0' ? price : price / (1 + item.taxRate);
        } else {
          item.taxPrice = 0;
          item.freeTaxPrice = 0;
          setAggreementPrice(item);
        }
        // updateItemPrice(item, exchangeRate, currency);
      }
    }
  }
};

export function formatSelectV2Options(
  options: any,
  keyProp: string = 'name',
  valueProp: string = 'code'
) {
  if (!options || options.length === 0) return [];
  // if (!keyProp) keyProp = 'name';
  // if (!valueProp) valueProp = 'code';
  return options.map((item: any) => ({
    // eslint-disable-next-line eqeqeq
    label: (item[valueProp] != '-1' ? item[valueProp] : '') + item[keyProp],
    value: item[valueProp],
    ...item,
  }));
}

export function calSkuAmount(skuList: ItemType[], isTax: TaxEnum) {
  const result = skuList.reduce(
    (acc: ItemAmountType, sku: ItemType) => {
      const { taxRate, discountAmount, taxPrice, freeTaxPrice, quantity } = sku;
      const dp = Number.parseFloat(discountAmount) || 0;
      const newValue: ItemAmountType = {};
      const taxPriceRowTotal = taxPrice * quantity;
      const freeTaxPriceRowTotal = freeTaxPrice * quantity;
      const {
        taxedTotal = 0,
        taxedDiscountTotal = 0,
        unTaxedTotal = 0,
        unTaxedDiscountTotal = 0,
      } = acc;
      newValue.taxedTotal = taxPriceRowTotal + taxedTotal;
      newValue.unTaxedTotal = freeTaxPriceRowTotal + unTaxedTotal;
      if (isTax === TaxEnum.No) {
        newValue.unTaxedDiscountTotal =
          unTaxedDiscountTotal + freeTaxPriceRowTotal - dp;
        newValue.taxedDiscountTotal =
          taxedDiscountTotal + taxPriceRowTotal - dp * (1 + taxRate);
      }
      if (isTax === TaxEnum.Yes) {
        newValue.taxedDiscountTotal =
          taxedDiscountTotal + taxPriceRowTotal - dp;
        newValue.unTaxedDiscountTotal =
          unTaxedDiscountTotal + freeTaxPriceRowTotal - dp / (1 + taxRate);
      }
      return newValue;
    },
    {
      taxedTotal: 0,
      taxedDiscountTotal: 0,
      unTaxedTotal: 0,
      unTaxedDiscountTotal: 0,
    }
  );
  return result;
}

export function assign(
  skuList: ItemType[],
  totalDiscount: number,
  isTax: string,
  untaxedTotalAmount: number,
  taxedTotalAmount: number
) {
  const skus = [...skuList] || [];
  const sortSkus = skus.sort((a, b) => {
    const { quantity: q1, freeTaxPrice: f1, taxPrice: t1 } = a;
    const { quantity: q2, freeTaxPrice: f2, taxPrice: t2 } = b;
    let ret = 0;
    if (isTax === '0') {
      ret = q1 * f1 > q2 * f2 ? 1 : -1;
    }
    if (isTax === '1') {
      ret = q1 * t1 > q2 * t2 ? 1 : -1;
    }
    return ret;
  });
  let totalAmount = 0;
  const len = sortSkus.length;
  for (let i = 0; i < len - 1; i++) {
    const { quantity, freeTaxPrice, taxPrice } = sortSkus[i];
    let rowDiscount = 0;
    if (isTax === '0') {
      rowDiscount =
        (totalDiscount * quantity * freeTaxPrice) / untaxedTotalAmount;
    }
    if (isTax === '1') {
      rowDiscount = (totalDiscount * quantity * taxPrice) / taxedTotalAmount;
    }
    const rd = Number.parseFloat(rowDiscount.toFixed(2));
    sortSkus[i].discountAmount = rd;
    totalAmount += rd;
  }
  sortSkus[len - 1].discountAmount = totalDiscount - totalAmount;
  return sortSkus;
}

export function getDisabledDate(
  time: Date,
  specifiedReceiptDayOfWeek: string[],
  receiptTimeCategory: boolean | string,
  holidaysList: string[],
  check: boolean
) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  // 今天及以前的日期禁用
  if (time.getTime() <= today.getTime()) return true;
  // Z002/Z014/叫料订单不卡控周期性发货
  if (check) {
    let specifiedDays = [];
    if (
      specifiedReceiptDayOfWeek?.includes('0') ||
      !specifiedReceiptDayOfWeek?.length
    ) {
      specifiedDays = ['01', '02', '03', '04', '05', '06', '07'];
    } else {
      specifiedDays = specifiedReceiptDayOfWeek;
    }
    // 勾选了周末与节假日均可收货，表示所有日期都可选
    if (receiptTimeCategory === 'X' || receiptTimeCategory === true) {
      specifiedDays = specifiedDays
        ?.filter((day) => day && day !== '0')
        .map((day) => Number.parseInt(day));
    } else {
      // 未勾选周末与节假日均可收货
      // 排除节假日
      const isHoliday = holidaysList.some((holiday) => {
        const _holiday = new Date(holiday);
        _holiday.setHours(0, 0, 0, 0);
        return _holiday.getTime() === time.getTime();
      });
      if (isHoliday) {
        return true;
      }
      // 排除周末
      specifiedDays = specifiedDays
        ?.filter((day) => day && !['0', '06', '07'].includes(day))
        .map((day) => Number.parseInt(day));
    }
    // 指定收货日
    if (specifiedDays) {
      // 禁用非指定收货日的日期
      if (time.getDay() !== 0 && !specifiedDays.includes(time.getDay())) {
        return true;
      }
      // 对于星期日，只有在指定的收货日期中才是可选的
      if (time.getDay() === 0 && !specifiedDays.includes(7)) {
        return true;
      }
    }
  }
  return false;
}

function tryTrim(str: any) {
  return (str && str.trim && str.trim()) || str;
}
// 比较客户物料关系的字段是否相同
export function isDiffCustomerRelation(obj1: ItemType, obj2: ItemType) {
  try {
    const keys1 = Object.keys(obj1);
    for (const key of keys1) {
      const value1 = obj1[key];
      const value2 = obj2[key];
      // 输入为空时不比较
      if (value2 && tryTrim(value1) !== tryTrim(value2)) {
        return true;
      }
    }
    return false;
  } catch (error) {
    console.log(error);
  }
}

// 添加商品埋点
export function sensorsSku(
  type: string,
  sku: SearchItemType & {
    searchKeyWord: string;
    orderSkuNo?: string;
    productImportSource?: string;
  }
) {
  const {
    skuNo,
    materialDescribe,
    customerSkuNo,
    customerSkuName,
    customerSkuUnitCount,
    customerSkuUnit,
    customerSkuSpecification,
    dataSource,
    matchField,
    searchKeyWord,
    orderSkuNo,
    productImportSource,
  } = sku;
  const sensorsData = {
    key_word: searchKeyWord,
    sku_no: skuNo,
    product_description: materialDescribe,
    customer_materiel_no: customerSkuNo,
    customer_materiel_name: customerSkuName,
    customer_materiel_quantity: customerSkuUnitCount,
    customer_materiel_quantity_unit: customerSkuUnit,
    customer_materiel_specifications_no: customerSkuSpecification,
    data_source: dataSource,
    match_route: matchField,
    product_import_source: productImportSource,
    order_sku_no: orderSkuNo || '',
  };
  track(type, sensorsData);
}
export function findNormalItemInDictList(
  dictList: SalesDictType[],
  code: string
) {
  return dictList?.find(
    (item) => item.status === 'normal' && item.code === code
  );
}
