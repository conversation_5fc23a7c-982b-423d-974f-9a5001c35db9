import { ElMessageBox } from 'element-plus';
import axios, { type AxiosRequestConfig } from 'axios';

const api = axios.create({
  baseURL: '/',
  withCredentials: true,
});

api.interceptors.request.use((config) => {
  return config;
});

api.interceptors.response.use(
  (response) => {
    if (response.status === 200 || response.status === 201) {
      return Promise.resolve(response);
    } else {
      return Promise.reject(response);
    }
  },
  (error) => {
    if (error) {
      const { response, request } = error;
      if (response?.status === 401) {
        ElMessageBox.alert('登录超时，请刷新重试！', '提示', {
          type: 'error',
        });
        const url = `/login/callback?stateUrl=${location?.pathname}`;
        window.location.replace(url);
      } else if (response?.status !== 200) {
        const message =
          response?.data?.message ||
          response?.status?.message ||
          `错误！请求地址：${request?.responseURL}`;
        ElMessageBox.alert(message, '提示', {
          type: 'error',
        });
      }
    }
    return Promise.reject(error);
  }
);

const request = function (...args: any[]) {
  return api(
    ...(args as [url: string, config?: AxiosRequestConfig<any> | undefined])
  )
    .then((res) => {
      if (
        (res?.status === 401 || res?.data?.code === 401) &&
        res?.request?.responseURL?.includes('/internal-api/user')
      ) {
        ElMessageBox.alert('登录超时，请刷新重试！', '提示', {
          type: 'error',
        });
        const url = `/login/callback?stateUrl=${location?.pathname}`;
        window.location.replace(url);
      } else if (
        res.status === 200 &&
        res.data !== null &&
        res.data !== undefined
      ) {
        return res.data;
      } else {
        return res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export default request;
