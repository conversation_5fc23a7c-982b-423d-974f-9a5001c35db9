import { useCommonStore } from '@/stores/common';
import { MenuItem } from '@/types/common';

const authCacheMap = new Map();

export function getButtonAuth(
  menuName: string,
  buttonName: string,
  authEnableKey = 'orderSaleEnable'
) {
  const commonStore = useCommonStore();
  const menu = commonStore.menuList || [];
  const authConfig = commonStore.buttonAuthConfig;
  // 关闭权限控制直接返回true
  if (authConfig[authEnableKey] === false) return true;
  const cacheKey = `${menuName}_${buttonName}`;
  if (authCacheMap.has(cacheKey)) return authCacheMap.get(cacheKey);
  if (!menuName || !menu || menu.length === 0) return false;

  // 菜单
  const menuFilter = filterFun({
    type: 'MENU',
    name: menuName,
  });
  const menuFound = findResource(menu, menuFilter);

  if (!menuFound || !menuFound.children || menuFound.children.length === 0)
    return;
  const buttonFilter = filterFun({
    type: 'BUTTON',
    name: buttonName,
  });
  const buttonFound = findResource(menuFound.children, buttonFilter);

  authCacheMap.set(cacheKey, !!buttonFound);
  if (buttonFound) return true;
}
function filterFun(condition: any) {
  return (item: any) =>
    item.type === condition.type &&
    (item.name === condition.name || item.link === condition.name);
}
function findResource(menu: MenuItem[], filter: any) {
  // eslint-disable-next-line unicorn/no-array-callback-reference
  let ret = menu.find(filter);
  if (!ret) {
    menu.forEach((subMenu) => {
      if (subMenu.children && !ret) {
        ret = findResource(subMenu.children, filter);
      }
    });
  }
  return ret;
}
