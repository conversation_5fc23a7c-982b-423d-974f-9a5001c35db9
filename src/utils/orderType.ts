import type { CompanyType } from '@/constants/index';
import type { OrderType } from '@/types/common';

export function isServiceOrder(type: string) {
  return type === 'Z005' || type === 'Z013';
}

export function isFreeOrder(type: string) {
  return type === 'Z006' || type === 'Z007';
}

export function isForecastOrder(type: string) {
  return type === 'Z002' || type === 'Z014';
}

export function isEnableDirectDeliverySupplier(orderType: string) {
  return (
    orderType === 'Z018' ||
    orderType === 'Z009' ||
    orderType === 'Z014' ||
    orderType === 'ZEV1' ||
    orderType === 'ZEV2' ||
    orderType === 'ZEV3' ||
    orderType === 'ZEV4'
  );
}

// 是否支持自动挑仓
export function isSupportedAutoPosition(factoryCode: CompanyType, orderType: OrderType) {
  return (
    (factoryCode === '1000' || factoryCode === '1300') &&
    !isForecastOrder(orderType) &&
    orderType !== 'Z014' &&
    orderType !== 'ZEV1' &&
    orderType !== 'ZEV2' &&
    orderType !== 'ZEV3' &&
    orderType !== 'ZEV4'
  );
}

export function isInnerOrderReason(orderReason: string) {
  return orderReason === '050';
}
