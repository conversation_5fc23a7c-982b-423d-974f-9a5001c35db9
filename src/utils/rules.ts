import { isEmpty } from 'lodash';

// el-form表单校验
export const checkSalesRange = (_rule: any, value: any, callback: any) => {
  if (isEmpty(value)) {
    callback(new Error('请选择销售范围'));
  } else {
    callback();
  }
};

// vxe-grid单元格校验
export const validNull = ({ cellValue }: any) => {
  if (cellValue === '' || cellValue === undefined || cellValue === null) {
    return Promise.reject();
  }
  return Promise.resolve();
};
export const validZero = ({ cellValue }: any) => {
  if (!cellValue || cellValue === 0) {
    return Promise.reject();
  }
  return Promise.resolve();
};
