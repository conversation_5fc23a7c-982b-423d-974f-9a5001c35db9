import { ElMessageBox } from 'element-plus';
import { DictList } from '@/types/common';
import { DetailItem, DetailOrder } from '@/types/detail';
import { PositionScope, SupplyPosition, SuppplyData } from '@/types/position';
import { CustomerData } from '@/types/customer';
import request from './request';
import { getDefaultDirectDeliverySupplier } from './item';
import { Z003Transform } from '.';
import type { UploadSkuInfoDetailVO } from '@/types/order';
import type {
  IDetailData,
  SearchItemType,
  WaveDeliveryDateResponse,
  WaveFillItem,
} from '@/types/item';
import type { EditItem } from '@/types/edit';

export function getFactoryList(
  salesOrganization: string,
  distributionChannel: string,
  dictList: DictList
) {
  if (distributionChannel && salesOrganization) {
    const f = dictList.factory.filter((item) => {
      return item.parentCode === `${salesOrganization}-${distributionChannel}`;
    });
    return f;
  }
  return [];
}

export function formatSKU(
  sku: SearchItemType,
  detail: UploadSkuInfoDetailVO,
  orderData: DetailOrder,
  dictList: DictList,
  customerData?: CustomerData
) {
  console.log(sku, detail, orderData, customerData);
  const {
    salesOrganization,
    distributionChannel,
    items,
    autoBatching,
    orderType,
  } = orderData;
  const { currency, refuseSystemDeliveryDate } = items.at(-1) as DetailItem;
  const {
    skuNo,
    materialDescribe,
    customerSkuNo,
    customerSkuName,
    customerSkuUnitCount,
    customerSkuUnit,
    customerSkuSpecification,
    skuUnitCount,
  } = sku;
  const {
    factoryProductPriceVOMap,
    // customerPriceTax,
    fillDefaultCustomProperty,
    customPropertyList,
    ifVpi,
    productGroup,
    ifSalePriceChannelLimit,
  } = detail;
  // const result = { ...detail } as unknown as UploadSkuInfoDetailVO;
  const result: EditItem = {
    soItemNo: '',
    quantity: 0,
    sapItemNo: '',
    taxPrice: 0,
    freeTaxPrice: 0,
    customPropertyList,
    currency,
    factory: '',
    ifVpi,
    productGroup,
    materiel: skuNo,
    addType: '1',
    validateItemNo: generateItemNo(),
    clearedQty: 0,
    ifSalePriceChannelLimit,
  };
  result.skuFactoryPriceMap = factoryProductPriceVOMap;
  result.sapMaterialName = materialDescribe;
  result.quantity = 0;
  result.freeTaxPrice = 0;
  result.taxPrice = 0;
  // result.leadTime = '';
  const directDeliverySupplier = getDefaultDirectDeliverySupplier(
    salesOrganization,
    orderType,
    dictList
  );
  result.directDeliverySupplier = directDeliverySupplier;
  result.position = directDeliverySupplier === '2' ? '-1' : '';
  // 客户物料关系相关字段赋值
  result.customerMaterialNo = customerSkuNo;
  result.customerMaterialName = customerSkuName;
  result.customerMaterialUnit = customerSkuUnit;
  result.customerSpecificationModel = customerSkuSpecification;
  result.customerSkuUnitCount = (customerSkuUnitCount || 0) as number;
  result.skuUnitCount = skuUnitCount || 0;
  // 整单时新增行 默认为 原整单是否接受标期标识
  if (autoBatching !== 'X') {
    result.refuseSystemDeliveryDate = refuseSystemDeliveryDate;
    // result.originRefuseSystemDeliveryDate = result.refuseSystemDeliveryDate;
    const lastCustomerDate = items.at(-1)?.customerDate;
    if (lastCustomerDate) {
      result.customerDate = lastCustomerDate;
    }
  }
  if (
    result &&
    Array.isArray(result.customPropertyList) &&
    fillDefaultCustomProperty !== 'Z'
  ) {
    result.customPropertyList = result.customPropertyList.map((property) => ({
      ...property,
      placeholder: property.customPropertyRemark,
      customPropertyRemark: '',
    }));
  }
  // 处理单位,商品行详情中使用
  if (detail) {
    const { unitCode, unitName } = detail;
    if (unitCode && unitName) {
      result.quantityUnit = unitCode;
      result.packageInfoList = [
        {
          skuNo,
          unitName,
          ruleDes: `1${unitName}/${unitName}`,
          conversion: 1,
          unit: unitCode,
        },
      ];
    }
  }
  if (currency) {
    result.currency = currency;
  }
  if (salesOrganization && salesOrganization.length >= 2) {
    const factoryList = getFactoryList(
      salesOrganization,
      distributionChannel,
      dictList
    );
    const factories = factoryList.filter((item) => {
      const { code } = item;
      if (code && code.length >= 2) {
        return (
          code[0] === salesOrganization[0] && code[1] === salesOrganization[1]
        );
      }
      return false;
    });
    console.log('------', factoryList, factories);
    if (factories && factories.length > 0) {
      for (const [i, factory] of factories.entries()) {
        console.log(i, factory);
        const { code } = factory;
        let c = Number.parseInt(code, 10);
        // 如果工邦邦，优先选择1000工厂
        if (c === 1300) {
          c = 1000;
        }
        // 建议销售价取值：ifSalePriceChannelLimit 为true时，使用销售组织匹配价格；否则使用工厂匹配价格
        const priceCode = ifSalePriceChannelLimit
          ? Number(Z003Transform(salesOrganization))
          : c;
        if (
          factoryProductPriceVOMap &&
          factoryProductPriceVOMap[priceCode] &&
          factoryProductPriceVOMap[priceCode].taxRateInPoint !== null
        ) {
          result.taxRate =
            customerData?.taxType === '0'
              ? 0
              : factoryProductPriceVOMap[priceCode].taxRateInPoint;
          result.suggestPrice =
            Number(factoryProductPriceVOMap[priceCode]?.suggestPrice) || 0;
        }
        const priceMap = factoryProductPriceVOMap[c];
        // 初始化工厂
        if (priceMap && priceMap.taxRateInPoint !== null) {
          // result.taxRate =
          //   customerData?.taxType === '0' ? 0 : priceMap.taxRateInPoint;
          // result.suggestPrice = Number(priceMap.suggestPrice) || 0;
          result.factory = c.toString();
          break;
        } else {
          const filterFactoryList = factoryList
            .filter((i) => {
              const codeNum = Number(i.code);
              return (
                factoryProductPriceVOMap &&
                factoryProductPriceVOMap[codeNum] &&
                factoryProductPriceVOMap[codeNum].taxRate != null
              );
            })
            .sort((i1, i2) => {
              const codeNum1 = Number(i1.code);
              const codeNum2 = Number(i2.code);
              return codeNum1 - codeNum2;
            });
          if (filterFactoryList && filterFactoryList.length > 0) {
            result.factory = filterFactoryList[0].code;
          }
        }
      }
    }
    // if (factory) {
    //   const { code } = factory;
    //   let c = Number.parseInt(code, 10);
    //   // 如果工邦邦，优先选择1000工厂
    //   if (c === 1300) {
    //     c = 1000;
    //   }
    //   const priceMap = factoryProductPriceVOMap[c];
    //   if (priceMap && priceMap.taxRateInPoint) {
    //     result.taxRate = isTax === '0' ? 0 : priceMap.taxRateInPoint;
    //     if (customerPriceTax != null) {
    //       if (customerPriceTax === '0') {
    //         result.freeTaxPrice = customerPrice;
    //         result.taxPrice = customerPrice / (1 + result.taxRate);
    //       } else {
    //         result.taxPrice = customerPrice;
    //         result.freeTaxPrice = customerPrice * (1 + result.taxRate);
    //       }
    //     }
    //   }
    //   result.factory = c.toString();
    // }
  }
  return result;
}

export function isNumber(val: string) {
  return /^([+-]?\d+)(\.\d+)?$/.test(val);
}

export function formatQuantityUnit(value: string, dictList: DictList) {
  if (value) {
    console.log(dictList.quantityUnit, value);
    const unit = dictList.quantityUnit.find((item) => item.code === value);
    if (unit && unit.name) {
      return unit.name;
    }
  }
  return value;
}

export const initGoodsList = (items: DetailItem[], dictList: DictList) => {
  if (!items) return;
  const newItems = items.map((item) => {
    if (
      item &&
      Array.isArray(item.customPropertyList) &&
      item?.fillDefaultCustomProperty !== 'Z'
    ) {
      item.customPropertyList = item.customPropertyList.map((property) => ({
        ...property,
        placeholder: property.customPropertyRemark,
        customPropertyRemark: property.customPropertyRemark || '',
      }));
    }
    return {
      ...item,
      quantityUnit: formatQuantityUnit(item.quantityUnit, dictList),
      itemPlanList: item.itemPlanDTOList
        ? item.itemPlanDTOList.map((i) => ({
            ...i,
            soItemPlanNo: i.soItemPlanNo,
          }))
        : [],
      clearedQty: item.clearedQty || 0,
      customerMaterialQuantity: item.customerMaterialQuantity
        ? Number.parseFloat(item.customerMaterialQuantity)
        : 0,
      leadTime: item.deliveryPosition
        ? item.deliveryReceiverLeadTime
        : item.transferLeadTime,
    } as unknown as EditItem;
  });
  return newItems;
};

export function getOrderMapItem(
  row: EditItem,
  data: DetailOrder
): DetailItem | undefined {
  if (row.soItemNo || row.sapItemNo) {
    const found = data.items.find((item) => {
      return item.soItemNo === row.soItemNo || item.sapItemNo === row.sapItemNo;
    });
    return found;
  }
  return undefined;
}

let initItemNo = 1000;
export function generateItemNo() {
  initItemNo++;
  return `800${initItemNo}`;
}

export const getSimPosition = async (
  factorySet: string[],
  skuSet: string[],
  positionScope: PositionScope
) => {
  // 震坤行发货 sim 库位
  const data = {
    skuSet,
    factorySet,
    positionScope,
  };
  const res = await request({
    url: '/oms-config/supply-network/query/common',
    method: 'POST',
    data,
  });
  const posList: SupplyPosition[] = [];
  if (res && res.status === 200 && Array.isArray(res.result)) {
    const codeMap: Record<string, boolean> = {};
    (res.result as SuppplyData[])
      .reduce((prev, next) => {
        prev.push(
          ...next.allPosition.map((item) => ({
            ...item,
            sku: next.sku,
            warehouseCode: next.warehouseCode,
          }))
        );
        return prev;
      }, [] as SupplyPosition[])
      .forEach((item: SupplyPosition) => {
        const key = `${item.factory}_${item.code}_${item.name}_${item.sku}`;
        if (!codeMap[key]) {
          codeMap[key] = true;
          posList.push(item);
        }
      });
  }
  return posList;
};

export const queryFinalLT = async (
  currentRow: EditItem,
  orderData: DetailOrder
) => {
  try {
    // 非直发才去查询物流LT
    if (currentRow.directDeliverySupplier === '0') {
      const positionWarehouseCode =
        (
          (currentRow.simPositionList || []).find(
            (item) => item.code === currentRow.position
          ) || {}
        )?.warehouseCode || '';
      const { receiverProvince, receiverCity, receiverDistrict } =
        orderData || {};
      const _data = {
        address: {
          province: receiverProvince,
          city: receiverCity,
          district: receiverDistrict,
        },
        skuInfos: [{ qty: currentRow.quantity, sku: currentRow.materiel }],
        wmsIds: [currentRow.deliveryWarehouseCode || positionWarehouseCode],
      };
      const res = await request({
        url: '/api-opc/v1/so/queryFinalLT',
        method: 'POST',
        data: _data,
      });
      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        currentRow.leadTime = res.data[0].transportAgingTime;
      }
    }
  } catch (error) {
    console.log(error);
  }
};

export function updateDeliveryData(data: {
  autoBatching: string;
  sourceList: IDetailData[];
  targetList: WaveDeliveryDateResponse[];
  itemList: EditItem[];
}) {
  const { autoBatching, sourceList, targetList, itemList } = data;
  let errorMsg = '<div style="max-height: 300px;overflow: auto">';
  let hasError = false;
  const fillList: WaveFillItem[] = [];
  let newSourceList = sourceList;
  targetList?.forEach((result: WaveDeliveryDateResponse) => {
    const sourceItem = newSourceList.find(
      (source: IDetailData) =>
        source.sku === result.material && source.qty === result.quantity
    );
    newSourceList = newSourceList.filter(
      (item: IDetailData) => item !== sourceItem
    );
    const { waveDeliveryDate, promoteInfo, skuArrivalDate } = result;
    const index = itemList.findIndex(
      (item: EditItem) => item.validateItemNo === sourceItem?.validateItemNo
    );
    if (!waveDeliveryDate && promoteInfo) {
      hasError = true;
      errorMsg += `第${
        index + 1
      }行SKU【${sourceItem?.sku}】查询交期失败：${promoteInfo}<br />`;
    }
    fillList.push({ ...result, waveDeliveryDate, index, skuArrivalDate });
  });
  errorMsg += '</div>';
  // autoBatching 不勾选就是整单
  if (autoBatching === 'Z') {
    try {
      const max: WaveFillItem = fillList
        .filter((x) => x.waveDeliveryDate)
        .sort(
          (x, y) =>
            new Date(y.waveDeliveryDate).getTime() -
            new Date(x.waveDeliveryDate).getTime()
        )[0];
      fillList.forEach((item) => {
        item.waveDeliveryDate = max.waveDeliveryDate;
      });
    } catch (error) {
      console.log(error);
    }
  }
  fillList.forEach(({ waveDeliveryDate, index, originSkuArrivalDate }) => {
    itemList[index].deliveryDate = waveDeliveryDate;
    itemList[index].originSkuArrivalDate = originSkuArrivalDate;
  });
  if (hasError) {
    ElMessageBox.alert(errorMsg, '操作提示', {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      type: 'warning',
    });
  }
}
