/**
 * @Author: luozhi<PERSON>
 * @Date: 2024-09-10 10:44:17
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-09-10 10:44:19
 * @Description: file content
 */
import { ElMessage } from 'element-plus';

function clipboardSuccess() {
  ElMessage.success('复制成功');
}

function clipboardError() {
  ElMessage.error('复制失败');
}

export default function handleClipboard(text: string) {
  if (navigator.clipboard) {
    // clipboard api 复制
    navigator.clipboard.writeText(text);
  } else {
    const textarea = document.createElement('textarea');
    document.body.appendChild(textarea);
    // 隐藏此输入框
    textarea.style.position = 'fixed';
    textarea.style.clip = 'rect(0 0 0 0)';
    textarea.style.top = '10px';
    // 赋值
    textarea.value = text;
    // 选中
    textarea.select();
    try {
      // 复制
      document.execCommand('copy', true);
    } catch (ex) {
      console.log(ex);
      clipboardError();
      return;
    } finally {
      // 移除输入框
      document.body.removeChild(textarea);
    }
  }
  clipboardSuccess();
}
