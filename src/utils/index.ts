import { ElMessageBox } from 'element-plus';
import _ from 'lodash';
import { foldFields, orderServiceFields } from '@/utils/orderService';
import { generate } from '@/utils/uuid';
import { boolProps as deliveryBoolProps } from '@/stores/delivery';
import { boolProps as invoiceBoolProps } from '@/stores/invoice';
import { useItemStore } from '@/stores/item';
import { useCommonStore } from '@/stores/common';
import { OrderFieldsSettings } from '@/types/common';
import * as validators from '@/utils/rules';
import type { ItemType } from '@/types/item';

/**
 * 获取最大公倍数
 * @param a number
 * @param b number
 * @returns number
 */
export function max_gy(a: number, b: number): number {
  // eslint-disable-next-line no-constant-condition
  if (!Number.isInteger(a) || !Number.isInteger(b)) return 1;
  const yu = a % b;
  // eslint-disable-next-line eqeqeq
  if (yu == 0) {
    return b;
  }
  a = b;
  b = yu;
  return max_gy(a, b);
}

export function getQueryObject(url: string): Record<string, string> {
  if (!url) {
    url = window.location.href;
  }
  const index = url.lastIndexOf('?');
  if (index < 0) {
    return {};
  }
  const search = url.slice(Math.max(0, index + 1));
  const obj: Record<string, string> = {};
  const reg = /([^&=?]+)=([^&=?]*)/g;
  search.replaceAll(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

export function Z003Transform(companyCode: string) {
  let ret = companyCode;
  try {
    ret = `${String(companyCode || '').slice(0, 2)}00`;
  } catch (error) {
    console.log(error);
  }
  return ret;
}

export function formatPrice(value = 0, fractionDigits = 6) {
  const num = Number(value);
  const roundNum =
    Math.round((num + Number.EPSILON) * 10 ** (fractionDigits + 1)) /
    10 ** (fractionDigits + 1);
  const formattedNum = roundNum.toFixed(fractionDigits);
  // 去除末尾的零和不必要的小数点
  return formattedNum.replace(/(\.\d*?)0+$/, '$1').replace(/\.$/, '');
}

export function isObjectNull(value: any) {
  return !value || JSON.stringify(value) === '{}';
}

export function formatStore(data: any): any {
  const contacts = [
    'receiverContact',
    'orderContact',
    'receivingInvoiceContact',
  ];
  for (const prop in data) {
    if (
      contacts.includes(prop) &&
      data[prop] &&
      typeof data[prop] === 'object'
    ) {
      data[prop] = data[prop].contactId;
    }
  }
  return data;
}

function trimPropertyList(item: any) {
  if (item && Array.isArray(item.customPropertyList)) {
    item.customPropertyList.forEach((property: any) => {
      if (property.placeholder !== undefined) {
        delete property.placeholder;
      }
    });
  }
}
const itemsBooleanProps = [
  'urgent',
  'customerDateSensitive',
  'needScrapingCode',
  'refuseSystemDeliveryDate',
];
function formatSubmitBoolean(data: any): void {
  for (const prop in data) {
    if (
      deliveryBoolProps.includes(prop) ||
      invoiceBoolProps.includes(prop) ||
      itemsBooleanProps.includes(prop)
    ) {
      if (data[prop]) {
        data[prop] = 'X';
      } else {
        data[prop] = 'Z';
      }
    }
  }

  data.items.forEach((item: any) => {
    for (const prop in item) {
      if (itemsBooleanProps.includes(prop)) {
        if (item[prop]) {
          item[prop] = 'X';
        } else {
          item[prop] = 'Z';
        }
      }
    }
  });
  if (Array.isArray(data.deliveryRequirements)) {
    data.deliveryRequirements = data.deliveryRequirements.join(',');
  }
  if (Array.isArray(data.packagingReq)) {
    data.packagingReq = data.packagingReq.join(',');
  }
}

export async function formatSubmitData(source: any) {
  const {
    customer,
    cusDetail,
    contactData,
    customerDateSensitive,
    orderSource,
    subOrderSource,
    selectedSalesRange,
    receiverContact,
    orderContact,
    receivingInvoiceContact,
    // receivingInvoicePhone,
    autoBatching,
    customerReferenceDate,
    entireOrderRefuseSDD,
    delivery,
    invoice,
    customerReferenceNo,
    orderType,
    skuList,
    costCenterList,
    isDraft,
    isCreate,
    bossID,
    orderDataInfo,
  } = source;
  const {
    orderNote,
    paymentTerm,
    serviceCenterSelfTransport,
    acceptSupplierDelivery,
    customerServiceId,
    sellerId,
    customerServiceName,
    sellerName,
    customerServiceEmail,
    sellerEmail,
    isTax,
    paymentTermCode,
    customerSource,
    customerSourceName,
    salesOffice,
    salesGroup,
    businessPartnerGroupName,
    customerClassificationName,
    customerNatureName,
    taxType,
    sellerMap,
    customerServiceMap,
    vflag,
  } = cusDetail || {};
  console.log(invoice);
  const {
    customerName,
    customerNumber,
    currency,
    exchangeRate,
    currencySymbol,
  } = customer || {};
  const { distributionChannel, productGroup, salesOrganization } =
    selectedSalesRange || {};
  const { bidCustomer } = delivery || {};
  const receiverContactId = (receiverContact || {}).contactId;
  const invoiceContactId = (receivingInvoiceContact || {}).contactId;
  const orderContactId = (orderContact || {}).contactId;
  const receivingInvoicePhone = (invoice || {}).invoicePhone;
  const itemStore = useItemStore();

  const items: ItemType[] = [];
  const validateUnitArr: any[] = [];

  skuList.forEach((item: ItemType) => {
    const {
      skuNo,
      taxPrice,
      freeTaxPrice,
      customerMaterialUnit,
      costCenter,
      materialDescribe,
      idx,
      sketchOrderItemNo,
      customPropertyList,
      notAcceptDemandReasons,
      dnOrderPendingReasons,
      itemEditable,
      customerMaterialQuantity,
    } = item;
    if (
      (!itemEditable || itemEditable === '1') &&
      customerMaterialUnit &&
      customerMaterialUnit.length > 10
    ) {
      validateUnitArr.push({ sketchOrderItemNo, skuNo });
    }
    const newItem: ItemType = {
      ..._.cloneDeep(item),
      currency,
      orderItemNo: idx,
      materiel: skuNo,
      sapMaterialName: materialDescribe,
      taxPrice: taxPrice == null ? 0 : taxPrice,
      freeTaxPrice: freeTaxPrice == null ? 0 : freeTaxPrice,
      conditionalPricingUnit: 1,
      customerMaterialQuantity: customerMaterialQuantity || 0,
      notAcceptDemandReasons: (notAcceptDemandReasons || []).join(','),
      dnOrderPendingReasons: (dnOrderPendingReasons || []).join(','),
    };
    if (Array.isArray(customPropertyList)) {
      newItem.customPropertyList = customPropertyList.map((property: any) => {
        delete property.placeholder;
        return property;
      });
    }
    if (orderType === 'Z007') {
      newItem.costCenterDesc = costCenterList.find(
        (item: any) => item.costCenter === costCenter
      )?.description;
    }
    if (!autoBatching && customerReferenceDate) {
      newItem.customerDate = customerReferenceDate;
    }
    if (!autoBatching && entireOrderRefuseSDD) {
      newItem.refuseSystemDeliveryDate = 'X';
    }
    newItem.waveDeliveryDate = newItem.deliveryDate || '';
    delete newItem.positionList;
    delete newItem.relateSkuList;
    if (bidCustomer === 'X' || bidCustomer === '8') {
      newItem.customerDate = newItem.recentDeliveryDate;
    }
    const selectedItemList = itemStore.selectedItemList.map((item) => item.idx);
    if (selectedItemList.includes(newItem.idx)) {
      newItem.selectedToOperate = true;
    } else {
      newItem.selectedToOperate = false;
    }
    newItem.customerDate = newItem.customerDate || '*';
    items.push(newItem);
  });
  // 展示客户叫料的时候才校验
  const showBidCustomer = initFieldsShow(
    'deliveryInfo',
    'bidCustomer'
  )?.visible;
  if (
    showBidCustomer &&
    (bidCustomer === 'X' || bidCustomer === '8') &&
    items
      .filter((item) => !item.itemEditable || item.itemEditable === '1')
      .some(
        (sku) =>
          !sku.recentDeliveryDate ||
          !sku.deliveryFrequency ||
          !sku.deliveryCycle
      )
  ) {
    ElMessageBox.alert(
      '未发货条件为 客户叫料，商品行 最近发货日期、发货频次，发货周期必填！',
      '操作提示',
      {
        type: 'error',
        confirmButtonText: '确定',
      }
    );
    return;
  }
  if (validateUnitArr && validateUnitArr.length > 0) {
    const msg = validateUnitArr
      .map(
        ({ sketchOrderItemNo, skuNo }) => `行${sketchOrderItemNo}-sku为${skuNo}`
      )
      .join(',');
    ElMessageBox.alert(`【${msg}】的客户物料单位大于10位`, '错误', {
      type: 'error',
      confirmButtonText: '确认',
    });
    return;
  }
  const orderServiceFieldsList = orderServiceFields.map((obj) => obj.field);
  const deliveryStore = foldFields(
    _.cloneDeep(delivery),
    orderServiceFieldsList.concat('packagingReq')
  );
  const invoiceStore = formatStore(_.cloneDeep(invoice));
  const contactStore = formatStore(_.cloneDeep(contactData));

  const orderData = _.cloneDeep({
    ...orderDataInfo,
    orderNo: orderDataInfo.orderNo || bossID,
    items,
    customerName,
    customerNo: customerNumber,
    currency,
    exchangeRate,
    currencySymbol,
    customerDateSensitive,
    isTax,
    customerSource,
    customerSourceName,
    salesOffice,
    salesGroup,
    businessPartnerGroupName,
    customerClassificationName,
    customerNatureName,
    taxType,
    sellerMap,
    customerServiceMap,
    vflag,
    customerServiceId,
    customerServiceName,
    customerServiceEmail,
    sellerId,
    sellerName,
    sellerEmail,
    orderNote,
    distributionChannel,
    productGroup,
    salesOrganization,
    customerReferenceDate,
    receivingInvoiceContact: invoiceContactId,
    receiverContact: receiverContactId,
    receivingInvoicePhone,
    orderContact: orderContactId,
    orderSource,
    subOrderSource,
    customerReferenceNo: customerReferenceNo
      ? customerReferenceNo.trim().replaceAll(/\r\n|\r|\n/g, '')
      : '',
    orderType,
    paymentTerm: paymentTerm || paymentTermCode,
    serviceCenterSelfTransport,
    acceptSupplierDelivery,
    requestId: generate(),
    ...deliveryStore,
    ...invoiceStore,
    ...contactStore,
    ...itemStore.totalAmount,
  });

  // 特殊处理
  orderData.disableShipping = orderData.disableShipping || '*';
  orderData.designatedShipping = orderData.designatedShipping || '*';

  orderData.items.forEach((element: ItemType) => {
    trimPropertyList(element);
  });
  delete orderData.receiverContactList;
  delete orderData.receivingInvoiceContactList;
  delete orderData.orderContactList;
  let hasConflictFields = false;
  let conflictMessage = '交货信息中存在冲突字段，订单创建后将自动修正：<br />';
  if (
    orderData.hideLogo &&
    (orderData.dnSignatureReq?.indexOf('01') > -1 ||
      orderData.dnSignatureReq?.indexOf('05') > -1)
  ) {
    conflictMessage +=
      '因隐藏logo=是，故修正送货单签章要求不等于盖红章或每页盖章，紧固件特殊包装要求等于无要求；<br />';
    hasConflictFields = true;
  }
  if (
    !orderData.receiptTimeCategory &&
    (orderData.specifiedReceiptDayOfWeek?.indexOf('06') > -1 ||
      orderData.specifiedReceiptDayOfWeek?.indexOf('07') > -1)
  ) {
    conflictMessage +=
      '因工作日和周末均可收=否，故取消指定收货日期=周六/周日选项；';
    hasConflictFields = true;
  }
  const commonStore = useCommonStore();
  if (
    commonStore.isShowCombinedDelivery &&
    orderData.combinedDelivery === '4' &&
    orderData.autoDelivery
  ) {
    conflictMessage +=
      '合单发货=手动指定合单时，自动发货需等于否，系统已自动修正；';
    hasConflictFields = true;
  }
  if (!isDraft && hasConflictFields) {
    await ElMessageBox.confirm(conflictMessage, {
      dangerouslyUseHTMLString: true,
      showCancelButton: false,
      showClose: false,
      closeOnClickModal: false,
    });
  }
  if (isCreate && isDraft) {
    // 创建草稿
    orderData.sketchOnly = 'X';
  }
  const { updateWay } = orderDataInfo || {};
  if (updateWay) {
    orderData.updateWay = updateWay;
  }
  if (!isDraft) {
    // 创建订单
    orderData.sketchTag = '01';
  }
  formatSubmitBoolean(orderData);
  return orderData;
}

export function handleDraftResponse(
  res: any,
  isDraftDetail: boolean,
  orderData: any
): void {
  if (res) {
    const { code } = res;
    let { msgList, sketchOrderNo } = res.data || [];
    if (code === 200) {
      if (Array.isArray(msgList)) msgList = msgList.join(' ');
      const str = `<div style="max-height:400px;max-width:350px;overflow:auto;white-space: break-spaces;">${msgList}</div>`;
      ElMessageBox.alert(str, '操作提示', {
        type: 'success',
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        callback: () => {
          try {
            if (isDraftDetail) {
              window.location.reload();
            } else {
              let url = `/sr/draft/list?voucherNoList=${sketchOrderNo}`;
              if (orderData.orderBasis === 'STOCK_CLEARANCE') {
                url = `/sr/stockClearance/list?voucherNoList=${sketchOrderNo}`;
              }
              window.location.replace(url);
            }
          } catch (error) {
            console.log(error);
          }
        },
      });
    } else {
      let message = '';
      if (res.data && res.data.msgList) {
        if (Array.isArray(res.data.msgList)) {
          const arr = res.data.msgList.map((item: any) => {
            if (typeof item === 'string') {
              return item.split(';').filter((s) => s);
            }
            return item;
          });
          message = _.flatten(arr).join(';<br>');
        } else {
          message = res.data as string;
        }
      } else if (res.msg) {
        message = res.msg;
      }
      ElMessageBox.alert(message, '错误', {
        type: 'error',
        confirmButtonText: '确认',
        dangerouslyUseHTMLString: true,
      });
    }
  }
}
export function soRedirect(path: any, callback?: any) {
  const isLocal = /local/.test(window.location.href);
  const origin = isLocal
    ? 'http://local.zkh360.com:5173'
    : window.location.origin;
  location.href = origin + path;
  callback && callback();
}

export function handleCreateResponse(
  orderData: any,
  res: any,
  createWorkOrder: any
): void {
  if (res) {
    const { bossID, orderNo } = orderData;
    const { code, data, bizCode } = res;
    if (code === 200 || code === 201) {
      const str = `订单创建成功！对应的外围订单号为：${
        bossID ? bossID : orderNo
      }`;
      const msg: any = {
        200: str,
        201: data && (data as any).length > 0 ? `${str};` + `${data[0]}` : str,
      };
      ElMessageBox.alert(msg[code], '订单创建成功', {
        type: 'success',
        confirmButtonText: '确定',
        customStyle: { 'white-space': 'break-spaces' },
        dangerouslyUseHTMLString: true,
        callback: () => {
          try {
            soRedirect(
              `/sr/draft/list?voucherNoList=${orderNo}&orderCreateStatus=`
            );
          } catch (error) {
            console.log(error);
          }
        },
      });
    } else if (code !== 200 && bizCode) {
      createWorkOrder(orderData, res);
    } else {
      let message = '';
      if (res.data) {
        if (Array.isArray(res.data)) {
          const arr = res.data.map((item: any) => {
            if (typeof item === 'string') {
              return item.split(';').filter((s) => s);
            }
            return item;
          });
          message = _.flatten(arr).join(';<br>');
        } else {
          message = res.data as string;
        }
      } else if (res.msg) {
        message = res.msg;
      }
      ElMessageBox.alert(message, '错误', {
        type: 'error',
        confirmButtonText: '确认',
        dangerouslyUseHTMLString: true,
      });
    }
  }
}

// 通过字典获得当前请求url
export function getOrderOptUrl(
  dictList: any,
  orderType: string,
  optType: string
) {
  const orderOptUrls = dictList.orderOptUrl;
  if (orderOptUrls && orderOptUrls.length > 0) {
    const foundDict = orderOptUrls.find((dict: any) => {
      if (dict.parentCode) {
        const itemInfo = dict.parentCode.split('-');
        if (itemInfo && itemInfo.length > 0) {
          return itemInfo[0] === orderType && itemInfo[1] === optType;
        }
      }
      return false;
    });
    if (foundDict) {
      return foundDict.code;
    }
  }
  return '';
}

export function isNull(val: any) {
  if (val === '' || val === undefined || val === null) {
    return true;
  }
  return false;
}

// 选择无要求'0'时设置值只为0
export function setValueIfIncludesZero(value: string[]) {
  if (value?.includes('0')) {
    return ['0'];
  }
  return value;
}
export const isPro = () => {
  const host = window.location.host;
  return host.startsWith('boss.') || host.startsWith('boss-pre.');
};

function findFieldRecursive(
  fields: OrderFieldsSettings | OrderFieldsSettings[],
  endName: string
): any {
  if (!fields || !Array.isArray(fields)) {
    return undefined;
  }
  const res = fields.find((item) => item.prop === endName);
  if (res) {
    return res;
  }
  for (const field of fields) {
    if (field.children && Array.isArray(field.children)) {
      const found = findFieldRecursive(field.children, endName);
      if (found) {
        return found;
      }
    }
  }
  return undefined;
}

/**
 *
 * @param firstName 客户信息、交期要素等大模块prop
 * @param endName 具体字段或按钮prop
 * @returns field：获取具体字段配置
 */
const cacheMap: any = new Map();
export const initFieldsShow = (firstName: string, endName?: string) => {
  const commonStore = useCommonStore();
  const { orderFieldsSettings } = commonStore;
  const cacheKey = `${firstName}_${endName || ''}`;
  if (cacheMap.has(cacheKey)) return cacheMap.get(cacheKey);
  if (firstName && Object.keys(orderFieldsSettings).length > 0) {
    const fields = orderFieldsSettings[firstName] || {};
    if (endName && fields?.children && fields.children?.length > 0) {
      const field = findFieldRecursive(fields.children, endName);
      cacheMap.set(cacheKey, field);
      return field;
    }
    cacheMap.set(cacheKey, fields);
    return fields;
  }
  return {
    visible: true,
  };
};

// 配置化validator转成rules.ts里的函数
export const formatValidators = (rules: any) => {
  try {
    if (!rules) return;
    Object.entries(rules).forEach(([, ruleItem]) => {
      if (Array.isArray(ruleItem)) {
        ruleItem.forEach((rule) => {
          if (
            rule.validator &&
            validators[rule.validator as keyof typeof validators]
          ) {
            rule.validator =
              validators[rule.validator as keyof typeof validators];
          }
        });
      }
    });
    return rules;
  } catch (error) {
    console.log(error);
  }
};

export function toFixedByRadix(number: any, radix = 6, keepRadix = false) {
  try {
    if (typeof number === 'string') {
      number = Number(number);
    }
    const base = 10 ** radix;
    const accNumber = Math.round(number * base) / base;
    let ret = accNumber;
    if (keepRadix) {
      ret = Number(accNumber.toFixed(radix));
    }
    return ret;
  } catch (error) {
    console.log(error);
    return number;
  }
}
