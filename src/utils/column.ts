export interface IColumn {
  title?: string;
  type?: string;
  fixed?: string;
  field?: string;
  slot?: string;
  headerSlot?: string;
  width?: number;
  showOverflow?: boolean;
  take?: string;
  visible?: boolean;
  slots?: any;
}
const draftColumn: IColumn[] = [
  { title: '', type: 'checkbox', width: 50, fixed: 'left' },
  {
    title: '项目行',
    field: 'sketchOrderItemNo',
    slot: 'sketchOrderItemNo',
    width: 130,
    fixed: 'left',
    showOverflow: false,
  },
  { title: '正式订单号', field: 'soNo', slot: 'soNo', width: 210 },
  { title: '草稿单行状态', field: 'sketchOrderItemStatusName', width: 100 },
  {
    title: '报错事件',
    field: 'validateResult',
    slot: 'validateResult',
    width: 140,
    showOverflow: false,
  },
  { title: '商品编号', field: 'skuNo', slot: 'skuNo', width: 200 },
  { title: '商品描述', field: 'materialDescribe', width: 300 },
  {
    title: '工厂',
    field: 'factory',
    slot: 'factory',
    headerSlot: 'factoryHeader',
    width: 260,
  },
  {
    title: '数量',
    field: 'quantity',
    slot: 'quantity',
    headerSlot: 'quantityHeader',
    width: 160,
  },
  {
    title: '建议含税销售价',
    field: 'suggestPrice',
    slot: 'suggestPrice',
    width: 160,
  },
  {
    title: '含税单价',
    field: 'taxPrice',
    slot: 'taxPrice',
    headerSlot: 'taxPriceHeader',
    width: 160,
  },
  {
    title: '未税单价',
    field: 'freeTaxPrice',
    slot: 'freeTaxPrice',
    headerSlot: 'freeTaxPriceHeader',
    width: 160,
  },

  { title: '税率', field: 'taxRate', slot: 'taxRate', width: 70 },
  { title: '整行税额', field: 'taxAmount', width: 120 },
  {
    title: '选择直发',
    field: 'directDeliverySupplier',
    slots: {
      default: 'directDeliverySupplier',
      header: 'directDeliverySupplierHeader',
    },
    width: 140,
  },
  {
    title: '库位',
    field: 'position',
    slot: 'position',
    headerSlot: 'positionHeader',
    width: 270,
  },
  {
    title: '标准发货日期',
    field: 'sysDeliveryDate',
    slot: 'sysDeliveryDate',
    width: 145,
  },
  {
    title: '标准送达日期',
    field: 'skuArrivalDate',
    headerSlot: 'skuArrivalDateHeader',
    slot: 'skuArrivalDate',
    width: 145,
  },
  {
    title: '客户期望送达日期',
    field: 'customerDate',
    slot: 'customerDate',
    headerSlot: 'customerDateHeader',
    width: 145,
  },
  // {
  //   title: '是否接受标期',
  //   field: 'refuseSystemDeliveryDate',
  //   slot: 'refuseSystemDeliveryDate',
  //   headerSlot: 'refuseSystemDeliveryDateHeader',
  //   width: 150,
  // },
  {
    title: '成本中心',
    field: 'costCenter',
    slots: { default: 'costCenter', header: 'costCenterHeader' },
    width: 200,
  },
  {
    title: '总账科目',
    field: 'generalLedgerAccount',
    slots: {
      default: 'generalLedgerAccount',
      header: 'generalLedgerAccountHeader',
    },
    width: 200,
  },
  {
    title: '最近发货日期',
    field: 'recentDeliveryDate',
    slots: {
      default: 'recentDeliveryDate',
      header: 'recentDeliveryDateHeader',
    },
    width: 200,
  },
  {
    title: '发货频次',
    field: 'deliveryFrequency',
    slots: {
      default: 'deliveryFrequency',
      header: 'deliveryFrequencyHeader',
    },
    width: 200,
  },
  {
    title: '发货周期',
    field: 'deliveryCycle',
    slots: { default: 'deliveryCycle', header: 'deliveryCycleHeader' },
    width: 200,
  },
  {
    title: '协议价审批号',
    field: 'unifyApprovalNo',
    slot: 'unifyApprovalNo',
    width: 230,
  },
  {
    title: '协议价审批状态',
    field: 'salePriceApprovalStatusDesc',
    width: 120,
  },
  {
    title: '外围单号',
    field: 'orderNo',
    width: 120,
  },
  { title: '原始销售订单号', field: 'originSapOrderNo', width: 140 },
  { title: 'SAP退货单单号', field: 'referenceOrderNo', width: 140 },
  { title: '退货单行号', field: 'referenceOrderItemNo', width: 100 },
  {
    title: '操作',
    field: 'manage',
    slot: 'manage',
    width: 120,
    fixed: 'right',
  },
];
const createColumn: IColumn[] = [
  { title: '', type: 'checkbox', width: 50, fixed: 'left' },
  {
    title: '项目行',
    field: 'idx',
    slot: 'idx',
    width: 130,
    fixed: 'left',
    showOverflow: false,
  },
  { title: '商品编号', field: 'skuNo', slot: 'skuNo', width: 200 },
  { title: '商品描述', field: 'materialDescribe', width: 300 },
  {
    title: '工厂',
    field: 'factory',
    slot: 'factory',
    headerSlot: 'factoryHeader',
    width: 260,
  },
  {
    title: '数量',
    field: 'quantity',
    slot: 'quantity',
    headerSlot: 'quantityHeader',
    width: 160,
  },
  {
    title: '建议含税销售价',
    field: 'suggestPrice',
    slot: 'suggestPrice',
    width: 160,
  },
  {
    title: '含税单价',
    field: 'taxPrice',
    slot: 'taxPrice',
    headerSlot: 'taxPriceHeader',
    width: 160,
  },
  {
    title: '未税单价',
    field: 'freeTaxPrice',
    slot: 'freeTaxPrice',
    headerSlot: 'freeTaxPriceHeader',
    width: 160,
  },

  { title: '税率', field: 'taxRate', slot: 'taxRate', width: 70 },
  { title: '整行税额', field: 'taxAmount', width: 120 },
  {
    title: '选择直发',
    field: 'directDeliverySupplier',
    slots: {
      default: 'directDeliverySupplier',
      header: 'directDeliverySupplierHeader',
    },
    width: 140,
  },
  {
    title: '库位',
    field: 'position',
    slot: 'position',
    headerSlot: 'positionHeader',
    width: 270,
  },
  {
    title: '标准发货日期',
    field: 'sysDeliveryDate',
    slot: 'sysDeliveryDate',
    width: 145,
  },
  {
    title: '标准送达日期',
    field: 'skuArrivalDate',
    headerSlot: 'skuArrivalDateHeader',
    slot: 'skuArrivalDate',
    width: 145,
  },
  {
    title: '客户期望送达日期',
    field: 'customerDate',
    slot: 'customerDate',
    headerSlot: 'customerDateHeader',
    width: 145,
  },
  // {
  //   title: '是否接受标期',
  //   field: 'refuseSystemDeliveryDate',
  //   slot: 'refuseSystemDeliveryDate',
  //   headerSlot: 'refuseSystemDeliveryDateHeader',
  //   width: 150,
  // },
  {
    title: '成本中心',
    field: 'costCenter',
    slots: { default: 'costCenter', header: 'costCenterHeader' },
    width: 200,
  },
  {
    title: '总账科目',
    field: 'generalLedgerAccount',
    slots: {
      default: 'generalLedgerAccount',
      header: 'generalLedgerAccountHeader',
    },
    width: 200,
  },
  {
    title: '最近发货日期',
    field: 'recentDeliveryDate',
    slots: {
      default: 'recentDeliveryDate',
      header: 'recentDeliveryDateHeader',
    },
    width: 200,
  },
  {
    title: '发货频次',
    field: 'deliveryFrequency',
    slots: {
      default: 'deliveryFrequency',
      header: 'deliveryFrequencyHeader',
    },
    width: 200,
  },
  {
    title: '发货周期',
    field: 'deliveryCycle',
    slots: { default: 'deliveryCycle', header: 'deliveryCycleHeader' },
    width: 200,
  },
  { title: '原始销售订单号', field: 'originSapOrderNo', width: 140 },
  { title: 'SAP退货单单号', field: 'referenceOrderNo', width: 140 },
  { title: '退货单行号', field: 'referenceOrderItemNo', width: 100 },
  {
    title: '操作',
    field: 'manage',
    slot: 'manage',
    width: 120,
    fixed: 'right',
  },
];

function getColumns(columns: IColumn[]) {
  const htmlList: any[] = [];
  const formatColumns = [];
  for (const column of columns) {
    if (column.slot || column.headerSlot) {
      column.slots = {};
    }
    if (column.slot) {
      column.slots.default = column.slot;
      if (htmlList?.find((item) => item === column.slot)) {
        column.type = 'html';
        column.showOverflow = false;
      }
      delete column.slot;
    }
    if (column.headerSlot) {
      column.slots.header = column.headerSlot;
      delete column.headerSlot;
    }
    formatColumns.push(column);
  }
  console.log(formatColumns);
  return formatColumns;
}
export { getColumns, draftColumn, createColumn };
