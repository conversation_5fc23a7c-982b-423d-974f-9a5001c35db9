<script setup lang="ts">
import { RouterView, useRoute, useRouter } from 'vue-router';
import { Layout } from '@boss/layout2';
import { computed, onMounted, ref } from 'vue';
import { Refresh } from '@boss/refresh';
import { ElMessageBox } from 'element-plus';
import Cookies from 'js-cookie';
import { useCommonStore } from '@/stores/common';
import '@boss/layout2/style.css';
// import '@/utils/sensors';
import { track } from '@/utils/sensors';

const commonStore = useCommonStore();
const userName = computed(() => commonStore.userName || '');
const roleName = ref('');

const instance = ref<Refresh | null>(null);
const isShowLayout = ref(false);
const pageLoading = ref(true);

const initRoleName = () => {
  const data = Cookies.get('bizRoleName')?.replaceAll('"', '');
  if (data) {
    roleName.value = data;
  }
};

const alertFn = (
  msg: string,
  title: string,
  btnText: string,
  callback: () => void
) => {
  ElMessageBox.alert(msg, title, {
    confirmButtonText: btnText,
    callback: () => {
      callback();
    },
  });
};

const router = useRouter();
const route = useRoute();

const isShowDropdownTask = computed(
  () => route.meta.showDropdownTask
) as unknown as boolean;
const collapse = computed(() => route.meta.collapseMenu) as unknown as boolean;

onMounted(async () => {
  try {
    // await grayscaleOnline(); // 灰度上线函数
    console.log('web mounted');
    await router.isReady(); // 确保路由就绪
    await commonStore.getUserRole();
    isShowLayout.value = window.self === window.top;
    await Promise.all([
      commonStore.getTemplateExcelUrls(),
      commonStore.getAcceptFileType(),
      commonStore.getMenuList(),
      commonStore.getButtonAuthConfig(),
    ]);
    initRoleName();
    instance.value = Refresh.getInstance(alertFn);
  } catch (error) {
    console.log(error);
  } finally {
    pageLoading.value = false;
  }
});

// 灰度上线逻辑函数 2024年1月18日
// const grayscaleOnline = async () => {
//   await commonStore.getRoleList();
//   const isgray = commonStore.roleList.filter(
//     (item: any) => item.workbenchAvailable
//   );
//   if (isgray.length > 0 && window.self === window.top) {
//     isShowLayout.value = true;
//   } else {
//     isShowLayout.value = false;
//   }
// };

const handleRoleChange = () => {
  if (instance.value) {
    instance.value.refresh();
  }
};
</script>

<template>
  <div v-loading="pageLoading" class="min-h-100vh">
    <div v-if="!pageLoading" class="min-h-100vh">
      <Layout
        :is-show-layout="isShowLayout"
        :name="userName"
        title="工作台"
        :collapse="collapse"
        :show-task="isShowDropdownTask"
        :role="roleName"
        :on-role="handleRoleChange"
        :sensor-track="track"
      >
        <div class="w-full flex-1 overflow-y-auto router-container">
          <router-view />
        </div>
      </Layout>
      <!-- <div v-else class="w-full h-100vh overflow-y-auto">
        <router-view />
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.router-container {
  height: calc(100vh - 64px);
}
</style>
