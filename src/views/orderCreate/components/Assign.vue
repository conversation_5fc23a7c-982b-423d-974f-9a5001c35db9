<script lang="ts" setup>
import { computed, ref } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useItemStore } from '@/stores/item';
import { useOrderStore } from '@/stores/order';
import { getButtonAuth } from '@/utils/auth';

const itemStore = useItemStore();
const orderStore = useOrderStore();

const discount = ref(0);

const isDraftDetail = computed(() => orderStore.isDraftDetail);
const handleAssign = async () => {
  const res = await itemStore.assignDiscount({
    discount: discount.value,
  });
  if (res) {
    discount.value = 0;
  } else {
    ElMessage.error('分配整单折扣金额失败！');
  }
};
defineExpose({
  discount,
});
</script>

<template>
  <el-form-item
    v-if="
      getButtonAuth('销售跟单', `${isDraftDetail ? '草稿' : '创建'}_确认分配`)
    "
    class="search-row"
    label="整单折扣金额"
  >
    <el-input-number
      v-model="discount"
      class="discount-input w-240px m-r-10px"
      placeholder="整单折扣金额"
      :min="0"
      :max="Number.MAX_VALUE"
      :step="1"
      :precision="2"
    />
    <el-button
      plain
      type="primary"
      class="m-r-10px"
      :disabled="discount < 0"
      @click="handleAssign"
    >
      确认分配
    </el-button>
    <el-tooltip
      effect="dark"
      content="确认分配后，系统将自动把整单折扣金额按照商品行的金额占比进行分配到每一行的折扣金额上"
      placement="top"
    >
      <el-icon class="v-text-bottom"><InfoFilled /></el-icon>
    </el-tooltip>
  </el-form-item>
</template>

<style lang="scss" scoped>
.search-row {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
}
</style>
