<script lang="ts" setup>
import { computed } from 'vue';
import { isEmpty } from 'lodash';
import { ElMessage, ElMessageBox } from 'element-plus';
import { storeToRefs } from 'pinia';
// import { sensors } from '@/utils/index';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useItemStore } from '@/stores/item';
import bus from '@/utils/bus';
import AddSkuItem from '@/components/AddSkuItem.vue';
import { sensorsSku } from '@/utils/item';
import { initFieldsShow } from '@/utils';
import type { SearchItemType } from '@/types/item';

const customerStore = useCustomerStore();
const orderStore = useOrderStore();
const itemStore = useItemStore();

const orderDataInfo = computed(() => orderStore.orderData);
const { isHeaderDisabled, isDraftDetail } = storeToRefs(orderStore);
const customerNumber = computed(
  () => customerStore.customer.customerNumber || ''
);
const selectedSalesRange = computed(() => customerStore.selectedSalesRange);
const factoryList = computed(() => itemStore.factoryList);

const addSkuToTable = async (
  currentSelectSku: SearchItemType,
  searchKeyWord: string
) => {
  if (!isEmpty(selectedSalesRange.value)) {
    // const {
    //   skuNo,
    //   materialDescribe,
    //   customerSkuNo,
    //   customerSkuName,
    //   customerSkuUnitCount,
    //   customerSkuUnit,
    //   customerSkuSpecification,
    //   dataSource,
    //   matchField,
    // } = currentSelectSku;
    // const data = {
    //   key_word: searchKeyWord,
    //   sku_no: skuNo,
    //   product_description: materialDescribe,
    //   customer_materiel_no: customerSkuNo,
    //   customer_materiel_name: customerSkuName,
    //   customer_materiel_quantity: customerSkuUnitCount,
    //   customer_materiel_quantity_unit: customerSkuUnit,
    //   customer_materiel_specifications_no: customerSkuSpecification,
    //   data_source: dataSource,
    //   match_route: matchField,
    // };
    // sensors('SoCreateSelectProductConfirmButtonClick', data); // 点击确认添加埋点
    sensorsSku('SoCreateSelectProductConfirmButtonClick', {
      ...currentSelectSku,
      searchKeyWord,
    });
    try {
      orderStore.editStore({
        pageLoading: true,
      });
      console.log(currentSelectSku, console.log(factoryList.value));
      await itemStore.addGoodsFromRow({
        factoryList: factoryList.value,
        currentSelectSku,
      });
      bus.emit('updateCheckbox', {
        rows: itemStore.selectedItemList,
        checked: true,
      });
      await itemStore.getDeliveryDate();
      await customerStore.changeClientDetail();
    } catch (error) {
      if (error instanceof Error) {
        ElMessageBox.alert((error && error.message) || '操作失败！', '错误', {
          confirmButtonText: '确定',
          type: 'error',
        });
      }
    } finally {
      orderStore.editStore({
        pageLoading: false,
      });
    }
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};
const addNullSkuToTable = async () => {
  if (!isEmpty(selectedSalesRange.value)) {
    try {
      orderStore.editStore({
        pageLoading: true,
      });
      await itemStore.addNullGoods();
      itemStore.editStore({
        showSelectedItem: true,
        selectedIndex: itemStore.itemList.length - 1,
      });
      bus.emit('updateCheckbox', {
        rows: itemStore.selectedItemList,
        checked: true,
      });
      await customerStore.changeClientDetail();
    } catch (error) {
      if (error instanceof Error) {
        ElMessageBox.alert((error && error.message) || '操作失败！', '错误', {
          confirmButtonText: '确定',
          type: 'error',
        });
      }
    } finally {
      orderStore.editStore({
        pageLoading: false,
      });
    }
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};

const disabledField = (prop?: string) => {
  return initFieldsShow('skuInfo', prop)?.disabled;
};
</script>

<template>
  <AddSkuItem
    required
    :has-under-approval="orderDataInfo.hasUnderApproval"
    :disabled="isHeaderDisabled || disabledField('addItem')"
    :customer-code="customerNumber"
    :source="isDraftDetail ? 'draft' : 'create'"
    @add-sku-to-table="addSkuToTable"
    @add-null-sku-to-table="addNullSkuToTable"
  />
</template>

<style lang="scss" scoped></style>
