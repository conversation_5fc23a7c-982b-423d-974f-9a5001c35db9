<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { FormInstance, FormRules } from 'element-plus';
import { isEqual } from 'lodash';
import { storeToRefs } from 'pinia';
import { type SaleOrgList, useCustomerStore } from '@/stores/customer';
import { useCommonStore } from '@/stores/common';
import { useOrderStore } from '@/stores/order';
import { useItemStore } from '@/stores/item';
import { initFieldsShow } from '@/utils';

type validateFormValue = {
  customerReferenceNo: string;
  selectedSalesRange: SaleOrgList;
  customerServiceId: string;
  customerName: string;
  serviceCenterSelfTransport: string;
  acceptSupplierDelivery: string;
  paymentTerm: string;
  sellerId: string;
};

type State = {
  companyCode: string;
  validateFormValue: validateFormValue;
  rules: FormRules;
};

const props = defineProps({
  showCustomerEdit: { type: Boolean, default: false },
});

const emit = defineEmits(['updateCustomerEdit']);

const showDlg = computed({
  get: () => props.showCustomerEdit,
  set: (val) => {
    updateDialogStatus(val);
  },
});

const form = ref<FormInstance>();

const updateDialogStatus = (val: boolean) => {
  emit('updateCustomerEdit', val);
};

const customerStore = useCustomerStore();
const commonStore = useCommonStore();
const orderStore = useOrderStore();
const itemStore = useItemStore();

const { dictList } = storeToRefs(commonStore);
const { companyCode, isHeaderDisabled, isDraftDetail } =
  storeToRefs(orderStore);
const {
  selectedSalesRange: originalSalesRange,
  customerReferenceNo,
  customer,
  cusDetail: customerDetail,
  saleOrgList,
} = storeToRefs(customerStore) as any;

const vflag = computed(() => customerStore.cusDetail?.vflag);

const draftDetailSaleRange = computed(() => {
  const {
    salesOrganization,
    distributionChannel,
    productGroup,
    salesOrganizationName,
    distributionChannelName,
    productGroupName,
  } = customerDetail.value?.saleOrgVO || {};
  let ret = '';
  if (salesOrganization) ret += `${salesOrganization}/`;
  if (distributionChannel) ret += `${distributionChannel}/`;
  if (productGroup) ret += `${productGroup} `;
  if (salesOrganizationName) ret += `${salesOrganizationName} `;
  if (distributionChannelName) ret += `${distributionChannelName} `;
  if (productGroupName) ret += `${productGroupName} `;
  return ret;
});

const paymentTerms = computed(() => {
  return dictList.value.paymentTerms || [];
});

const salesGroupName = computed(() => {
  if (customerDetail.value && dictList.value) {
    const { salesGroup } = customerDetail.value;
    if (dictList.value.salesGroup) {
      const group = dictList.value.salesGroup.find(
        (item: any) => salesGroup === item.parentCode
      );
      if (group) {
        return group.name;
      }
    }
  }
  return '';
});

const salesOfficeName = computed(() => {
  if (customerDetail.value && dictList.value) {
    const { salesOffice } = customerDetail.value;
    if (dictList.value.salesOffice) {
      const office = dictList.value.salesOffice.find(
        (item: any) => salesOffice === item.parentCode
      );
      if (office) {
        return office.name;
      }
    }
  }
  return '';
});
const sellerList = computed(() => {
  if (customerDetail.value && customerDetail.value.sellerMap) {
    return Object.keys(customerDetail.value.sellerMap).map((k) => {
      return {
        code: k,
        name: customerDetail.value.sellerMap[k]
          ? customerDetail.value.sellerMap[k].userName
          : '',
      };
    });
  }
  return [];
});
const customerList = computed(() => {
  if (customerDetail.value && customerDetail.value.customerServiceMap) {
    return Object.keys(customerDetail.value.customerServiceMap).map((k) => {
      return {
        code: k,
        name: customerDetail.value.customerServiceMap[k]
          ? customerDetail.value.customerServiceMap[k].userName
          : '',
      };
    });
  }
  return [];
});

const state = reactive<State>({
  companyCode: companyCode.value && companyCode.value.toString(),
  validateFormValue: {
    customerReferenceNo: '',
    selectedSalesRange: {
      salesOrganization: '',
      productGroup: '',
      distributionChannel: '',
      salesOrganizationName: '',
      distributionChannelName: '',
      productGroupName: '',
    },
    customerServiceId: '',
    customerName: '',
    serviceCenterSelfTransport: '',
    acceptSupplierDelivery: '',
    paymentTerm: '',
    sellerId: '',
  },
  rules: {
    selectedSalesRange: [
      { required: true, message: '请选择销售范围', trigger: 'change' },
    ],
  },
});
onMounted(() => {
  state.validateFormValue = {
    customerReferenceNo: customerReferenceNo.value,
    selectedSalesRange: originalSalesRange.value,
    paymentTerm: customerDetail.value.paymentTerm,
    customerName: customer.value.customerName,
    serviceCenterSelfTransport: customerDetail.value.serviceCenterSelfTransport,
    acceptSupplierDelivery: customerDetail.value.acceptSupplierDelivery,
    sellerId: customerDetail.value.sellerId,
    customerServiceId: customerDetail.value.customerServiceId,
  };
  init(customerDetail.value);
});

watch(
  () => customerDetail.value,
  (now, prev) => {
    if (now !== prev) {
      init(now);
    }
  }
);
const init = (detail: any) => {
  let paymentTerm = detail.paymentTerm;
  let serviceCenterSelfTransport = detail.serviceCenterSelfTransport;
  let acceptSupplierDelivery = detail.acceptSupplierDelivery;
  if (!paymentTerm && detail) {
    paymentTerm = detail.paymentTerm;
  }
  if (!serviceCenterSelfTransport && detail) {
    serviceCenterSelfTransport = detail.serviceCenterSelfTransport;
  }
  if (!acceptSupplierDelivery && detail) {
    acceptSupplierDelivery = detail.acceptSupplierDelivery;
  }
  state.validateFormValue.paymentTerm = paymentTerm;
  state.validateFormValue.serviceCenterSelfTransport =
    serviceCenterSelfTransport;
  state.validateFormValue.acceptSupplierDelivery = acceptSupplierDelivery;
};

const submit = () => {
  form.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 如果销售范围没有变化，不更新客户详情
      const {
        selectedSalesRange,
        customerServiceId,
        sellerId,
        serviceCenterSelfTransport,
      } = state.validateFormValue;
      console.log(originalSalesRange.value, selectedSalesRange);
      if (
        customer.value &&
        !isEqual(originalSalesRange.value, selectedSalesRange)
      ) {
        const { customerNumber } = customer.value;
        const { salesOrganization, productGroup, distributionChannel } =
          selectedSalesRange;
        await customerStore.getClientDetail({
          customerNumber,
          productGroup,
          distributionChannel,
          salesOrganization,
          receiverContactId:
            customerStore.contactData?.receiverContact?.contactId || '',
        });
      }
      const { sellerMap, customerServiceMap } = customerDetail.value;
      updateDialogStatus(false);
      const updateItem: any = {
        customerReferenceNo: state.validateFormValue.customerReferenceNo,
        customer: {
          ...customer.value,
          customerName: state.validateFormValue.customerName,
        },
        cusDetail: {
          ...customerStore.cusDetail,
          serviceCenterSelfTransport,
          customerServiceId,
          customerServiceName: customerServiceId
            ? customerServiceMap[customerServiceId]?.userName
            : '',
          acceptSupplierDelivery:
            state.validateFormValue.acceptSupplierDelivery,
          sellerName: sellerId ? sellerMap[sellerId]?.userName : '',
          sellerId,
        },
      };
      if (!isDraftDetail.value) {
        updateItem.cusDetail = {
          ...updateItem.cusDetail,
          saleOrgVO: state.validateFormValue.selectedSalesRange,
        };
      }
      customerStore.editStore({
        ...updateItem,
      });
      await itemStore.getDeliveryDate();
    } else {
      return false;
    }
  });
};
const cancel = () => {
  updateDialogStatus(false);
  form.value?.resetFields();
};

const disabledField = (prop?: string) => {
  return initFieldsShow('customerInfo', prop)?.disabled;
};
</script>

<template>
  <el-dialog
    v-model="showDlg"
    title="客户详情"
    :show-close="false"
    width="800px"
    @closed="updateDialogStatus(false)"
  >
    <el-form
      ref="form"
      :model="state.validateFormValue"
      :rules="state.rules"
      label-position="left"
      label-width="130px"
      :disabled="isHeaderDisabled || disabledField('editCustomerDialog')"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="售达方">
            <el-input
              v-model="state.validateFormValue.customerName"
              :disabled="!vflag"
              maxlength="35"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="送达方" prop="send">
            <el-input v-model="customer.customerName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="收票方">
            <el-input v-model="customer.customerName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款方" prop="send">
            <el-input v-model="customer.customerName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="销售">
            <el-select
              v-model="state.validateFormValue.sellerId"
              clearable
              placeholder="选择销售"
            >
              <el-option
                v-for="item in sellerList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客服" prop="send">
            <el-select
              v-model="state.validateFormValue.customerServiceId"
              clearable
              placeholder="选择客服"
            >
              <el-option
                v-for="item in customerList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="客户来源">
            <el-input v-model="customerDetail.customerSourceName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款条件" prop="paymentTerm">
            <el-select
              v-model="state.validateFormValue.paymentTerm"
              placeholder="请选择付款条件"
              disabled
            >
              <el-option
                v-for="item in paymentTerms"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="自营配送">
            <el-select
              v-model="state.validateFormValue.serviceCenterSelfTransport"
              placeholder="是否自营配送"
            >
              <el-option
                v-for="item in dictList['serviceCenterSelfTransport']"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="含税/未税">
            <el-input
              v-model="
                ({ '1': '含税', '0': '未税' } as any)[customerDetail.isTax]
              "
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="销售办事处">
            <el-input v-model="salesOfficeName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="销售组">
            <el-input v-model="salesGroupName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="接受供应商直发">
            <el-select
              v-model="state.validateFormValue.acceptSupplierDelivery"
              placeholder="是否接受供应商直发"
            >
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="客户订单号">
            <el-input
              v-model="state.validateFormValue.customerReferenceNo"
              clearable
              placeholder="客户订单号"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="销售范围" prop="selectedSalesRange">
            <el-input
              v-if="isDraftDetail"
              disabled
              :model-value="draftDetailSaleRange"
            />
            <el-select
              v-else
              v-model="state.validateFormValue.selectedSalesRange"
              value-key="idx"
              placeholder="请选择销售范围"
            >
              <el-option
                v-for="item in saleOrgList"
                :key="item.key"
                :label="item.value"
                :value="item.data"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="btnGroup ba-row-center">
      <el-button type="primary" @click="submit">确认保存</el-button>
      <el-button type="primary" plain @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss" scoed>
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
