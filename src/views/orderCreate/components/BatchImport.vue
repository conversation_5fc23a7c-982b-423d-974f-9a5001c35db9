<script lang="ts" setup>
import uniq from 'lodash/uniq';
import { computed, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useItemStore } from '@/stores/item';
import bus from '@/utils/bus';
import { useCustomerStore } from '@/stores/customer';
import { SkuVoType } from '@/types/item';

const itemStore = useItemStore();
const customerStore = useCustomerStore();

const factoryList = computed(() => itemStore.factoryList);

const emit = defineEmits(['close', 'handleCheckboxChange']);

const pageLoading = ref(false);
const reactData = reactive({
  text: '',
});
const handleCancel = () => {
  emit('close');
};
const handleImport = async () => {
  if (reactData.text) {
    const uploadSkuInfoVOList: SkuVoType[] = [];
    const rows = reactData.text.split('\n');
    if (rows && rows.length > 0) {
      rows.forEach((row) => {
        const strs = row.trim();
        if (strs) {
          const strList = strs.split(/\s+/);
          if (strList && strList.length > 0) {
            const arr: string[] = [];
            strList.forEach((str) => {
              if (str) {
                const sr = str.trim();
                if (sr) {
                  arr.push(sr);
                }
              }
            });
            if (arr && arr.length <= 2) {
              const rowData = {
                skuNo: arr[0],
                quantity: 0,
              };
              const quantity = arr[1] ? arr[1].trim() : '';
              rowData.quantity = Number(quantity.replace(/,/, '')) || 0;
              uploadSkuInfoVOList.push(rowData);
            }
          }
        }
      });
    }
    const total = uploadSkuInfoVOList.length;
    if (total && factoryList?.value) {
      pageLoading.value = true;
      try {
        const res: any = await itemStore.uploadGoods({
          factoryList: factoryList.value,
          skuVOList: uploadSkuInfoVOList,
        });
        const { result } = res;
        if (result && result.code === 200) {
          const { uploadSkuInfoDetailVOList, failSkuList } = result.data;
          const successCallback = () => {
            if (
              uploadSkuInfoDetailVOList &&
              uploadSkuInfoDetailVOList.length > 0
            ) {
              emit('close');
              setTimeout(async () => {
                bus.emit('updateCheckbox', {
                  rows: itemStore.selectedItemList,
                  checked: true,
                });
                await itemStore.getDeliveryDate();
                await customerStore.changeClientDetail(true);
              });
            }
          };
          if (failSkuList && failSkuList.length > 0) {
            const failLine = failSkuList.length;
            const successLine = uploadSkuInfoDetailVOList.length;
            const skus = uniq(failSkuList).join(',');
            ElMessageBox.alert(
              `导入成功商品行数量为${successLine}行，失败数量为${failLine}行。` +
                `其中${skus}为无效商品或未维护建议销售价，无法添加!`,
              '错误',
              {
                type: 'error',
                confirmButtonText: '确定',
                callback: successCallback,
              }
            );
          } else {
            ElMessage.success(`成功导入${uploadSkuInfoDetailVOList.length}行`);
            successCallback();
          }
        } else if (result && (result.data || result.msg)) {
          ElMessageBox.alert(result.data || result.msg, '错误', {
            confirmButtonText: '确定',
            type: 'error',
          });
        }
      } catch (error) {
        console.log(error);
      } finally {
        pageLoading.value = false;
      }
    }
  }
};
</script>

<template>
  <div v-loading="pageLoading" class="batch-import">
    <div>
      请在输入框内输入sku编码以及对应数量，用空格隔开。一行对应一种商品，请注意换行。
      <span class="strong">支持直接在Excel中复制粘贴至输入框内</span>
      ，快速导入商品，最多不超过100行。
    </div>
    <div class="batch-import-input-row">
      <el-input
        v-model="reactData.text"
        :autosize="{ minRows: 4, maxRows: 6 }"
        class="batch-import-input"
        type="textarea"
        placeholder="例如：sku编码 数量"
        resize="none"
        clearable
      />
    </div>
    <div class="batch-import-btn-row">
      <el-button class="batch-import-btn" type="primary" @click="handleImport">
        确认导入
      </el-button>
      <el-button class="batch-import-btn" @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.batch-import {
  text-align: left;
  &-btn {
    width: 90px;
  }
  .batch-import-input-row {
    margin: 10px 0;
  }
  .batch-import-btn-row {
    margin: 10px auto;
    text-align: center;
  }
  .strong {
    font-weight: bold;
  }
}
</style>
