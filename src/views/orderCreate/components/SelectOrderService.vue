<!--
 * @Author: luozhikai
 * @Date: 2023-09-21 10:35:23
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-01-05 10:39:43
 * @Description: file content
-->
<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { isEmpty } from 'lodash';
import { useCommonStore } from '@/stores/common';
const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: '',
  }, // 接收值
  field: { type: String, default: '' },
  type: { type: String, default: '' },
  dictName: { type: String, default: '' },
  dictKey: { type: String, default: '' },
  isMultiple: { type: Boolean, default: false },
  isForceReset: { type: Boolean, default: false },
  defaultLabel: { type: String, default: '' },
  clearable: { type: <PERSON><PERSON>an, default: false },
  disabled: { type: Boolean, default: false },
});

const state = reactive<{ options: any[] }>({
  options: [],
});

const commonStore = useCommonStore();
const { orderServiceDict, dictList } = storeToRefs(commonStore);

const fieldConfig = computed(
  () => orderServiceDict.value[props.field] || {}
) as any;
const label = computed(() => fieldConfig.value.label || props.defaultLabel);
const multiple = computed(() => fieldConfig.value.multiple || props.isMultiple);
const forceReset = computed(
  () => fieldConfig.value.forceReset || props.isForceReset
);
const detailValue = computed(() => {
  let value: string[] = [];
  if (!multiple.value) {
    value = [props.modelValue as string];
  }
  const valueArr: string[] = [];
  value.forEach((val) => {
    const option = (state.options || []).find(({ code }) => code === val);
    if (option) {
      valueArr.push(option.name);
    }
  });
  return valueArr.join(',');
});

const mutualExclusion = (
  selected: string[],
  option1: string,
  option2: string
) => {
  if (selected.includes(option1)) {
    state.options = state.options.map((option) => {
      if (option.code === option2) {
        return {
          ...option,
          disabled: true,
        };
      }
      return option;
    });
  } else if (selected.includes(option2)) {
    state.options = state.options.map((option) => {
      if (option.code === option1) {
        return {
          ...option,
          disabled: true,
        };
      }
      return option;
    });
  } else {
    state.options = state.options.map((option) => {
      return {
        ...option,
        disabled: false,
      };
    });
  }
};
// 最多选limit项，到达上限时需要把除了已选项和无要求（code为0）项之外的其他选项置灰
// 不能直接用el-select的multiple-limit属性，会把无要求也置灰
const maxSelectLimit = (selected: string[], limit: number) => {
  if (selected.length >= limit) {
    state.options = state.options.map((option) => {
      if (option.code === '0' || selected.includes(option.code)) return option;
      return {
        ...option,
        disabled: true,
      };
    });
  } else {
    state.options = state.options.map((option) => ({
      ...option,
      disabled: false,
    }));
  }
};
const emitChange = (val: string | string[]) => {
  if (forceReset.value) {
    if (multiple.value) {
      if (val.includes('0')) {
        state.options = state.options.map((option) => {
          if (option.code !== '0') {
            return {
              ...option,
              disabled: true,
            };
          }
          return option;
        });
        emit('update:modelValue', ['0']);
      } else {
        if (props.field === 'vehicleReq') {
          // 车辆要求：国6、国5互斥
          mutualExclusion(val as string[], '10', '14');
        } else if (props.field === 'packagingReq') {
          mutualExclusion(val as string[], '03', '04');
        } else if (props.field === 'disableShipping') {
          maxSelectLimit(val as string[], 7);
        } else {
          state.options = state.options.map((option) => {
            return {
              ...option,
              disabled: false,
            };
          });
        }
        emit('update:modelValue', val);
      }
    } else {
      emit('update:modelValue', '0');
    }
  } else {
    emit('update:modelValue', val);
  }
};

watch(
  [() => Object.keys(dictList.value).length, () => props.modelValue],
  (newVal, oldVal) => {
    if (props.dictName === 'dictList') {
      if (newVal[0] && !oldVal?.[0]) {
        state.options = dictList.value
          ? dictList.value[props.dictKey || props.field]
          : [];
      }
      if (newVal[1] && (!oldVal || !oldVal?.[1])) {
        emitChange(newVal[1] as string | string[]);
      }
    }
  },
  {
    immediate: true,
  }
);

watch(
  [() => Object.keys(orderServiceDict.value).length, () => props.modelValue],
  (newVal, oldVal) => {
    if (!props.dictName || props.dictName === 'orderServiceDict') {
      if (newVal[0] && !oldVal?.[0]) {
        state.options = orderServiceDict.value[props.field]
          ? orderServiceDict.value[props.field].options
          : [];
      }
      if (newVal[1] && (!oldVal || !oldVal?.[1])) {
        emitChange(newVal[1] as string | string[]);
      }
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => props.modelValue,
  (newVal) => {
    if (
      (props.field === 'disableShipping' ||
        props.field === 'designatedShipping') &&
      isEmpty(newVal)
    ) {
      state.options = state.options.map((o) => ({
        ...o,
        disabled: false,
      }));
    }
  }
);
</script>

<template>
  <el-form-item v-if="type !== 'detail'" :label="label" :prop="field">
    <template #label>
      <div class="flex items-center">
        <el-tooltip
          v-if="field === 'labelPasteWay'"
          effect="dark"
          content="仅适用于非OEM紧固件"
          placement="top"
        >
          <el-icon class="mr-1"><InfoFilled /></el-icon>
        </el-tooltip>
        <span>{{ label }}</span>
      </div>
    </template>
    <el-select
      :multiple="multiple"
      :model-value="modelValue"
      :clearable="clearable"
      :placeholder="`请选择${label}`"
      class="w-full"
      :disabled="props.disabled"
      @change="emitChange"
    >
      <el-option
        v-for="item in state.options"
        :key="item.code"
        :label="item.name"
        :value="item.code"
        :disabled="item.disabled || item.persistDisabled"
      />
    </el-select>
  </el-form-item>
  <span v-else>
    <el-tooltip
      v-if="field === 'labelPasteWay'"
      effect="dark"
      content="仅适用于非OEM紧固件"
      placement="top"
    >
      <i class="el-icon-info" />
    </el-tooltip>
    {{ label }}：{{ detailValue }}
  </span>
</template>
