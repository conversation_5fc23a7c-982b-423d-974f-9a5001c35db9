<script lang="ts" setup>
defineProps({
  title: { type: String, default: '' },
  tooltip: { type: String, default: '' },
  isRequired: <PERSON><PERSON><PERSON>,
});
</script>

<template>
  <span class="flex items-center">
    <!-- <RequiredColTitle v-if="isRequired">{{ title }}</RequiredColTitle> -->
    <div>
      <div v-if="isRequired" class="requiredCol">
        <span>*</span>
        {{ title }}
      </div>
      <span v-else>{{ title }}</span>
    </div>
    <el-tooltip v-if="tooltip" class="item" effect="dark" placement="top">
      <template #content>{{ tooltip }}</template>
      <el-icon class="v-text-bottom"><InfoFilled /></el-icon>
    </el-tooltip>
  </span>
</template>

<style lang="scss">
.requiredCol {
  span {
    color: #f56c6c;
  }
}
</style>
