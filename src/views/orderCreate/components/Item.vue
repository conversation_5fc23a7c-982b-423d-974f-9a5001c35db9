<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useOrderStore } from '@/stores/order';
import bus from '@/utils/bus';
import { initFieldsShow } from '@/utils';
import AddItem from './AddItem.vue';
import Assign from './Assign.vue';
import Total from './Total.vue';
import ButtonGroup from './ButtonGroup.vue';
import SkuTable from './SkuTable.vue';

const orderStore = useOrderStore();

const assignRef = ref<any>(null);
const skuTableRef = ref<any>(null);
const orderType = computed(() => orderStore.orderData?.orderType);
const isSAP814 = computed(() => orderStore?.isSAP814);

bus.on('updateCheckbox', (item: any) => {
  const { rows, checked } = item;
  console.log(rows, checked);
  skuTableRef.value.tableRef.setCheckboxRow(rows, checked);
});

const showField = (prop?: string) => {
  return initFieldsShow('skuInfo', prop)?.visible;
};

defineExpose({
  assignRef,
  skuTableRef,
});
</script>

<template>
  <div class="item">
    <el-row :gutter="20">
      <el-col v-if="!isSAP814 && showField('addItem')" :span="12">
        <AddItem />
      </el-col>
      <el-col v-if="orderType === 'Z001' && showField('assign')" :span="12">
        <Assign ref="assignRef" />
      </el-col>
    </el-row>
    <el-row :gutter="20" justify="space-between">
      <el-col v-if="showField('total')" :span="8">
        <Total />
      </el-col>
      <el-col v-if="showField('buttonGroup')" :span="16">
        <ButtonGroup />
      </el-col>
    </el-row>
    <div v-if="showField('skuTable')" class="item-table">
      <SkuTable ref="skuTableRef" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.item {
  &-table {
    margin-top: 10px;
  }
}
</style>
