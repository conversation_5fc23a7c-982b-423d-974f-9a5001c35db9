<!--
 * @Author: luozhikai
 * @Date: 2023-09-13 15:38:01
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-12-19 19:15:57
 * @Description: file content
-->
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import endsWith from 'lodash/endsWith';
import { storeToRefs } from 'pinia';
import DividerHeader from '@/components/DividerHeader.vue';
import UploadFileComponent from '@/components/UploadFiles.vue';
import { useDeliveryStore } from '@/stores/delivery';
import { useCommonStore } from '@/stores/common';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useItemStore } from '@/stores/item';
import { initFieldsShow } from '@/utils';
import { getButtonAuth } from '@/utils/auth';
import { getDefaultDirectDeliverySupplier } from '@/utils/item';
import request from '@/utils/request';
import type { SalesDictType, ValidatorResultDTO } from '@/types/common';

const deliveryStore = useDeliveryStore();
const commonStore = useCommonStore();
const orderStore = useOrderStore();
const itemStore = useItemStore();
const customerStore = useCustomerStore();
const { deliveryData } = storeToRefs(deliveryStore);
const { dictList, orderFieldsSettings } = storeToRefs(commonStore);
const {
  orderData,
  isHeaderDisabled,
  isDraftDetail,
  isFakeSketch,
  isSAP814,
  categoryCode,
} = storeToRefs(orderStore);
const { itemList: skuList } = storeToRefs(itemStore);

const validatorResultDTOList = computed(() =>
  orderData.value?.validatorResultDTOList
    ?.map(
      (item: ValidatorResultDTO, index: number) =>
        `${index + 1}. ${item.validateName}: ${item.validateMsg?.replace(
          /\n/,
          '；'
        )}`
    )
    ?.join('\n')
);
const comment = computed({
  get: () => orderData.value.comment,
  set: (value) =>
    orderStore.editStore({
      orderData: {
        ...orderData.value,
        comment: value,
      },
    }),
});

const isShowSapReturnDnNo = computed(() => {
  const skuValid = (skuList.value || []).some(
    (item: any) =>
      item.directDeliverySupplier === '0' && endsWith(item.position, '04')
  );
  return (
    !isSAP814.value &&
    /z001|z006|z007/gim.test(orderData.value.orderType) &&
    orderReason.value === '038' &&
    skuValid
  );
});

const orderReason = computed({
  get: () => deliveryData.value.orderReason,
  set: (value) => deliveryStore.updateDeliveryData({ orderReason: value }),
});
const sapReturnDnNo = computed({
  get: () => deliveryData.value.sapReturnDnNo,
  set: (value) => deliveryStore.updateDeliveryData({ sapReturnDnNo: value }),
});
const orderNote = computed({
  get: () => deliveryData.value.orderNote,
  set: (value) => deliveryStore.updateDeliveryData({ orderNote: value }),
});

const attachmentList = computed({
  get: () =>
    (orderStore.orderData.attachmentList || []).map((file: any) => ({
      ...file,
      name: file.name || file.fileName,
    })),
  set: (value) =>
    orderStore.editStore({
      orderData: {
        ...orderStore.orderData,
        attachmentList: value,
      },
    }),
});

const orderReasonList = computed(() => {
  // Z018只支持“空压机直租”，“空压机加盟”
  const orList = (dictList.value && dictList.value.orderReason) || [];
  let options = orList;
  if (orderData.value.orderType) {
    const typeOptions = orList.filter(
      (or: SalesDictType) => or.parentCode === orderData.value.orderType
    );
    if (typeOptions.length > 0) {
      options = typeOptions;
    } else {
      options = orList.filter((or: SalesDictType) => or.parentCode === '');
    }
  }
  const normal = options.filter(
    (option: SalesDictType) => option.status !== 'stop'
  );
  if (administrator.value) {
    const item = options.find((option: SalesDictType) => option.code === '038');
    if (item) {
      normal.push(item);
    }
  }
  const stop = options.filter(
    (option: SalesDictType) => option.status === 'stop'
  );
  let allOptions = [...normal, ...stop];
  allOptions = allOptions.reduce((a: SalesDictType[], b: SalesDictType) => {
    if (!a.some((item) => item.code === b.code)) {
      a.push(b);
    }
    return a;
  }, []);
  return allOptions;
});

// 白名单用户可以选择对账差异调整
const administrator = ref(false);
const getPermission = async () => {
  const res = await request({
    url: '/api-opc/v1/so/template/so/create/modify/invoice/receiver/permission',
    method: 'get',
  });
  if (res.code === 200 && res.data === 'success') {
    administrator.value = true;
  } else {
    administrator.value = false;
  }
};

watch(
  () => deliveryData.value.orderReason,
  (newVal, preVal) => {
    const { salesOrganization } = customerStore.cusDetail?.saleOrgVO || {};
    const { orderBasis, orderSource } = orderData.value;
    orderStore.checkSelectWhsAuth({
      customerNo: customerStore.customer?.customerNumber || '',
      orderBasis,
      orderReason: newVal,
      orderType: categoryCode.value,
      salesOrg: salesOrganization,
      orderSource,
    });
    // 由【对账差异调整】 修改为其他选项时，自动清空已选择的发货仓，选择直发=系统自动判断，库位=自动挑仓，且两个字段置灰不可选择
    if (preVal === '038' && newVal !== '038') {
      skuList.value.forEach((item: any) => {
        item.directDeliverySupplier = getDefaultDirectDeliverySupplier(
          salesOrganization,
          categoryCode.value,
          commonStore.dictList
        );
        item.position = item.directDeliverySupplier === '2' ? '-1' : '';
      });
    }
  }
);
const orderReasonDisabled = (item: SalesDictType) => {
  if (administrator.value && item.code === '038') return false;
  return item.status === 'stop' || item.code === '055';
};

const disabledOrderReason = computed(
  () => isHeaderDisabled.value || isSAP814.value
);
const showField = (prop?: string) => {
  return initFieldsShow('otherInfo', prop)?.visible;
};
const disabledField = (prop?: string) => {
  return initFieldsShow('otherInfo', prop)?.disabled;
};
const getFieldLabel = (prop?: string) => {
  return initFieldsShow('otherInfo', prop)?.label || '';
};

onMounted(() => {
  getPermission();
});
</script>

<template>
  <DividerHeader>其他信息</DividerHeader>
  <el-row :gutter="20">
    <el-col v-if="showField('orderReason')" :span="12">
      <el-form-item
        :label="getFieldLabel('orderReason') || '订单原因'"
        prop="orderReason"
      >
        <el-select
          v-model="orderReason"
          clearable
          filterable
          placeholder="请选择订单原因"
          class="w-full"
          :disabled="
            disabledOrderReason ||
            isHeaderDisabled ||
            disabledField('orderReason')
          "
        >
          <el-option
            v-for="(item, index) in orderReasonList"
            :key="item.code + index"
            :disabled="orderReasonDisabled(item)"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col v-if="showField('sapReturnDnNo')" :span="12">
      <el-form-item
        v-if="isShowSapReturnDnNo"
        :label="getFieldLabel('sapReturnDnNo') || 'sap退货交货单'"
        prop="sapReturnDnNo"
      >
        <el-input
          v-model="sapReturnDnNo"
          placeholder="订单备注"
          :disabled="isHeaderDisabled || disabledField('sapReturnDnNo')"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col v-if="showField('orderNote')" :span="24">
      <el-form-item
        :label="getFieldLabel('orderNote') || '订单备注'"
        class="longField"
        prop="orderNote"
      >
        <el-input
          v-model="orderNote"
          placeholder="订单备注"
          type="textarea"
          :disabled="isHeaderDisabled || disabledField('orderNote')"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-form-item
        v-if="(isDraftDetail || showField('comment')) && !isFakeSketch"
        :label="getFieldLabel('comment') || '草稿单备注'"
        class="longField"
        prop="comment"
      >
        <el-input
          v-model="comment"
          :placeholder="getFieldLabel('comment') || '草稿单备注'"
          type="textarea"
          show-word-limit
          maxlength="500"
          :disabled="isHeaderDisabled || disabledField('comment')"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col v-if="showField('validatorResultDTOList')" :span="24">
      <el-form-item
        v-if="isDraftDetail && !isFakeSketch"
        :label="getFieldLabel('validatorResultDTOList') || '报错事件'"
      >
        <el-input
          v-model="validatorResultDTOList"
          type="textarea"
          disabled
          :autosize="{ minRows: 4, maxRows: 6 }"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col
      v-if="
        Object.keys(orderFieldsSettings).length > 0 &&
        showField('stockClearanceProcessNo')
      "
      :span="24"
    >
      <el-form-item
        v-if="isDraftDetail"
        label-width="170px"
        :label="
          getFieldLabel('stockClearanceProcessNo') || '库存出清审批协同流程'
        "
      >
        <el-link
          type="primary"
          target="_blank"
          :href="`/wb/so-task/list?processInstanceId=${orderData.stockClearanceProcessNo}&status=`"
        >
          {{ orderData.stockClearanceProcessNo }}
        </el-link>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col
      v-if="
        showField('attachmentList') &&
        getButtonAuth('销售跟单', `${isDraftDetail ? '草稿' : '创建'}_订单附件`)
      "
      :span="24"
    >
      <el-form-item
        :label="getFieldLabel('attachmentList') || '订单附件'"
        class="w-600px"
      >
        <UploadFileComponent
          v-model="attachmentList"
          attachment-type="general"
          :disabled="isHeaderDisabled || disabledField('attachmentList')"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>
