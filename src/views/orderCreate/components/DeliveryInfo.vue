<!--
 * @Author: luozhikai
 * @Date: 2023-09-13 15:37:44
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-09-22 11:23:58
 * @Description: file content
-->
<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { useOrderStore } from '@/stores/order';
import { useDeliveryStore } from '@/stores/delivery';
import { useCommonStore } from '@/stores/common';
import { useItemStore } from '@/stores/item';

import { getDisabledDate } from '@/utils/item';
import { initFieldsShow } from '@/utils';
import SelectOrderService from './SelectOrderService.vue';

const orderStore = useOrderStore();
const deliveryStore = useDeliveryStore();
const commonStore = useCommonStore();
const itemStore = useItemStore();

const { deliveryData } = storeToRefs(deliveryStore);
const { isHeaderDisabled } = storeToRefs(orderStore);
const { dictList, holidaysList } = storeToRefs(commonStore);

const bidCustomer = computed({
  get: () => deliveryData.value.bidCustomer,
  set: (value) => deliveryStore.updateDeliveryData({ bidCustomer: value }),
});
// const autoBatching = computed({
//   get: () => deliveryData.value.autoBatching,
//   set: (value) => deliveryStore.updateDeliveryData({ autoBatching: value }),
// });
const entireOrderRefuseSDD = computed({
  get: () => deliveryData.value.entireOrderRefuseSDD,
  set: (value) =>
    deliveryStore.updateDeliveryData({ entireOrderRefuseSDD: value }),
});
const customerReferenceDate = computed({
  get: () => deliveryData.value.customerReferenceDate,
  set: (value) =>
    deliveryStore.updateDeliveryData({ customerReferenceDate: value }),
});
const receiptTimeCategory = computed({
  get: () => deliveryData.value.receiptTimeCategory,
  set: (value) =>
    deliveryStore.updateDeliveryData({ receiptTimeCategory: value }),
});
const specifiedReceiptDayOfWeek = computed({
  get: () => deliveryData.value.specifiedReceiptDayOfWeek,
  set: (value) =>
    deliveryStore.updateDeliveryData({ specifiedReceiptDayOfWeek: value }),
});
const deliverySensitivityInterval = computed({
  get: () => deliveryData.value.deliverySensitivityInterval,
  set: (value) =>
    deliveryStore.updateDeliveryData({ deliverySensitivityInterval: value }),
});
const deliveryReplyTimeliness = computed({
  get: () => deliveryData.value.deliveryReplyTimeliness,
  set: (value) =>
    deliveryStore.updateDeliveryData({ deliveryReplyTimeliness: value }),
});
const deliverySensitivity = computed({
  get: () => deliveryData.value.deliverySensitivity,
  set: (value) =>
    deliveryStore.updateDeliveryData({ deliverySensitivity: value }),
});
const customerAcceptSysDate = computed({
  get: () => deliveryData.value.customerAcceptSysDate,
  set: (value) =>
    deliveryStore.updateDeliveryData({ customerAcceptSysDate: value }),
});

const isBidCustomer = computed(() => {
  return bidCustomer.value === 'X' || bidCustomer.value === '8';
});
const handleChangeBidCustomer = (val: string) => {
  if (
    /z001/gim.test(orderStore.orderData.orderType.value) &&
    (val === 'X' || val === '8')
  ) {
    console.log('reset customerData and refuse....');
    customerReferenceDate.value = '';
    handleRefDate('');
    entireOrderRefuseSDD.value = false;
  }
  if (val !== '13') {
    specifiedReceiptDayOfWeek.value = [];
  }
};

const disabledDate = (time: Date) => {
  const check =
    !['Z002', 'Z014'].includes(orderStore.orderData?.orderType?.value || '') &&
    !['8', 'X'].includes(bidCustomer.value);
  return getDisabledDate(
    time,
    specifiedReceiptDayOfWeek.value,
    receiptTimeCategory.value,
    holidaysList.value,
    check
  );
};

const handleRefDate = (date: string) => {
  itemStore.updateItemProps({ customerDate: date });
};

// const isAllowAutoBatching = computed(() => {
//   return autoBatching.value === 'X' || autoBatching.value === true;
// });

// const handleRefSDD = (isRefDate: CheckboxValueType) => {
//   console.log(isRefDate);
//   itemStore.updateItemProps({ refuseSystemDeliveryDate: isRefDate });
// };

const focusDatePicker = () => {
  if (
    receiptTimeCategory.value === false &&
    specifiedReceiptDayOfWeek.value?.length &&
    specifiedReceiptDayOfWeek.value?.every(
      (item: string) => !['0', '01', '02', '03', '04', '05'].includes(item)
    )
  ) {
    ElMessage.warning(
      '无可选日期，请修改客户指定日收货或工作日与周末均可收货!'
    );
  }
};

const showField = (prop?: string) => {
  return initFieldsShow('deliveryInfo', prop)?.visible;
};
const disabledField = (prop?: string) => {
  return initFieldsShow('deliveryInfo', prop)?.disabled;
};
</script>

<template>
  <el-row :gutter="20">
    <el-col v-if="showField('bidCustomer')" :span="6" class="customer-select">
      <el-form-item label="交期条件" prop="bidCustomer">
        <el-select
          v-model="bidCustomer"
          placeholder="请选择交期条件"
          :disabled="isHeaderDisabled || disabledField('bidCustomer')"
          @change="handleChangeBidCustomer"
        >
          <el-option
            v-for="item in dictList['noDeliveryReason']?.filter(
              (item) => item.supportQuery !== '0'
            )"
            :key="item.code"
            :disabled="item.status === 'stop'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col v-if="showField('receiptTimeCategory')" :span="6">
      <el-form-item label-width="0" prop="receiptTimeCategory">
        <el-checkbox
          v-model="receiptTimeCategory"
          label="周末与节假日均可收货"
          :disabled="isHeaderDisabled || disabledField('receiptTimeCategory')"
        />
      </el-form-item>
    </el-col>
    <el-col v-if="showField('customerReferenceDate')" :span="6">
      <el-form-item label="客户期望送达日期" prop="customerReferenceDate">
        <el-date-picker
          v-model="customerReferenceDate"
          :disabled-date="disabledDate"
          clearable
          type="date"
          :disabled="
            isBidCustomer ||
            isHeaderDisabled ||
            disabledField('customerReferenceDate')
          "
          value-format="YYYY-MM-DD"
          placeholder="选择日期"
          class="w-full"
          @change="handleRefDate"
          @focus="focusDatePicker"
        />
      </el-form-item>
    </el-col>
    <el-col v-if="showField('customerAcceptSysDate')" :span="6">
      <el-form-item prop="customerAcceptSysDate">
        <el-checkbox
          v-model="customerAcceptSysDate"
          :disabled="isHeaderDisabled || disabledField('customerAcceptSysDate')"
        >
          客户接受标期
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col v-if="showField('specifiedReceiptDayOfWeek')" :span="6">
      <SelectOrderService
        v-model="specifiedReceiptDayOfWeek"
        field="specifiedReceiptDayOfWeek"
        :disabled="
          isHeaderDisabled ||
          disabledField('specifiedReceiptDayOfWeek') ||
          bidCustomer !== '13'
        "
      />
    </el-col>
    <el-col v-if="showField('deliverySensitivity')" :span="6">
      <el-form-item label-width="0" prop="deliverySensitivity">
        <el-checkbox
          v-model="deliverySensitivity"
          label="客户交期敏感"
          :disabled="isHeaderDisabled || disabledField('deliverySensitivity')"
        />
      </el-form-item>
    </el-col>
    <el-col v-if="showField('deliverySensitivityInterval')" :span="6">
      <el-form-item label="交期敏感区间" prop="deliverySensitivityInterval">
        <template #label>
          <div class="flex justify-center items-center">
            <span>交期敏感区间</span>
            <el-tooltip
              effect="dark"
              content="闭区间，左、右区间可分别输入 -14～0、0～14 之间的整数，建议英文逗号分隔"
              placement="top"
            >
              <el-icon class="v-text-bottom"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input
          v-model="deliverySensitivityInterval"
          placeholder="请输入交期敏感区间"
          :disabled="
            isHeaderDisabled || disabledField('deliverySensitivityInterval')
          "
        />
      </el-form-item>
    </el-col>
    <el-col v-if="showField('deliveryReplyTimeliness')" :span="6">
      <el-form-item label="交期回复时效" prop="deliveryReplyTimeliness">
        <el-select
          v-model="deliveryReplyTimeliness"
          placeholder="请选择交期条件"
          :disabled="
            isHeaderDisabled || disabledField('deliveryReplyTimeliness')
          "
          clearable
        >
          <el-option
            v-for="item in dictList['deliveryReplyTimeliness']"
            :key="item.code"
            :disabled="item.status === 'stop'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
</template>
