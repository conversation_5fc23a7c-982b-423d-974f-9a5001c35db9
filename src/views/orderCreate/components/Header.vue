<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import type { DictList } from '@/types/common';

enum SealEnum {
  yes = 1,
  no = 2,
}

const route = useRoute();
const showDlg = ref(false);
const sealStatus = ref(SealEnum.no);

const orderNo = computed(() => route.params.orderNo as string);
const handleDownloadContract = () => {
  window.open(
    `/internal-api/so/exportSketchPdf?no=${orderNo.value}&status=${sealStatus.value}`,
    '_blank'
  );
  showDlg.value = false;
};

const orderStore = useOrderStore();
const commonStore = useCommonStore();

const { isDraftDetail, companyCode, categoryCode } = storeToRefs(orderStore);
const dictList = computed(() => commonStore.dictList);

const customerInfo = computed(() => {
  const dict: DictList = dictList.value;
  let companyName = '';
  let orderType = '';
  if (companyCode.value && categoryCode.value && dict) {
    if (Array.isArray(dict.soCategory)) {
      const dictItem = dict.soCategory.find(
        (item) => item.code === categoryCode.value
      );
      if (dictItem) {
        orderType = dictItem.name;
      }
    }
    if (Array.isArray(dict.companyScope)) {
      const dictItem = dict.companyScope.find(
        (item) => item.code === companyCode.value
      );
      if (dictItem) {
        companyName = dictItem.name;
      }
    }
  }
  return {
    companyName,
    orderType,
  };
});
</script>

<template>
  <div class="order-detail-header ba-row-between">
    <p>
      <span>当前创建订单为：</span>
      <span>
        {{ customerInfo.companyName }}&nbsp;&nbsp;&nbsp;&nbsp;{{
          customerInfo.orderType
        }}
      </span>
    </p>
    <div class="flex">
      <el-button
        v-if="isDraftDetail"
        type="primary"
        class="ml-5px"
        plain
        @click="showDlg = true"
        >导出合同</el-button
      >
    </div>
    <el-dialog v-model="showDlg" title="导出合同" width="400px">
      <div class="sealRow">
        <el-radio v-model="sealStatus" :label="2" border size="default">
          订单销售合同（无印章）
        </el-radio>
      </div>
      <div class="sealRow">
        <el-radio v-model="sealStatus" :label="1" border size="default">
          订单销售合同（有印章）
        </el-radio>
      </div>
      <div class="sealRow">
        <el-radio v-model="sealStatus" :label="3" border size="default">
          订单销售合同（WORD）
        </el-radio>
      </div>
      <template #footer>
        <span class="flex justify-center">
          <el-button @click="showDlg = false">取 消</el-button>
          <el-button type="primary" @click="handleDownloadContract"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.sealRow {
  margin-bottom: 10px;
  text-align: center;
}
.ba-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order-detail-header {
  p {
    margin: 0;
    padding: 0;
    > span:first-child {
      font-size: 14px;
    }
    > span:last-child {
      font-size: 20px;
    }
  }
}

.DialogFooter {
  text-align: center;
}
</style>