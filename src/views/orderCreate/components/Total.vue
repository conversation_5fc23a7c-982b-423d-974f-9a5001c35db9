<script lang="ts" setup>
import { computed, ref, watchEffect } from 'vue';
import { initFieldsShow } from '@/utils';
import { useCustomerStore } from '@/stores/customer';
import { useItemStore } from '@/stores/item';
import { useOrderStore } from '@/stores/order';

export interface Fields {
  label: string;
  prop: string;
  visible: boolean;
  showCurrencySymbol?: boolean;
  unit?: string;
  value?: number;
  style?: string;
}
const customerStore = useCustomerStore();
const itemStore = useItemStore();
const orderStore = useOrderStore();

const currencySymbol = computed(() => {
  return customerStore.customer?.currencySymbol || '￥';
});
const company = computed(() => {
  return orderStore.companyCode || 0;
});
// const taxedTotalAmount = computed(() => {
//   return itemStore.totalAmount.taxedTotalAmount || 0;
// });
// const untaxedTotalAmount = computed(() => {
//   return itemStore.totalAmount.untaxedTotalAmount || 0;
// });
// const taxedDiscountTotal = computed(() => {
//   return itemStore.totalAmount.taxedDiscountTotal || 0;
// });
// const unTaxedDiscountTotal = computed(() => {
//   return itemStore.totalAmount.unTaxedDiscountTotal || 0;
// });

// const showField = (prop?: string) => {
//   return initFieldsShow('skuInfo', prop)?.visible;
// };

// const getFieldLabel = (prop: string) => {
//   return initFieldsShow('skuInfo', prop)?.label;
// };
// const getFieldLabel = (prop: string) => {
//   const field = initFieldsShow('skuInfo', prop)?.field;
//   return field?.label || '';
// };

const fields = ref<Fields[]>([]);
watchEffect(() => {
  const defaultFields = [
    {
      label: '折前订单含税金额',
      prop: 'taxedTotalAmount',
      visible: true,
      style: 'width: 50%',
    },
    {
      label: '折前订单未税金额',
      prop: 'untaxedTotalAmount',
      visible: true,
      style: 'width: 50%',
    },
    {
      label: '折后订单含税金额',
      prop: 'taxedDiscountTotal',
      visible: true,
      style: 'width: 50%',
    },
    {
      label: '折后订单未税金额：',
      prop: 'unTaxedDiscountTotal',
      visible: true,
      style: 'width: 50%',
    },
  ];
  const found = initFieldsShow('skuInfo', 'total')?.children;
  if (found?.length) {
    fields.value = found;
  } else {
    fields.value = defaultFields;
  }
});
</script>

<template>
  <div class="merchantInfo-row flex flex-wrap">
    <div v-for="field in fields" :key="field.prop" :style="field.style">
      <span>
        {{ field.label }}
      </span>
      <span
        v-if="field.showCurrencySymbol !== false"
        :class="{ currency: company === '2400' }"
      >
        {{ currencySymbol }}
      </span>
      {{ itemStore.totalAmount[field.prop] }}
      <span v-if="field.unit">{{ field.unit }}</span>
    </div>
  </div>
  <!-- <div class="merchantInfo-row">
    <div>
      <span v-if="showField('taxedTotalAmount')" class="amount">
        {{ getFieldLabel('taxedTotalAmount') || '折前订单含税金额：' }}
        <span :class="{ currency: company === '2400' }">
          {{ currencySymbol }}
        </span>
        {{ formatPrice(taxedTotalAmount, 6) }}
      </span>

      <span v-if="showField('untaxedTotalAmount')" class="amount">
        {{ getFieldLabel('untaxedTotalAmount') || '折前订单未税金额：' }}
        <span :class="{ currency: company === '2400' }">
          {{ currencySymbol }}
        </span>
        {{ formatPrice(untaxedTotalAmount, 6) }}
      </span>
    </div>
    <div>
      <span v-if="showField('taxedDiscountTotal')" class="amount">
        {{ getFieldLabel('taxedDiscountTotal') || '折后订单含税金额：' }}
        <span :class="{ currency: company === '2400' }">
          {{ currencySymbol }}
        </span>
        {{ formatPrice(taxedDiscountTotal, 6) }}
      </span>
      <span v-if="showField('unTaxedDiscountTotal')" class="amount">
        {{ getFieldLabel('unTaxedDiscountTotal') || '折后订单未税金额：' }}
        <span :class="{ currency: company === '2400' }">
          {{ currencySymbol }}
        </span>
        {{ formatPrice(unTaxedDiscountTotal, 6) }}
      </span>
    </div>
  </div> -->
</template>

<style lang="scss" scoped>
.merchantInfo-row {
  font-size: 14px;
  color: #909399;
  .amount {
    margin-right: 20px;
  }
  .currency {
    color: #ff4949;
  }
}
</style>
