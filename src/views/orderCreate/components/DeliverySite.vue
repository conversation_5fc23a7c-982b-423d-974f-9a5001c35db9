<script setup lang="ts">
import { CheckboxValueType, ElMessageBox } from 'element-plus';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useOrderStore } from '@/stores/order';
import { useDeliveryStore } from '@/stores/delivery';
import { useCommonStore } from '@/stores/common';
import { useItemStore } from '@/stores/item';
import { initFieldsShow } from '@/utils';

const commonStore = useCommonStore();
const itemStore = useItemStore();
const deliveryStore = useDeliveryStore();
const orderStore = useOrderStore();

const { dictList, isShowCombinedDelivery } = storeToRefs(commonStore);
const { deliveryData } = storeToRefs(deliveryStore);
const { itemList: skuList } = storeToRefs(itemStore);
const { isHeaderDisabled } = storeToRefs(orderStore);

const entireOrderRefuseSDD = computed({
  get: () => deliveryData.value.entireOrderRefuseSDD,
  set: (value) =>
    deliveryStore.updateDeliveryData({ entireOrderRefuseSDD: value }),
});
const autoBatching = computed({
  get: () => deliveryData.value.autoBatching,
  set: (value) => deliveryStore.updateDeliveryData({ autoBatching: value }),
});

const customerReferenceDate = computed({
  get: () => deliveryData.value.customerReferenceDate,
  set: (value) =>
    deliveryStore.updateDeliveryData({ customerReferenceDate: value }),
});

const customerDeliveryConfirmed = computed({
  get: () => deliveryData.value.customerDeliveryConfirmed,
  set: (value) =>
    deliveryStore.updateDeliveryData({ customerDeliveryConfirmed: value }),
});

const autoDelivery = computed({
  get: () => deliveryData.value.autoDelivery,
  set: (value) => deliveryStore.updateDeliveryData({ autoDelivery: value }),
});

const handleChange = async (val: CheckboxValueType, el: string) => {
  if (el === 'autoBatching') {
    commonStore.changeLoading(true);
    await itemStore.getDeliveryDate();
    commonStore.changeLoading(false);
    if (val) {
      entireOrderRefuseSDD.value = false;
    }
    valCustomerDate();
  }
};

const valCustomerDate = () => {
  if (!autoBatching.value) {
    if (
      skuList.value.length > 0 &&
      skuList.value.some(
        (item) => item.customerDate !== skuList.value[0].customerDate
      )
    ) {
      ElMessageBox.confirm(
        '订单发货方式修改为整单发货，请检查和确认是否清空订单行客户期望送达日期并重新维护',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          itemStore.updateItemProps({
            customerDate: '',
          });
        })
        .catch(() => {
          autoBatching.value = true;
        });
    }
  } else if (customerReferenceDate.value) {
    ElMessageBox.confirm(
      '订单发货方式修改为分批发货，请检查和确认客户期望送达日期',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        itemStore.updateItemProps({
          customerDate: customerReferenceDate.value,
        });
      })
      .catch(() => {
        autoBatching.value = false;
      });
  }
};

const showField = (prop?: string) => {
  return initFieldsShow('deliverySite', prop)?.visible;
};
const disabledField = (prop?: string) => {
  return initFieldsShow('deliverySite', prop)?.disabled;
};
</script>

<template>
  <el-row :gutter="20">
    <el-col v-if="showField('autoDelivery')" :span="6">
      <el-form-item>
        <el-checkbox
          v-model="autoDelivery"
          :disabled="isHeaderDisabled || disabledField('autoDelivery')"
          @change="(val) => handleChange(val, 'autoDelivery')"
        >
          自动发货
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col v-if="showField('autoBatching')" :span="6">
      <el-form-item>
        <el-checkbox
          v-model="autoBatching"
          :disabled="isHeaderDisabled || disabledField('autoBatching')"
          @change="(val) => handleChange(val, 'autoBatching')"
        >
          允许分批
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col v-if="showField('customerDeliveryConfirmed')" :span="6">
      <el-form-item>
        <el-checkbox
          v-model="customerDeliveryConfirmed"
          :disabled="
            isHeaderDisabled || disabledField('customerDeliveryConfirmed')
          "
          @change="(val) => handleChange(val, 'customerDeliveryConfirmed')"
        >
          发货需与客户确认/预约
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col
      v-if="isShowCombinedDelivery && showField('combinedDelivery')"
      :span="6"
    >
      <el-form-item label="合单发货" prop="combinedDelivery">
        <el-select
          v-model="deliveryData.combinedDelivery"
          clearable
          :disabled="isHeaderDisabled || disabledField('combinedDelivery')"
        >
          <el-option
            v-for="(item, index) in dictList.CombinedDelivery"
            :key="index"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
</template>
