<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage, type FormInstance } from 'element-plus';
import { isForecastOrder } from '@/utils/orderType';
import { useCommonStore } from '@/stores/common';
import { useOrderStore } from '@/stores/order';
import { type DeliveryData, useDeliveryStore } from '@/stores/delivery';
import { formatValidators, initFieldsShow } from '@/utils';
import { Options, OrderFieldsSettings } from '@/types/common';
import request from '@/utils/request';
import FormItem from './FormItem.vue';

type FormRef = FormInstance & {
  formData: DeliveryData;
};

const commonStore = useCommonStore();
const orderStore = useOrderStore();

const deliveryStore = useDeliveryStore();
const { deliveryData } = deliveryStore;
const { dictList } = storeToRefs(commonStore);
const {
  isHeaderDisabled,
  orderData: draftDetail,
  isDraftDetail,
} = storeToRefs(orderStore);

const orderType = computed(() => orderStore.orderData?.orderType || '');

let rules: any = {
  receiverContact: [
    { required: true, message: '请选择收货联系人', trigger: 'blur' },
  ],
  orderContact: [
    { required: true, message: '请选择订单联系人', trigger: 'blur' },
  ],
  deliveryWarehouseInfo: [
    { required: true, message: '请选择送货资料需要仓配信息', trigger: 'blur' },
  ],
  collectionAmount: [
    {
      pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/,
      message: '请输入有效的预收款金额',
      trigger: 'blur',
    },
  ],
};
if (isForecastOrder(orderType.value)) {
  rules = {
    collectionAmount: [
      {
        pattern: /^([1-9]\d{0,12})(\.\d{1,2})?$/,
        message: '请输入有效的预收款金额',
        trigger: 'blur',
      },
    ],
  };
}
const reactRules = ref(rules);
// const deliveryForm = ref('deliveryForm');
const deliveryFormRef = ref<FormRef>();

const state = reactive({ ...deliveryData });

const validatedeliveryInfo = async () => {
  // 检查签单返回是否可选
  const formData = deliveryFormRef.value?.formData;
  if (!isDraftDetail.value || !formData) return true;
  // 兼容配置化表单场景，没有展示的值无需校验
  const hasSigningBack = formFields.value?.fields?.find(
    (item: any) => item.prop === 'signingBack'
  );
  if (hasSigningBack) {
    const { customerNo, salesOrganization, distributionChannel } =
      draftDetail.value || {};
    const res: any = await request({
      url: '/api-opc/v1/so/getSigningBackOptionByCustomer',
      method: 'POST',
      data: {
        customerNumber: customerNo,
        vkorg: salesOrganization,
        vtweg: distributionChannel,
      },
    });
    const { signingBack } = formData;
    console.log('signingBack', signingBack);
    if (res && res.code === 200) {
      if (res.data?.length === 0 || res.data?.includes(signingBack)) {
        return true;
      } else {
        ElMessage.error('签单返回不符合要求，请重新选择！');
        return false;
      }
    } else {
      ElMessage.error(res.msg || '查询当前客户可选签单枚举失败');
    }
  }
  return true;
};

const submit = async () => {
  const preValidRes = await validatedeliveryInfo();
  if (!preValidRes) return false;
  deliveryStore.updateDeliveryData({ ...deliveryFormRef.value?.formData });
};

// watch(
//   () => deliveryFormRef.value?.formData?.disableShipping,
//   (newVal: any) => {
//     if (!isEmpty(newVal)) {
//       console.log('监听禁用货运变化', newVal);
//       state.designatedShipping = [];
//     }
//   }
// );

// watch(
//   () => deliveryFormRef.value?.formData?.designatedShipping,
//   (newVal: any) => {
//     if (!isEmpty(newVal)) {
//       console.log('监听指定货运变化', newVal);
//       state.disableShipping = [];
//     }
//   }
// );

watch(
  () => deliveryFormRef.value?.formData?.paid,
  (newVal: any) => {
    console.log('监听已付款变化', newVal, formFields.value);
    if (!newVal && deliveryFormRef.value?.formData?.tradeNo) {
      deliveryFormRef.value.formData.tradeNo = '';
    }
  }
);

const defaultFields = ref({
  labelWidth: '165px',
  fields: [
    {
      label: '订单信息',
      type: 'dividerHeader',
      labelWidth: '0',
      span: 24,
    },
    {
      name: '后补订单',
      prop: 'backupOrder',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '已付款',
      prop: 'paid',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '清呆滞库存',
      prop: 'clearSlackStock',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '不向客户展示',
      prop: 'virtualReturn',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      label: '付款流水号',
      prop: 'tradeNo',
      type: 'input',
      span: 12,
    },
    {
      label: '项目号',
      prop: 'projectNo',
      type: 'input',
      span: 12,
    },
    {
      label: '预收款金额',
      prop: 'collectionAmount',
      type: 'input',
      span: 12,
    },
    {
      label: '供应商代码',
      prop: 'supplierAccount',
      type: 'input',
      maxlength: '10',
      span: 24,
    },
    {
      label: '付款备注',
      prop: 'paymentNote',
      type: 'textarea',
      maxlength: 50,
      span: 24,
    },
    {
      label: '合同备注',
      prop: 'agreementNote',
      type: 'textarea',
      maxlength: 50,
      span: 24,
    },
    {
      label: '随货附件要求',
      type: 'dividerHeader',
      labelWidth: '0',
      span: 24,
    },
    {
      name: '附订单',
      prop: 'attachOrder',
      type: 'checkbox',
      labelWidth: '0',
      span: 8,
    },
    {
      name: '附COA',
      prop: 'attachCoa',
      type: 'checkbox',
      labelWidth: '0',
      span: 8,
    },
    {
      name: '附MSDS',
      prop: 'attachMsds',
      type: 'checkbox',
      labelWidth: '0',
      span: 8,
    },
    {
      name: '附TDS',
      prop: 'attachTds',
      type: 'checkbox',
      labelWidth: '0',
      span: 8,
    },
    {
      name: '其他随货资料',
      prop: 'specifiedDocument',
      type: 'checkbox',
      labelWidth: '0',
      span: 8,
    },
    {
      label: '送货单模板',
      prop: 'deliverySlipTemplate',
      type: 'select',
      span: 12,
      options: dictList.value?.deliverySlipTemplate || [],
    },
    {
      label: '打印份数',
      prop: 'printNum',
      type: 'number',
      span: 12,
    },
    {
      prop: 'dnPaperReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'dnIncidentalWay',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
      clearble: true,
    },
    {
      prop: 'dnSignatureReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    { type: 'span', span: 12 },
    {
      label: '标签模板',
      prop: 'labelTemplate',
      type: 'select',
      span: 12,
      options: dictList.value?.labelTemplate || [],
    },
    {
      prop: 'otherLabelReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'fastenerLabelReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'labelPasteWay',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      label: '合格证标识',
      prop: 'certificateIdentification',
      type: 'select',
      span: 12,
      options: dictList.value?.certificateIdentification || [],
    },
    {
      label: '签单返回',
      prop: 'signingBack',
      type: 'select',
      span: 12,
      options:
        dictList.value?.signingBack?.map((item) => {
          return { ...item, disabled: item.status === 'stop' };
        }) || [],
    },
    {
      label: '仓储要求',
      type: 'dividerHeader',
      labelWidth: '0',
      span: 24,
    },
    {
      name: '隐藏logo',
      prop: 'hideLogo',
      type: 'checkbox',
      labelWidth: '0',
      span: 12,
    },
    {
      prop: 'fastenerDetect',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'packagingReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
      dictName: 'dictList',
      isMultiple: true,
      isForceReset: true,
      defaultLabel: '包装要求',
    },
    {
      prop: 'fastenerSpecialPackageReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'deliveryWarehouseInfo',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
      dictName: 'dictList',
      isMultiple: true,
      isForceReset: true,
      defaultLabel: '送货资料需要仓配信息',
    },
    {
      label: '交货其他备注',
      prop: 'deliveryOtherNote',
      type: 'textarea',
      span: 24,
      maxlength: '50',
    },
    {
      label: '运输要求',
      type: 'dividerHeader',
      labelWidth: '0',
      span: 24,
    },
    {
      name: '保税区/出口加工区',
      prop: 'exportProcessingZone',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      label: '预约送货',
      prop: 'scheduleDelivery',
      type: 'select',
      span: 12,
      placeholder: '请选择预约送货方式',
      options: dictList.value?.scheduleDelivery || [],
    },
    {
      prop: 'specifiedReceiptTime',
      type: 'selectOrderService',
      span: 24,
      labelWidth: '0',
    },
    {
      label: '装运条件',
      prop: 'shippingCondition',
      type: 'select',
      span: 12,
      options: dictList.value?.shippingConditions || [],
    },
    {
      label: '送卸货要求',
      prop: 'deliveryUnloadingReq',
      type: 'select',
      span: 12,
      options: dictList.value?.deliveryUnloadingReq || [],
    },
    {
      prop: 'disableShipping',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
      clearble: true,
    },
    {
      prop: 'designatedShipping',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
      clearble: true,
    },
    {
      prop: 'vehicleReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'deliveryRequirements',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
      dictName: 'dictList',
      dictKey: 'deliveryRequirement',
      isMultiple: true,
      isForceReset: true,
      clearble: true,
      defaultLabel: '配送员要求',
    },
    {
      prop: 'signingReq',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
    {
      prop: 'forkliftRelated',
      type: 'selectOrderService',
      span: 12,
      labelWidth: '0',
    },
  ],
});

const formFields = ref();

const formatOptions = (field: OrderFieldsSettings) => {
  if (field.type === 'select') {
    const key = field.optionKey || field.prop;
    field.options = dictList.value[key] as unknown as Options[];
  }
  return field;
};
const initFields = () => {
  try {
    const settings = initFieldsShow('editMore', 'deliveryInfo');
    if (settings?.children?.length) {
      const _fields = settings.children.map((field: OrderFieldsSettings) =>
        formatOptions(field)
      );
      formFields.value = { fields: _fields };
      if (settings?.rules && Object.keys(settings.rules)?.length > 0) {
        reactRules.value = formatValidators(settings.rules);
      } else {
        reactRules.value = {};
      }
    } else {
      formFields.value = defaultFields.value;
    }
  } catch (error) {
    console.log(error);
  }
};

const disabledField = (prop?: string) => {
  return initFieldsShow('editMore', prop)?.disabled;
};

onMounted(() => {
  console.log('deliveryInfo mounted...');
  initFields();
});

defineExpose({
  submit,
  deliveryFormRef,
});
</script>

<template>
  <FormItem
    ref="deliveryFormRef"
    :form-fields="formFields"
    :rules="reactRules"
    :init-data="state"
    :disabled="isHeaderDisabled || disabledField('deliveryInfo')"
  />
</template>

<style lang="scss">
.checkBox {
  margin: 20px;
  padding-left: 20px;
  .el-form-item {
    margin-bottom: 0;
  }
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.el-form-item__content {
  display: flex;
}
</style>
