<!--
 * @Author: luozhikai
 * @Date: 2023-09-13 15:39:04
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-01-05 15:29:21
 * @Description: file content
-->
<script setup lang="ts">
import { computed, onMounted, reactive, ref, toRefs } from 'vue';
import { storeToRefs } from 'pinia';
import DividerHeader from '@/components/DividerHeader.vue';
import SelectOrderService from '@/views/orderCreate/components/SelectOrderService.vue';
import { isForecastOrder } from '@/utils/orderType';

import { useCommonStore } from '@/stores/common';
import { useOrderStore } from '@/stores/order';
import { useDeliveryStore } from '@/stores/delivery';

console.log('init DeliveryInfo...');
const start = performance.now();

const commonStore = useCommonStore();
const orderStore = useOrderStore();

const deliveryStore = useDeliveryStore();
const { deliveryData } = deliveryStore;
const { dictList } = storeToRefs(commonStore);
const { isHeaderDisabled } = storeToRefs(orderStore);

const orderType = computed(() => orderStore.orderData?.orderType || '');

let rules: any = {
  receiverContact: [
    { required: true, message: '请选择收货联系人', trigger: 'blur' },
  ],
  orderContact: [
    { required: true, message: '请选择订单联系人', trigger: 'blur' },
  ],
  collectionAmount: [
    {
      pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/,
      message: '请输入有效的预收款金额',
      trigger: 'blur',
    },
  ],
};
if (isForecastOrder(orderType.value)) {
  rules = {
    collectionAmount: [
      {
        pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/,
        message: '请输入有效的预收款金额',
        trigger: 'blur',
      },
    ],
  };
}
const reactRules = reactive(rules);
const deliveryForm = ref('deliveryForm');

const state = reactive({ ...deliveryData });
const {
  packagingReq,
  // referenceStandardShippingReq,
  receiptTimeCategory,
  backupOrder,
  clearSlackStock,
  paid,
  attachOrder,
  specifiedDocument,
  exportProcessingZone,
  virtualReturn,
  autoDelivery,
  attachCoa,
  attachMsds,
  attachTds,
  signingBack,
  printNum,
  deliverySlipTemplate,
  deliveryUnloadingReq,
  certificateIdentification,
  labelTemplate,
  projectNo,
  collectionAmount,
  supplierAccount,
  shippingCondition,
  deliveryRequirements,
  scheduleDelivery,
  disableShipping,
  designatedShipping,
  deliveryOtherNote,
  paymentNote,
  agreementNote,
  dnPaperReq,
  dnIncidentalWay,
  dnSignatureReq,
  otherLabelReq,
  fastenerLabelReq,
  labelPasteWay,
  fastenerDetect,
  fastenerSpecialPackageReq,
  specifiedReceiptDayOfWeek,
  specifiedReceiptTime,
  vehicleReq,
  signingReq,
  forkliftRelated,
} = toRefs(state);

// const emit = defineEmits(['submit']);

// watch(disableShipping, (newVal: any) => {
//   if (!isEmpty(newVal)) {
//     console.log(newVal);
//     state.designatedShipping = [];
//   }
// });

// watch(designatedShipping, (newVal: any) => {
//   if (!isEmpty(newVal)) {
//     console.log(newVal);
//     state.disableShipping = [];
//   }
// });

const submit = () => {
  deliveryStore.updateDeliveryData({ ...state });
};

onMounted(() => {
  console.log(performance.now() - start);
});

defineExpose({
  // ...toRefs(state),
  submit,
  deliveryForm,
});
</script>

<template>
  <el-form
    ref="deliveryForm"
    label-width="120px"
    :rules="reactRules"
    :model="state"
    :disabled="isHeaderDisabled"
  >
    <DividerHeader>订单信息</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="backupOrder">
          <el-checkbox v-model="backupOrder" label="后补订单" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="paid">
          <el-checkbox v-model="paid" label="已付款" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="clearSlackStock">
          <el-checkbox v-model="clearSlackStock" label="清呆滞库存" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="virtualReturn">
          <el-checkbox v-model="virtualReturn" label="不向客户展示" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="autoDelivery">
          <el-checkbox v-model="autoDelivery" label="自动发货" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="项目号" prop="projectNo">
          <el-input
            v-model="projectNo"
            placeholder="请输入项目号"
            class="w-full"
            maxlength="40"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="预收款金额" prop="collectionAmount">
          <el-input
            v-model="collectionAmount"
            class="w-full"
            label="预收款金额"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="供应商代码" prop="supplierAccount">
          <el-input
            v-model="supplierAccount"
            placeholder="请输入供应商代码"
            maxlength="10"
            class="w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="付款备注" prop="paymentNote">
          <el-input
            v-model="paymentNote"
            placeholder="付款备注"
            maxlength="50"
            type="textarea"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="合同备注" prop="agreementNote">
          <el-input
            v-model="agreementNote"
            placeholder="合同备注"
            maxlength="50"
            type="textarea"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>随货文件要求</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <!-- <el-col :span="6">
        <el-form-item label-width="0" prop="hideLogo">
          <el-checkbox v-model="hideLogo" label="隐藏logo" />
        </el-form-item>
      </el-col> -->
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachOrder">
          <el-checkbox v-model="attachOrder" label="附订单" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachCoa">
          <el-checkbox v-model="attachCoa" label="附COA" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachMsds">
          <el-checkbox v-model="attachMsds" label="附MSDS" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachTds">
          <el-checkbox v-model="attachTds" label="附TDS" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="specifiedDocument">
          <el-checkbox v-model="specifiedDocument" label="其他随货资料" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="6">
        <el-form-item label-width="0" prop="referenceStandardShippingReq">
          <el-checkbox v-model="referenceStandardShippingReq" label="参考标准客户出货要求文件" />
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="送货单模板" prop="deliverySlipTemplate">
          <el-select
            v-model="deliverySlipTemplate"
            placeholder="请选择送货单模板"
            class="w-full"
          >
            <el-option
              v-for="item in dictList['deliverySlipTemplate']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="打印份数" prop="supplierAccount">
          <el-input-number
            v-model="printNum"
            width="120px"
            :min="0"
            :max="99"
            :step="1"
            :precision="0"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="dnPaperReq" field="dnPaperReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="dnIncidentalWay"
          clearable
          field="dnIncidentalWay"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="dnSignatureReq" field="dnSignatureReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="标签模板" prop="labelTemplate">
          <el-select
            v-model="labelTemplate"
            placeholder="请选择标签模板"
            class="w-full"
          >
            <el-option
              v-for="item in dictList['labelTemplate']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="otherLabelReq" field="otherLabelReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService
          v-model="fastenerLabelReq"
          field="fastenerLabelReq"
        />
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="labelPasteWay" field="labelPasteWay" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="合格证标识" prop="certificateIdentification">
          <el-select
            v-model="certificateIdentification"
            placeholder="请选合格证标识"
            class="w-full"
            clearable
          >
            <el-option
              v-for="item in dictList['certificateIdentification']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="签单返回" prop="signingBack">
          <el-select
            v-model="signingBack"
            placeholder="请选择签单返回"
            class="w-full"
          >
            <el-option
              v-for="item in dictList['signingBack']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              :disabled="item.status === 'stop'"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>仓储要求</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="fastenerDetect" field="fastenerDetect" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService
          v-model="packagingReq"
          dict-name="dictList"
          field="packagingReq"
          :is-multiple="true"
          :is-force-reset="true"
          default-label="包装要求"
        />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="fastenerSpecialPackageReq"
          field="fastenerSpecialPackageReq"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="交货其他备注" prop="deliveryOtherNote">
          <el-input
            v-model="deliveryOtherNote"
            placeholder="交货其他备注"
            maxlength="50"
            type="textarea"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>运输要求</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="exportProcessingZone">
          <el-checkbox
            v-model="exportProcessingZone"
            label="保税区/出口加工区"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="receiptTimeCategory">
          <el-checkbox
            v-model="receiptTimeCategory"
            label="工作日与周末均可收货"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          class="m-l--10px"
          label="预约送货"
          prop="scheduleDelivery"
        >
          <el-select
            v-model="scheduleDelivery"
            placeholder="请选择预约送货方式"
            class="w-full"
          >
            <el-option
              v-for="item in dictList['scheduleDelivery']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService
          v-model="specifiedReceiptDayOfWeek"
          field="specifiedReceiptDayOfWeek"
        />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="specifiedReceiptTime"
          field="specifiedReceiptTime"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="装运条件" prop="shippingCondition">
          <el-select
            v-model="shippingCondition"
            placeholder="请选择装运条件"
            class="w-full"
          >
            <el-option
              v-for="item in dictList['shippingConditions']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="送卸货要求" prop="deliveryUnloadingReq">
          <el-select
            v-model="deliveryUnloadingReq"
            placeholder="请选择送卸货要求"
            class="w-full"
            clearable
          >
            <el-option
              v-for="item in dictList['deliveryUnloadingReq']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService
          v-model="disableShipping"
          clearable
          field="disableShipping"
        />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="designatedShipping"
          clearable
          field="designatedShipping"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="vehicleReq" field="vehicleReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="deliveryRequirements"
          clearable
          dict-name="dictList"
          dict-key="deliveryRequirement"
          field="deliveryRequirements"
          :is-multiple="true"
          :is-force-reset="true"
          default-label="配送员要求"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <SelectOrderService v-model="signingReq" field="signingReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="forkliftRelated"
          clearable
          field="forkliftRelated"
        />
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss">
.checkBox {
  margin: 20px;
  padding-left: 20px;
  .el-form-item {
    margin-bottom: 0;
  }
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.el-form-item__content {
  display: flex;
}
</style>
