<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';

import { useItemStore } from '@/stores/item';
import { initFieldsShow } from '@/utils';
import DeliveryInfo from './DeliveryTabNew.vue';
import InvoiceInfo from './InvoiceTabNew.vue';

const props = defineProps({
  showDialog: { type: Boolean, required: true },
});

const itemStore = useItemStore();

const activeName = ref<'delivery' | 'invoice'>('delivery');
const emit = defineEmits(['update:showDialog']);

const deliveryInfo = ref<any>(null);
const invoiceInfo = ref<any>(null);

const showDlg = computed({
  get: () => props.showDialog,
  set: (val) => emit('update:showDialog', val),
});

const closeDialog = () => {
  emit('update:showDialog', false);
  deliveryInfo.value?.deliveryFormRef?.formRef?.resetFields();
  invoiceInfo.value?.invoiceFormRef?.formRef?.resetFields();
  deliveryInfo.value?.deliveryFormRef?.formRef?.clearValidate();
  invoiceInfo.value?.invoiceFormRef?.formRef?.clearValidate();
};

const submit = async () => {
  let deliveryInfoValid = true;
  let invoiceInfoValid = true;
  if (deliveryInfo.value) {
    await deliveryInfo.value?.deliveryFormRef?.formRef?.validate(
      (valid: boolean) => {
        deliveryInfoValid = valid;
        if (!valid) {
          ElMessage.error('请检查交货信息');
        }
      }
    );
  }
  if (invoiceInfo.value) {
    await invoiceInfo.value?.invoiceFormRef?.formRef?.validate(
      (valid: boolean) => {
        invoiceInfoValid = valid;
        if (!valid) {
          ElMessage.error('请检查发票信息');
        }
      }
    );
  }
  if (deliveryInfoValid && invoiceInfoValid) {
    invoiceInfo.value && invoiceInfo.value.submit();
    if (deliveryInfo.value) {
      const deliverySubmitRes = await deliveryInfo.value.submit();
      if (deliverySubmitRes === false) return;
    }
    emit('update:showDialog', false);
    await itemStore.getDeliveryDate();
  }
  // deliveryInfo.value?.deliveryFormRef?.formRef?.validate((valid: boolean) => {
  //   if (valid) {
  //     invoiceInfo.value?.invoiceFormRef?.formRef?.validate(
  //       async (v: boolean) => {
  //         console.log(v);
  //         if (v) {
  //           invoiceInfo.value.submit();
  //           deliveryInfo.value.submit();
  //           emit('update:showDialog', false);
  //           await itemStore.getDeliveryDate();
  //         } else {
  //           ElMessage.error('请检查发票信息');
  //         }
  //       }
  //     );
  //   } else {
  //     ElMessage.error('请检查交货信息');
  //   }
  // });
};

const showField = (prop?: string) => {
  return initFieldsShow('editMore', prop)?.visible;
};

onMounted(() => {
  console.log('EditOrderMore mounted...');
});
</script>

<template>
  <el-dialog
    v-model="showDlg"
    :show-close="false"
    title="编辑更多信息"
    top="10px"
    width="900px"
    class="edit-more-dialog"
    destroy-on-close
    @closed="closeDialog"
  >
    <el-scrollbar height="90vh">
      <el-tabs v-model="activeName">
        <el-tab-pane
          v-if="showField('deliveryInfo')"
          label="交货信息"
          name="delivery"
        >
          <DeliveryInfo ref="deliveryInfo" key="deliveryTab" />
        </el-tab-pane>
        <el-tab-pane
          v-if="showField('invoiceInfo')"
          label="发票信息"
          name="invoice"
        >
          <InvoiceInfo ref="invoiceInfo" key="invoiceTab" />
        </el-tab-pane>
      </el-tabs>
      <div class="ba-row-center">
        <el-button type="primary" @click="submit">确认保存</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </div>
    </el-scrollbar>
  </el-dialog>
</template>

<style lang="scss">
.edit-more-dialog {
  margin-bottom: 0;
  .el-dialog__body {
    padding: 0;
    .el-scrollbar {
      padding: 0 20px;
    }
    .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 10;
      background: white;
    }

    .ba-row-center {
      display: flex;
      align-items: center;
      justify-content: center;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 10;
      padding: 10px;
    }
  }
}
</style>
