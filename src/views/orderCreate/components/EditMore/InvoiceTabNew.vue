<!--
 * @Author: luozhikai
 * @Date: 2023-09-13 15:39:21
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-11-08 19:16:14
 * @Description: file content
-->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch, watchEffect } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import { storeToRefs } from 'pinia';
import { isEmpty } from 'lodash';
import { isForecastOrder } from '@/utils/orderType';

import { type InvoiceState, useInvoiceStore } from '@/stores/invoice';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import { useCustomerStore } from '@/stores/customer';
import { findInvoiceType, searchContactListByGroup } from '@/api/order';
import { formatValidators, initFieldsShow } from '@/utils';
import FormItem from './FormItem.vue';
import type {
  Options,
  OrderFieldsSettings,
  SalesDictType,
} from '@/types/common';

type FormRef = FormInstance & {
  formData: InvoiceState;
};

const invoiceStore = useInvoiceStore();

const { invoiceData } = invoiceStore;
const orderStore = useOrderStore();
const commonStore = useCommonStore();
const customerStore = useCustomerStore();

const checkContact = (_rule: any, value: any, callback: any) => {
  if (isEmpty(value)) {
    callback(new Error('请选择收票联系人'));
  } else {
    callback();
  }
};

let rules: any = {
  receivingInvoiceContact: [{ validator: checkContact, trigger: 'blur' }],
};

const { dictList } = storeToRefs(commonStore);
const { isHeaderDisabled } = storeToRefs(orderStore);
const orderType = computed(() => orderStore.orderData?.orderType || '');

const { customer, cusDetail } = storeToRefs(customerStore);
const saleOrgVO = computed(() => cusDetail?.value.saleOrgVO);

// const emit = defineEmits(['submit']);
// const invoiceForm = ref('invoiceForm');
const invoiceFormRef = ref<FormRef>();
const loadingInvoiceContactList = ref(false);

rules = isForecastOrder(orderType.value) ? {} : rules;
const reactRules = reactive(rules);

const state = reactive({ ...invoiceData });

const queryInvoiceContactList = (contactName: string) => {
  const { customerNumber } = customer.value;
  if (saleOrgVO.value) {
    const { salesOrganization, productGroup, distributionChannel } =
      saleOrgVO.value;
    loadingInvoiceContactList.value = true;
    searchContactListByGroup({
      customerCode: customerNumber,
      contactName,
      distributionChannel,
      productGroup,
      salesOrganization,
    })
      .then((res) => {
        if (res?.data?.records?.length) {
          state.receivingInvoiceContactList = res.data.records;
        }
      })
      .finally(() => {
        loadingInvoiceContactList.value = false;
      });
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};

const formFields = ref<any>({});

const defaultFields = ref({
  labelWidth: '140px',
  fields: [
    {
      label: '客户税号',
      prop: 'corporationTaxNum',
      type: 'input',
      span: 24,
      disabled: true,
    },
    {
      label: '开票地址和电话',
      prop: 'invoiceAddressTelephone',
      type: 'input',
      span: 24,
      disabled: true,
    },
    {
      label: '开户行',
      prop: 'bankName',
      type: 'input',
      span: 24,
      disabled: true,
    },
    {
      label: '账号',
      prop: 'bankNumber',
      type: 'input',
      span: 12,
      disabled: true,
    },
    {
      label: '客户付款账号',
      prop: 'customerPayAccountTypeName',
      type: 'input',
      span: 12,
    },
    {
      label: '收票联系人',
      prop: 'receivingInvoiceContact',
      type: 'selectContact',
      span: 12,
      required: true,
      contactList: state.receivingInvoiceContactList || [],
      loading: loadingInvoiceContactList.value,
      remoteMethod: queryInvoiceContactList,
    },
    {
      label: '收票人电话',
      prop: 'invoicePhone',
      type: 'input',
      span: 12,
      disabled: true,
    },
    {
      label: '收票地址',
      prop: 'invoiceAddress',
      type: 'input',
      span: 24,
      disabled: true,
    },
    {
      label: '寄票备注',
      prop: 'shippingInfo',
      type: 'textarea',
      span: 24,
      maxlength: '30',
      autosize: { minRows: 0, maxRows: 1 },
    },
    {
      name: '凭邮件开票',
      prop: 'invoicingByMail',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '退换货抵消',
      prop: 'returnOffset',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '合并开票',
      prop: 'mergeBilling',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '自动开票',
      prop: 'autoBilling',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '开票机器人',
      prop: 'billingRobot',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '显示折扣',
      prop: 'showDiscount',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      name: '附资料邮寄',
      prop: 'ifDocMailed',
      type: 'checkbox',
      labelWidth: '0',
      span: 6,
    },
    {
      label: '发票类型',
      prop: 'invoiceType',
      type: 'select',
      span: 12,
      options: [],
    },
    {
      label: '合并开票要求',
      prop: 'mergeBillingDemand',
      type: 'select',
      span: 12,
      options: dictList.value?.mergeBillingDemand || [],
    },
    {
      label: '快递公司',
      prop: 'expressCompany',
      type: 'select',
      span: 12,
      options: dictList.value?.expressCompany || [],
    },
    {
      label: '发票备注',
      prop: 'financialNote',
      type: 'textarea',
      span: 24,
      maxlength: '100',
    },
  ],
});

const invoiceTypeOptions = ref<SalesDictType[]>([]);
const getInvoiceType = async () => {
  const { salesOrganization } = saleOrgVO.value;
  const res = await findInvoiceType({ salesOrg: salesOrganization });
  if (res.code === 200 && res.data) {
    invoiceTypeOptions.value = res.data;
  } else {
    invoiceTypeOptions.value = [];
  }
};

watchEffect(() => {
  formFields.value?.fields?.forEach((item: any) => {
    if (item.prop === 'receivingInvoiceContact') {
      item.loading = loadingInvoiceContactList.value;
      item.contactList = state.receivingInvoiceContactList || [];
      console.log(invoiceFormRef.value?.formData);
    }
    if (item.prop === 'invoiceType') {
      item.options = invoiceTypeOptions.value;
    }
  });
});

watch(
  () => invoiceFormRef.value?.formData.receivingInvoiceContact,
  (contact) => {
    if (contact) {
      const { contactPhone, address } = contact;
      state.receivingInvoiceContact = contact;
      state.invoicePhone = contactPhone;
      state.invoiceAddress = address;
    } else {
      state.receivingInvoiceContact = {} as any;
      state.invoicePhone = '';
      state.invoiceAddress = '';
    }
  }
);

const submit = () => {
  invoiceStore.updateInvoiceData({ ...invoiceFormRef.value?.formData });
};

const formatOptions = (field: OrderFieldsSettings) => {
  if (field.type === 'select') {
    const key = field.optionKey || field.prop;
    field.options = dictList.value[key] as unknown as Options[];
  }
  return field;
};
const initFields = () => {
  try {
    const settings = initFieldsShow('editMore', 'invoiceInfo');
    if (settings?.children?.length) {
      const _fields = settings.children.map((field: OrderFieldsSettings) =>
        formatOptions(field)
      );
      formFields.value = { fields: _fields };
      if (settings?.rules && Object.keys(settings.rules)?.length > 0) {
        reactRules.value = formatValidators(settings.rules);
      } else {
        reactRules.value = { fields: [] };
      }
    } else {
      formFields.value = defaultFields.value;
    }
  } catch (error) {
    console.log(error);
  }
};

const disabledField = (prop?: string) => {
  return initFieldsShow('editMore', prop)?.disabled;
};

onMounted(() => {
  console.log('invoiceInfo mounted...');
  initFields();
  getInvoiceType();
});

defineExpose({
  submit,
  invoiceFormRef,
});
</script>

<template>
  <FormItem
    ref="invoiceFormRef"
    :form-fields="formFields"
    :rules="reactRules"
    :init-data="state"
    :disabled="isHeaderDisabled || disabledField('invoiceInfo')"
  />
</template>

<style scoped lang="scss">
.checkBox {
  margin: 20px;
  padding-left: 20px;
}
.checkBoxEmail {
  padding-left: 140px;
  margin-bottom: 20px;
  .el-form-item {
    margin-bottom: 0;
  }
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>

<style lang="scss">
.EditOrderMore-el-select {
  width: 100%;
}
</style>
