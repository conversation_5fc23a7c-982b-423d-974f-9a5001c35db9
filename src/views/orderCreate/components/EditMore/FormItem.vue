<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import DividerHeader from '@/components/DividerHeader.vue';
import SelectOrderService from '@/views/orderCreate/components/SelectOrderService.vue';
import SelectContact from '@/components/SelectContact.vue';

const props = defineProps({
  formFields: { type: Object, default: () => ({}) },
  initData: { type: Object, default: () => ({}) },
  rules: { type: Object, default: () => ({}) },
  disabled: { type: Boolean, default: false },
});

const formRef = ref();

const formData = ref<any>({ ...(props.initData || {}) });

const formFields = computed(() => props.formFields);

watch(
  () => props.initData,
  (newVal) => {
    console.log(newVal);
    if (newVal && Object.keys(newVal).length > 0) {
      formData.value = newVal;
    }
  },
  { deep: true }
);

defineExpose({
  formData,
  formRef,
});
</script>

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :disabled="disabled"
    :label-width="formFields.labelWidth || '120px'"
  >
    <el-row :gutter="10">
      <el-col
        v-for="field in formFields.fields"
        :key="field.prop"
        :span="field.span || 6"
      >
        <SelectOrderService
          v-if="field.type === 'selectOrderService'"
          v-model="formData[field.prop]"
          :field="field.prop"
          :dict-name="field.dictName"
          :dict-key="field.dictKey"
          :is-multiple="field.isMultiple"
          :is-force-reset="field.isForceReset"
          :default-label="field.defaultLabel"
          :clearable="field.clearble"
          :disabled="field.disabled"
          style="width: 100%"
        />
        <el-form-item
          v-else-if="field.prop"
          :label="field.label"
          :label-width="field.labelWidth"
          :prop="field.prop"
          :rules="field.rules"
          :required="field.required"
        >
          <el-checkbox
            v-if="field.type === 'checkbox'"
            v-model="formData[field.prop]"
            :style="field.style || { paddingLeft: '20px' }"
            :label="field.name"
            :disabled="field.disabled"
          />
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.prop]"
            filterable
            clearable
            :multiple="field.multiple"
            :placeholder="field.placeholder || `请选择${field.label}`"
            :style="field.style || 'width: 100%'"
            :disabled="field.disabled"
          >
            <el-option
              v-for="item in field.options || []"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              :disabled="item.disabled"
            />
          </el-select>
          <el-cascader
            v-else-if="field.type === 'cascader'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder || `请选择${field.label}`"
            :options="field.options"
            :props="field.cascaderProps"
            clearable
            :style="field.style || 'width: 100%'"
            :disabled="field.disabled"
          />
          <SelectContact
            v-else-if="field.type === 'selectContact'"
            v-model="formData[field.prop]"
            :title="field.label"
            :contact-list="field.contactList"
            :loading="field.loading"
            :remote-method="field.remoteMethod"
            :disabled="field.disabled"
          />
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.prop]"
            :min="field.min || 0"
            :max="field.max || 99"
            :step="field.step || 1"
            :precision="field.precision || 0"
            :disabled="field.disabled"
          />
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.prop]"
            clearable
            :autosize="field.autosize || { minRows: 2, maxRows: 4 }"
            type="textarea"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit || true"
            :placeholder="field.placeholder || `请输入${field.label}`"
            :style="field.style || ''"
            :disabled="field.disabled"
          />
          <span v-else-if="field.type === 'span'" />
          <el-input
            v-else
            v-model="formData[field.prop]"
            clearable
            type="input"
            :disabled="field.disabled"
            :maxlength="field.maxlength"
            :placeholder="field.placeholder || `请输入${field.label}`"
            :style="field.style || ''"
          />
        </el-form-item>
        <DividerHeader
          v-else-if="field.type === 'dividerHeader'"
          style="width: 100%"
          ><div>{{ field.label }}</div></DividerHeader
        >
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
