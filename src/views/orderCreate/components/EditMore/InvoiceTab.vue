<!--
 * @Author: luozhikai
 * @Date: 2023-09-13 15:39:21
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-11-08 19:16:14
 * @Description: file content
-->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { isEmpty } from 'lodash';
import { isForecastOrder } from '@/utils/orderType';

import { useInvoiceStore } from '@/stores/invoice';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import { useCustomerStore } from '@/stores/customer';
import { searchContactListByGroup } from '@/api/order';
import SelectContact from '@/components/SelectContact.vue';

const invoiceStore = useInvoiceStore();

const { invoiceData } = invoiceStore;
const orderStore = useOrderStore();
const commonStore = useCommonStore();
const customerStore = useCustomerStore();

const checkContact = (_rule: any, value: any, callback: any) => {
  if (isEmpty(value)) {
    callback(new Error('请选择收票联系人'));
  } else {
    callback();
  }
};

let rules: any = {
  receivingInvoiceContact: [{ validator: checkContact, trigger: 'blur' }],
};

const { dictList } = storeToRefs(commonStore);
const { isHeaderDisabled } = storeToRefs(orderStore);
const orderType = computed(() => orderStore.orderData?.orderType || '');

const { customer, cusDetail } = storeToRefs(customerStore);
const saleOrgVO = computed(() => cusDetail?.value.saleOrgVO);

// const emit = defineEmits(['submit']);
const invoiceForm = ref('invoiceForm');
const loadingInvoiceContactList = ref(false);

rules = isForecastOrder(orderType.value) ? {} : rules;
const reactRules = reactive(rules);

const state = reactive({ ...invoiceData });
const {
  corporationTaxNum,
  invoiceAddressTelephone,
  bankName,
  bankNumber,
  invoicePhone,
  invoiceAddress,
  receivingInvoiceContactList,
  customerPayAccountTypeName,
  shippingInfo,
  invoicingByMail,
  returnOffset,
  mergeBilling,
  autoBilling,
  billingRobot,
  showDiscount,
  ifDocMailed,
  invoiceType,
  mergeBillingDemand,
  expressCompany,
  financialNote,
} = toRefs(state);

const receivingInvoiceContact = computed({
  get: () => state.receivingInvoiceContact,
  set: (contact) => {
    if (contact) {
      const { contactPhone, address } = contact;
      state.receivingInvoiceContact = contact;
      state.invoicePhone = contactPhone;
      state.invoiceAddress = address;
    } else {
      state.receivingInvoiceContact = {} as any;
      state.invoicePhone = '';
      state.invoiceAddress = '';
    }
  },
});

const queryInvoiceContactList = (contactName: string) => {
  const { customerNumber } = customer.value;
  if (saleOrgVO.value) {
    const { salesOrganization, productGroup, distributionChannel } =
      saleOrgVO.value;
    loadingInvoiceContactList.value = true;
    searchContactListByGroup({
      customerCode: customerNumber,
      contactName,
      distributionChannel,
      productGroup,
      salesOrganization,
    })
      .then((res) => {
        if (res?.data?.records?.length) {
          // change local options
          state.receivingInvoiceContactList = res.data.records;
        }
      })
      .finally(() => {
        loadingInvoiceContactList.value = false;
      });
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};

const submit = () => {
  invoiceStore.updateInvoiceData({ ...state });
};

onMounted(() => {
  console.log('invoiceInfo mounted...');
});

defineExpose({
  ...toRefs(state),
  submit,
  invoiceForm,
});
</script>

<template>
  <el-form
    ref="invoiceForm"
    label-width="140px"
    :rules="reactRules"
    :model="state"
    :disabled="isHeaderDisabled"
  >
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="客户税号">
          <el-input v-model="corporationTaxNum" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开票地址和电话">
          <el-input v-model="invoiceAddressTelephone" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开户行">
          <el-input v-model="bankName" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="账号">
          <el-input v-model="bankNumber" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户付款账号" prop="customerPayAccountTypeName">
          <el-input
            v-model="customerPayAccountTypeName"
            placeholder="请输入客户付款账号"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item
          required
          label="收票联系人"
          prop="receivingInvoiceContact"
        >
          <SelectContact
            v-if="receivingInvoiceContact"
            v-model="receivingInvoiceContact"
            title="收票联系人"
            :contact-list="receivingInvoiceContactList"
            :loading="loadingInvoiceContactList"
            :remote-method="queryInvoiceContactList"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收票人电话" prop="invoicePhone">
          <el-input
            v-model="invoicePhone"
            placeholder="选择收票人电话"
            disabled
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="收票地址" prop="invoiceAddress">
          <el-input
            v-model="invoiceAddress"
            placeholder="选择收票地址"
            disabled
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="寄票备注">
          <el-input
            v-model="shippingInfo"
            placeholder="寄票备注"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxEmail">
      <el-col :span="6">
        <el-form-item label-width="0" prop="invoicingByMail">
          <el-checkbox v-model="invoicingByMail" label="凭邮件开票" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="returnOffset">
          <el-checkbox v-model="returnOffset" label="退换货抵消" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="mergeBilling">
          <el-checkbox v-model="mergeBilling" label="合并开票" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="autoBilling">
          <el-checkbox v-model="autoBilling" label="自动开票" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxEmail">
      <el-col :span="6">
        <el-form-item label-width="0" prop="billingRobot">
          <el-checkbox v-model="billingRobot" label="开票机器人" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="showDiscount">
          <el-checkbox v-model="showDiscount" label="显示折扣" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="ifDocMailed">
          <el-checkbox v-model="ifDocMailed" label="附资料邮寄" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="发票类型">
          <el-select
            v-model="invoiceType"
            placeholder="请选择普票/增票/服务发票"
          >
            <el-option
              v-for="item in dictList['invoiceType']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合并开票要求">
          <el-select
            v-model="mergeBillingDemand"
            placeholder="请选择合并开票要求"
          >
            <el-option
              v-for="item in dictList['mergeBillingDemand']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="快递公司">
          <el-select
            v-model="expressCompany"
            clearable
            placeholder="请选择快递公司"
          >
            <el-option
              v-for="item in dictList['expressCompany']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="发票备注" prop="financialNote">
          <el-input
            v-model="financialNote"
            placeholder="发票备注"
            type="textarea"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style scoped lang="scss">
.checkBox {
  margin: 20px;
  padding-left: 20px;
}
.checkBoxEmail {
  padding-left: 140px;
  margin-bottom: 20px;
  .el-form-item {
    margin-bottom: 0;
  }
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>

<style lang="scss">
.EditOrderMore-el-select {
  width: 100%;
}
</style>