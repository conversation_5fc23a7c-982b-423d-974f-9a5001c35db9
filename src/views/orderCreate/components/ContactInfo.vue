<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useInvoiceStore } from '@/stores/invoice';
import { searchContactListByGroup } from '@/api/order';
import { type ContactType } from '@/types/common';
import SelectContact from '@/components/SelectContact.vue';
import { initFieldsShow } from '@/utils';

const customerStore = useCustomerStore();
const orderStore = useOrderStore();
const invoiceStore = useInvoiceStore();

const { customer, cusDetail, contactData, selectedSalesRange } =
  storeToRefs(customerStore);
const { isHeaderDisabled } = storeToRefs(orderStore);
const { invoiceData } = storeToRefs(invoiceStore);

const receiverContact = computed({
  get: () => contactData.value.receiverContact as ContactType,
  set: (contact) => {
    changeContact(contact);
  },
});

const changeContact = async (contact: ContactType | null) => {
  if (contact) {
    const {
      address,
      contactPhone,
      province: receiverProvinceCode,
      city: receiverCityCode,
      region: receiverDistrictCode,
      addressId: receiverAddressId,
    } = contact;
    customerStore.editStore({
      contactData: {
        ...contactData.value,
        receiverContact: {
          ...contact,
          receiverProvinceCode,
          receiverCityCode,
          receiverDistrictCode,
          receiverAddressId,
        },
        receiverPhone: contactPhone,
        receiverAddress: address,
      },
    });
  } else {
    customerStore.editStore({
      contactData: {
        receiverContact: '',
        receiverPhone: '',
        receiverAddress: '',
      },
    });
  }
  const { salesOrganization, productGroup, distributionChannel } =
    selectedSalesRange.value;
  await customerStore.getClientDetail({
    customerNumber: customer.value?.customerNumber,
    productGroup,
    distributionChannel,
    salesOrganization,
    receiverContactId:
      customerStore.contactData?.receiverContact?.contactId || '',
  });
};

const changeReceivingInvoiceContact = (contact: ContactType) => {
  if (contact) {
    const { address, contactPhone } = contact;
    invoiceStore.updateInvoiceData({
      receivingInvoiceContact: contact,
      invoicePhone: contactPhone,
      invoiceAddress: address,
    });
  } else {
    invoiceStore.updateInvoiceData({
      receivingInvoiceContact: '',
      invoicePhone: '',
      invoiceAddress: '',
    });
  }
};
const receivingInvoiceContact = computed({
  get: () => invoiceData.value.receivingInvoiceContact as ContactType,
  set: (contact) => {
    changeReceivingInvoiceContact(contact as ContactType);
  },
});

const changeOrderContact = (contact: ContactType | null) => {
  if (contact) {
    const { contactName, contactPhone } = contact;
    customerStore.editStore({
      contactData: {
        ...contactData.value,
        orderContact: contact,
        orderContactName: contactName,
        orderContactPhone: contactPhone,
      },
    });
  } else {
    customerStore.editStore({
      contactData: {
        orderContact: '',
        orderContactName: '',
        orderContactPhone: '',
      },
    });
  }
};
const orderContact = computed({
  get: () => contactData.value.orderContact as ContactType,
  set: (contact) => {
    changeOrderContact(contact);
  },
});

const loadingReceiverContact = ref(false);
const loadingOrderContact = ref(false);
const loadingInvoiceContact = ref(false);

const queryReceiverContactList = (contactName: string) => {
  commonQuery(contactName, 'receiverContactList');
};
const queryOrderContactList = (contactName: string) => {
  commonQuery(contactName, 'orderContactList');
};
const queryReceivingInvoiceContactList = (contactName: string) => {
  commonQuery(contactName, 'receivingInvoiceContactList');
};

const commonQuery = (contactName: string, type: string) => {
  const { customerNumber } = customer.value;
  if (cusDetail.value.saleOrgVO) {
    const { salesOrganization, productGroup, distributionChannel } =
      cusDetail.value.saleOrgVO;
    if (type === 'receiverContactList') {
      loadingReceiverContact.value = true;
    }
    if (type === 'orderContactList') {
      loadingOrderContact.value = true;
    }
    if (type === 'receivingInvoiceContactList') {
      loadingInvoiceContact.value = true;
    }
    searchContactListByGroup({
      customerCode: customerNumber,
      contactName,
      distributionChannel,
      productGroup,
      salesOrganization,
    })
      .then((res) => {
        if (res?.data?.records?.length) {
          if (type === 'receiverContactList') {
            customerStore.editStore({
              contactData: {
                receiverContactList: res.data.records,
              },
            });
          }
          if (type === 'orderContactList') {
            customerStore.editStore({
              contactData: {
                orderContactList: res.data.records,
              },
            });
          }
          if (type === 'receivingInvoiceContactList') {
            invoiceStore.updateInvoiceData({
              receivingInvoiceContactList: res.data.records,
            });
          }
        }
      })
      .finally(() => {
        loadingReceiverContact.value = false;
        loadingOrderContact.value = false;
        loadingInvoiceContact.value = false;
      });
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};

const showField = (prop?: string) => {
  return initFieldsShow('customerInfo', prop)?.visible;
};
const disabledField = (prop?: string) => {
  return initFieldsShow('customerInfo', prop)?.disabled;
};

defineExpose({
  changeContact,
  changeReceivingInvoiceContact,
  changeOrderContact,
});
</script>

<template>
  <el-col v-if="showField('receiverContact')" :span="6">
    <el-form-item label="收货联系人" prop="receiverContact">
      <SelectContact
        v-model="receiverContact"
        title="收货联系人"
        :disabled="
          !(customer && customer.customerNumber) ||
          isHeaderDisabled ||
          disabledField('receiverContact')
        "
        :contact-list="contactData.receiverContactList"
        :loading="loadingReceiverContact"
        :remote-method="queryReceiverContactList"
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('receiverPhone')" :span="6">
    <el-form-item label="收货人电话" prop="receiverPhone">
      <el-input
        :value="contactData.receiverPhone || '--'"
        placeholder="联系电话"
        disabled
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('receiverAddress')" :span="12">
    <el-form-item label="收货地址" class="longField" prop="receiverAddress">
      <el-input
        :value="contactData.receiverAddress || '--'"
        placeholder="收货地址"
        disabled
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('receivingInvoiceContact')" :span="6">
    <el-form-item label="收票联系人" prop="receivingInvoiceContact">
      <SelectContact
        v-model="receivingInvoiceContact"
        title="收票联系人"
        :disabled="
          !(customer && customer.customerNumber) ||
          isHeaderDisabled ||
          disabledField('receivingInvoiceContact')
        "
        :contact-list="invoiceData.receivingInvoiceContactList"
        :loading="loadingInvoiceContact"
        :remote-method="queryReceivingInvoiceContactList"
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('invoicePhone')" :span="6">
    <el-form-item label="收票人电话" prop="invoicePhone">
      <el-input
        :value="invoiceData.invoicePhone || '--'"
        placeholder="收票人电话"
        :disabled="true"
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('invoiceAddress')" :span="12">
    <el-form-item label="收票地址" class="longField" prop="invoiceAddress">
      <el-input
        :value="invoiceData.invoiceAddress || '--'"
        placeholder="收票地址"
        :disabled="true"
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('orderContact')" :span="6">
    <el-form-item label="订单联系人" prop="orderContact">
      <SelectContact
        v-model="orderContact"
        title="订单联系人"
        :disabled="
          !(customer && customer.customerNumber) ||
          isHeaderDisabled ||
          disabledField('orderContact')
        "
        :contact-list="contactData.orderContactList"
        :loading="loadingOrderContact"
        :remote-method="queryOrderContactList"
      />
    </el-form-item>
  </el-col>
  <el-col v-if="showField('orderContactPhone')" :span="6">
    <el-form-item label="订单人电话" prop="orderContactPhone">
      <el-input
        :value="contactData.orderContactPhone || '--'"
        placeholder="订单人电话"
        :disabled="true"
      />
    </el-form-item>
  </el-col>
</template>
