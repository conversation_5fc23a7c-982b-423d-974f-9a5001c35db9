<script lang="ts" setup>
import { computed, reactive, ref, watchEffect } from 'vue';
import slice from 'lodash/slice';
import { storeToRefs } from 'pinia';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import { useItemStore } from '@/stores/item';
import { useDeliveryStore } from '@/stores/delivery';
import OptRequiredRowTitle from '@/views/orderCreate/components/OptRequiredRowTitle.vue';
import ProductDetail from '@/views/orderCreate/components/ProductDetail/index.vue';
import { createColumn, draftColumn, getColumns } from '@/utils/column';
import { type ItemType, type JumpToOuterType } from '@/types/item';
import {
  isEnableDirectDeliverySupplier as isEnableDirectDeliverySupplierApi,
  isForecastOrder,
  isFreeOrder,
  isInnerOrderReason,
} from '@/utils/orderType';
import {
  formatPrice,
  formatValidators,
  initFieldsShow,
  isNull,
} from '@/utils/index';
import {
  formatSelectV2Options,
  getDirectDeliverySupplierList,
  getDisabledDate,
  getPositionList,
} from '@/utils/item';
import { getButtonAuth } from '@/utils/auth';
import { OrderBasisConfig } from '@/types/common';
import type {
  VxeTableDefines,
  VxeTableInstance,
  VxeTablePropTypes,
} from 'vxe-table';

const customerStore = useCustomerStore();
const orderStore = useOrderStore();
const commonStore = useCommonStore();
const itemStore = useItemStore();
const deliveryStore = useDeliveryStore();

const {
  dictList = {},
  mmDictList,
  holidaysList,
  orderBasisConfig,
} = storeToRefs(commonStore) as any;
const { selectedSalesRange } = storeToRefs(customerStore);
const { orderData, isDraftDetail, isFakeSketch, isSAP814, allowSelectWhsAuth } =
  storeToRefs(orderStore) as any;
const { factoryList, positionListMap } = storeToRefs(itemStore);
const { deliveryData } = storeToRefs(deliveryStore);

const orderDataInfo = computed(() => orderStore.orderData);

const orderType = computed(() => orderData.value?.orderType);
const isForecast = computed(() => {
  return isForecastOrder(orderType.value);
});
const isZ007 = computed(() => orderType.value === 'Z007');
const isTax = computed(() => customerStore.cusDetail?.isTax);
const isTaxedCustomer = computed(() => isTax.value === '1');
const currentSalesRangeOrderBasisConfig = computed<OrderBasisConfig[]>(() => {
  const selected = selectedSalesRange.value.salesOrganization;
  const objKeys = Object.keys(orderBasisConfig.value);
  const key = objKeys.find((k) => k.slice(0, 2) === selected.slice(0, 2))!;
  return orderBasisConfig.value[key];
});

const specifiedReceiptDayOfWeek = computed(
  () => deliveryData.value.specifiedReceiptDayOfWeek
);
const receiptTimeCategory = computed(
  () => deliveryData.value?.receiptTimeCategory
);
const bidCustomer = computed(() => deliveryData.value?.bidCustomer);
const disabledDate = (time: Date) => {
  const check =
    !['Z002', 'Z014'].includes(orderType.value) &&
    !['8', 'X'].includes(bidCustomer.value);
  return getDisabledDate(
    time,
    specifiedReceiptDayOfWeek.value,
    receiptTimeCategory.value,
    holidaysList.value,
    check
  );
};

const focusDatePicker = () => {
  if (
    (receiptTimeCategory.value === 'Z' ||
      receiptTimeCategory.value === false) &&
    specifiedReceiptDayOfWeek.value?.length &&
    specifiedReceiptDayOfWeek.value?.every(
      (item: string) => !['0', '01', '02', '03', '04', '05'].includes(item)
    )
  ) {
    ElMessage.warning(
      '无可选日期，请修改客户指定日收货或工作日与周末均可收货!'
    );
  }
};

const skuList = computed(() => {
  return slice(itemStore.itemList || []);
});
const isBidCustomer = computed(() => {
  return bidCustomer.value === 'X' || bidCustomer.value === '8';
});
// const selectedSalesRange = computed(() => store.state.customer.selectedSalesRange);
const costCenterList = computed(() => {
  return (
    commonStore.costCenterList.map((item) => ({
      label: `${item.costCenter} ${item.description}`,
      value: item.costCenter,
      ...item,
    })) || []
  );
});
const customerNumber = computed(
  () => customerStore?.customer?.customerNumber || ''
);
const autoBatching = computed(() => deliveryData.value?.autoBatching);
const orderReason = computed(() => deliveryData.value?.orderReason);
const isEnableDirectDeliverySupplier = computed(() => {
  return isEnableDirectDeliverySupplierApi(orderType.value);
});
const directDeliverySupplierList = computed(() => {
  return getDirectDeliverySupplierList(
    dictList.value,
    orderType.value,
    selectedSalesRange.value.salesOrganization
  );
});

const selectedItemList = computed({
  get: () => itemStore.selectedItemList,
  set: (val) =>
    itemStore.editStore({
      selectedItemList: val.filter((item) => item.itemEditable !== '0'),
    }),
});

// 是否展示交货挂起tag（’99‘为解除挂起，这种情况不展示）
const showDnOrderPendingReasonTag = (row: ItemType) => {
  return (
    (row.dnOrderPendingReasons || []).filter((code: string) => code !== '99')
      ?.length > 0
  );
};

const tableRef = ref<VxeTableInstance>();
const tableCustom = {
  storage: true,
};
const toolbarConfig = {
  // custom: true,
  // zoom: true,
  slots: { tools: 'toolbar_tools' },
};
const checkboxConfig = reactive<VxeTablePropTypes.CheckboxConfig>({
  checkAll: true,
  checkMethod: ({ row }: any) => {
    return row && row.itemEditable !== '0';
  },
});

// const columns = ref(
//   getColumns(
//     isDraftDetail.value ? draftColumn : createColumn
//   ) as VxeTableDefines.ColumnInfo<any>[]
// );

const columns = ref<VxeTableDefines.ColumnInfo<any>[]>([]);

const columnConfig = {
  isBidCustomer: ['recentDeliveryDate', 'deliveryFrequency', 'deliveryCycle'], // 未发货条件为客户叫料时需要显示的列
  isZ007: ['costCenter', 'generalLedgerAccount'], // 订单类型为Z007需要显示的列
  isSAP814: ['originSapOrderNo', 'referenceOrderNo', 'referenceOrderItemNo'], // SAP-814订单需要显示的列
};

const initColumns = () => {
  const fields = initFieldsShow('skuInfo', 'skuTable')?.children;
  if (fields?.length) {
    columns.value = getColumns(fields) as VxeTableDefines.ColumnInfo<any>[];
    return;
  }
  columns.value = getColumns(
    isDraftDetail.value ? draftColumn : createColumn
  ) as VxeTableDefines.ColumnInfo<any>[];
  if (isFakeSketch.value) {
    columns.value.splice(
      columns.value.findIndex((item) => item.field === 'soNo'),
      3
    );
  }
};

const editRules = ref<any>({});
const getEditRules = () => {
  const rules = initFieldsShow('skuInfo', 'skuTable')?.rules;
  if (rules && Object.keys(rules)?.length > 0) {
    editRules.value = formatValidators(rules);
  }
  // return {};
};

watchEffect(() => {
  initColumns();
  getEditRules();
  const cloneColumns = [...columns.value];
  cloneColumns.forEach((column: any) => {
    if (columnConfig.isZ007.includes(column.field as string)) {
      column.visible = isZ007.value ? true : false;
    }
    if (columnConfig.isBidCustomer.includes(column.field as string)) {
      column.visible = isBidCustomer.value ? true : false;
    }
    if (columnConfig.isSAP814.includes(column.field as string)) {
      column.visible = isSAP814.value ? true : false;
    }
  });
  columns.value = cloneColumns;

  // tableRef.value?.loadColumn(
  //   columns.value as VxeTableDefines.ColumnInfo<any>[]
  // );
});

// watchEffect(() => {
//   editRules.value.freeTaxSaleAssessmentUnitPrice?.push({
//     validator: ({ cellValue, row }: any) => {
//       if (orderData.value?.orderBasis !== 'STOCK_CLEARANCE') return Promise.resolve();
//       const priceConfig = currentSalesRangeOrderBasisConfig.value?.find(
//         (config) => config.factoryConfig === row.factory
//       )?.freeTaxSaleAssessmentUnitPriceConfig;
//       if((priceConfig === 'geZero' && cellValue < 0) ||
//       (priceConfig === 'gtZero' && cellValue <= 0)) {
//         return Promise.reject()
//       }
//       return Promise.resolve()
//     }
//   })
// })

const isItemDisabled = (
  row: ItemType,
  column: VxeTableDefines.ColumnInfo<any>
) => {
  return (row && row.itemEditable === '0') || column?.params?.disabled === true; // 0：不可编辑 1：可编辑
};

const removeSku = (index: number) => {
  ElMessageBox.confirm('此操作将删除所选的商品, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    if (skuList.value && skuList.value.length > 0) {
      const idx = (skuList.value[index] as any)?.idx;
      itemStore.removeItemList({
        value: [idx],
        isTax: isTax.value,
      });
      if (index === 0 && skuList.value.length > 0) {
        const goodsItem = skuList.value.find((item: ItemType) => item.skuNo);
        const { salesOrganization, distributionChannel } =
          selectedSalesRange.value || {};
        if (goodsItem && goodsItem.productGroup) {
          await customerStore.getClientDetail({
            customerNumber: customerNumber.value,
            productGroup: goodsItem.productGroup,
            distributionChannel,
            salesOrganization,
            receiverContactId:
              customerStore.contactData?.receiverContact?.contactId || '',
          });
          getDeliveryDate();
        }
      } else {
        getDeliveryDate();
      }
    }
  });
};

const showSkuDetail = (index: number) => {
  itemStore.editStore({
    showSelectedItem: true,
    selectedIndex: index,
  });
};

// const changeColumnCheckbox = (value: any, type: ItemRowType) => {
//   skuList.value.forEach((_sku: any, index: number) => {
//     changeRow(value, index, type);
//   });
// };

const formatTaxRate = (row: any) => {
  if (row && row.taxRate) {
    return `${row.taxRate * 100}%`;
  }
  return 0;
};
const isFactoryDisable = (row: any, code: string | number) => {
  if (row && row.factoryProductPriceVOMap && code) {
    const { factoryProductPriceVOMap } = row;
    if (typeof code !== 'number') {
      code = Number.parseInt(code, 10);
    }
    return !(
      factoryProductPriceVOMap[code] &&
      ((orderData.value?.orderBasis === 'STOCK_CLEARANCE' &&
        currentSalesRangeOrderBasisConfig.value?.some(
          (item: OrderBasisConfig) => Number(item.factoryConfig) === code
        )) ||
        orderData.value?.orderBasis !== 'STOCK_CLEARANCE')
    );
  }
  return true;
};

const getDeliveryDate = async () => {
  orderStore.editStore({
    pageLoading: true,
  });
  await itemStore.getDeliveryDate();
  orderStore.editStore({
    pageLoading: false,
  });
};

const handleCheckboxChange = (event: any) => {
  const { records } = event;
  selectedItemList.value = records;
};

const changeRow = async (value: any, index: number, type: string) => {
  await itemStore.updateItem({ type, index, value });
  if (type === 'quantity') {
    getDeliveryDate();
  }
};

const computedMax = (
  row: ItemType,
  column: VxeTableDefines.ColumnInfo<any>
) => {
  if (column.params?.maxProp) {
    const foundItem = orderDataInfo.value?.items?.find(
      (item: ItemType) => item.orderItemNo === row.orderItemNo
    );
    if (foundItem) {
      return foundItem[column.params?.maxProp];
    }
  }
  return Number.POSITIVE_INFINITY;
};

const jumpToOuter = (type: keyof JumpToOuterType, row: ItemType) => {
  const field = row[type];
  if (field) {
    switch (type) {
      case 'soNo':
        window.open(`/orderSale/formal/detail/${field}?soNo=${field}`);
        break;
      case 'unifyApprovalNo':
        window.open(`/priceAudit/audit/${field}`);
        break;
    }
  }
};

// 不在名单内客户建单时，选择直发=系统自动判断，库位=自动挑仓，且两个字段置灰不可选择
const disabledDirectAndPosition = (row: ItemType) => {
  return (
    !allowSelectWhsAuth.value &&
    row.directDeliverySupplier === '2' &&
    row.position === '-1'
  );
};
// 选择直发禁用逻辑
const disabledDirectDeliverySupplier = (
  row: ItemType,
  column: VxeTableDefines.ColumnInfo<any>
) => {
  return (
    isEnableDirectDeliverySupplier.value ||
    row.isPositionDisabled ||
    isItemDisabled(row, column) ||
    isSAP814.value ||
    disabledDirectAndPosition(row)
  );
};

// 库位禁用逻辑
const disabledPosition = (
  row: ItemType,
  column: VxeTableDefines.ColumnInfo<any>
) => {
  return (
    row.isPositionDisabled ||
    isItemDisabled(row, column) ||
    isSAP814.value ||
    disabledDirectAndPosition(row)
  );
};

const isFreeTaxSaleAssessmentUnitPriceError = (
  factory: string,
  price: number
) => {
  if (orderData.value?.orderBasis !== 'STOCK_CLEARANCE') return false;
  const priceConfig = currentSalesRangeOrderBasisConfig.value?.find(
    (config) => config.factoryConfig === factory
  )?.freeTaxSaleAssessmentUnitPriceConfig;
  return (
    (priceConfig === 'geZero' && price < 0) ||
    (priceConfig === 'gtZero' && price <= 0)
  );
};

defineExpose({
  tableRef,
  currentSalesRangeOrderBasisConfig,
});
</script>

<template>
  <div class="order-sale-draft-table">
    <vxe-grid
      id="order-sale-draft-grid"
      ref="tableRef"
      border
      keep-source
      show-overflow
      align="center"
      show-header-overflow
      size="small"
      max-height="600"
      :row-config="{
        height: 80,
      }"
      :scroll-x="{ enabled: true, gt: 0 }"
      :scroll-y="{ enabled: true, gt: 0 }"
      :custom-config="tableCustom"
      :data="skuList"
      :columns="columns"
      :toolbar-config="toolbarConfig"
      :edit-rules="editRules"
      :column-config="{
        resizable: true,
        isHover: true,
        useKey: true,
        minWidth: 50,
        maxFixedSize: 6,
      }"
      :checkbox-config="checkboxConfig"
      @checkbox-change="handleCheckboxChange"
      @checkbox-all="handleCheckboxChange"
    >
      <template #toolbar_tools>
        <!-- <el-button
          type="primary"
          class="mr-3"
          :disabled="selectedItemList.length === 0"
          @click="createSalePrice"
          >价格审批</el-button
        > -->
      </template>
      <!-- 自定义标题 -->
      <template #customTitle="{ column }">
        <OptRequiredRowTitle
          :title="column.title"
          :tooltip="column.params?.tooltip"
          :is-required="!!column.params?.required"
        />
      </template>
      <template #sketchOrderItemNo="{ row }">
        <el-tag
          v-if="row.notAcceptDemandReasons?.length > 0"
          type="success"
          style="margin-right: 2px"
          >不纳入需求</el-tag
        >
        <el-tag
          v-if="showDnOrderPendingReasonTag(row)"
          style="margin-right: 2px"
          >交货挂起</el-tag
        >
        <span>{{ row.sketchOrderItemNo || row.idx || '-' }}</span>
      </template>
      <template #idx="{ row }">
        <el-tag
          v-if="row.notAcceptDemandReasons?.length > 0"
          type="success"
          style="margin-right: 2px"
          >不纳入需求</el-tag
        >
        <el-tag
          v-if="showDnOrderPendingReasonTag(row)"
          style="margin-right: 2px"
          >交货挂起</el-tag
        >
        <span>{{ row.idx || '-' }}</span>
      </template>
      <template #soNo="{ row }">
        <el-button
          v-if="row.soNo"
          type="primary"
          text
          @click="jumpToOuter('soNo', row)"
          >{{ row.soNo }}</el-button
        >
      </template>
      <template #validateResult="{ row }">
        <el-scrollbar max-height="80px">
          <div
            v-for="item in row.validatorResultDTOList"
            :key="item.validateName"
          >
            <el-tooltip :content="item.validateMsg" placement="top">
              <p>{{ item.validateName }}</p>
            </el-tooltip>
          </div>
        </el-scrollbar>
      </template>
      <template #unifyApprovalNo="{ row }">
        <el-button
          v-if="row.unifyApprovalNo"
          type="primary"
          text
          @click="jumpToOuter('unifyApprovalNo', row)"
          >{{ row.unifyApprovalNo }}</el-button
        >
      </template>
      <template #skuNo="{ row }">
        <div v-for="item in row.skuAttributeTags" :key="item" class="tag-info">
          <span>{{ item }}</span>
        </div>
        <span class="ml-5px">{{ row.skuNo || '-' }}</span>
      </template>
      <template #factoryHeader>
        <span>
          <OptRequiredRowTitle
            title="工厂"
            :is-required="true"
            tooltip="工厂置灰无法选择原因：该商品在该工厂下未配置税率"
            class="inline-block"
          />
        </span>
      </template>
      <template #factory="{ row, rowIndex, column }">
        <el-select
          v-model="row.factory"
          :class="{ tableCellError: !row.factory && row.skuNo }"
          class="w-full custom-class"
          placeholder="请选择"
          :disabled="isItemDisabled(row, column)"
          @change="(val: any) => changeRow(val, rowIndex, 'factory')"
        >
          <el-option
            v-for="item in factoryList"
            :key="item.code"
            :label="(item.code !== '-1' ? item.code : '') + item.name"
            :value="item.code"
            :disabled="isFactoryDisable(row, item.code)"
          />
        </el-select>
      </template>
      <template #appointSapBatch="{ row, rowIndex, column }">
        <el-input
          v-model="row.appointSapBatch"
          placeholder="请输入批次"
          clearable
          :disabled="isItemDisabled(row, column)"
          :class="{
            tableCellError: !!column.params?.required && !row.appointSapBatch,
          }"
          @change="(val: any) => changeRow(val, rowIndex, 'appointSapBatch')"
        />
      </template>
      <template #quantityHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '数量'"
          :is-required="true"
        />
      </template>
      <template #quantity="{ row, rowIndex, column }">
        <el-input-number
          v-model="row.quantity"
          :class="{ tableCellError: !row.quantity || row.quantity === 0 }"
          class="custom-class w-full"
          :min="0"
          :max="computedMax(row, column)"
          :step="1"
          :disabled="isItemDisabled(row, column)"
          @change="(val: any) => changeRow(val, rowIndex, 'quantity')"
        />
      </template>

      <template #suggestPrice="{ row }">
        <span>{{ formatPrice(row.suggestPrice, 6) }}</span>
      </template>

      <!-- 含税单价 -->
      <template #taxPriceHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '含税单价'"
          :tooltip="column.params?.tooltip"
          :is-required="
            isTaxedCustomer && !isFreeOrder(orderType) && !isForecast
          "
        />
      </template>

      <template #taxPrice="{ row, rowIndex, column }">
        <template v-if="isTaxedCustomer">
          <el-popover
            v-if="orderDataInfo.hasUnderApproval"
            placement="top"
            title=""
            width="400"
            trigger="hover"
          >
            <span>存在正在审批中的审批单，不支持修改含税单价！</span>
            <template #reference>
              <span>{{ formatPrice(row.taxPrice) }}</span>
            </template>
          </el-popover>
          <el-input-number
            v-else
            v-model="row.taxPrice"
            class="w-full custom-class"
            :class="{
              tableCellError:
                orderType !== 'Z005' &&
                !isFreeOrder(orderType) &&
                !(row.taxPrice > 0),
            }"
            :min="0"
            :precision="6"
            :step="1"
            :disabled="isFreeOrder(orderType) || isItemDisabled(row, column)"
            @change="(val: any) => changeRow(val, rowIndex, 'taxPrice')"
          />
        </template>
        <span v-else>{{ formatPrice(row.taxPrice) }}</span>
      </template>

      <!-- 未税单价 -->
      <template #freeTaxPriceHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '未税单价'"
          :tooltip="column.params?.tooltip"
          :is-required="
            !isTaxedCustomer && !isFreeOrder(orderType) && !isForecast
          "
        />
      </template>

      <template #freeTaxPrice="{ row, rowIndex, column }">
        <span v-if="isTaxedCustomer">{{ formatPrice(row.freeTaxPrice) }}</span>
        <template v-else>
          <el-popover
            v-if="orderDataInfo.hasUnderApproval"
            placement="top"
            title=""
            width="400"
            trigger="hover"
          >
            <span>存在正在审批中的审批单，不支持修改未税单价！</span>
            <template #reference>
              <span>{{ formatPrice(row.freeTaxPrice) }}</span>
            </template>
          </el-popover>
          <el-input-number
            v-else
            v-model="row.freeTaxPrice"
            class="w-full custom-class"
            :class="{
              tableCellError:
                orderType !== 'Z005' &&
                !isFreeOrder(orderType) &&
                row.freeTaxPrice <= 0 &&
                row.relateType !== 1,
            }"
            :min="0"
            :precision="6"
            :step="1"
            :disabled="isFreeOrder(orderType) || isItemDisabled(row, column)"
            @change="(val: any) => changeRow(val, rowIndex, 'freeTaxPrice')"
          />
        </template>
      </template>

      <template #freeTaxSaleAssessmentUnitPrice="{ row, rowIndex, column }">
        <el-input-number
          v-model="row.freeTaxSaleAssessmentUnitPrice"
          class="w-full custom-class"
          :class="{
            tableCellError:
              (column.params?.required &&
                isNull(row.freeTaxSaleAssessmentUnitPrice)) ||
              isFreeTaxSaleAssessmentUnitPriceError(
                row.factory,
                row.freeTaxSaleAssessmentUnitPrice
              ),
          }"
          :min="0"
          :precision="6"
          :step="1"
          :disabled="isItemDisabled(row, column)"
          @change="
            (val: any) =>
              changeRow(val, rowIndex, 'freeTaxSaleAssessmentUnitPrice')
          "
        />
      </template>

      <template #sku90Turnover="{ row, rowIndex, column }">
        <el-input-number
          v-model="row.sku90Turnover"
          class="w-full custom-class"
          :class="{
            tableCellError:
              column.params?.required && isNull(row.sku90Turnover),
          }"
          :min="0"
          :step="1"
          :disabled="isItemDisabled(row, column)"
          @change="(val: any) => changeRow(val, rowIndex, 'sku90Turnover')"
        />
      </template>

      <template #taxRate="{ row }">
        <span>{{ formatTaxRate(row) }}</span>
      </template>

      <template #formatPrice="{ row, column }">
        <span>{{ formatPrice(row[column.field], 6) }}</span>
      </template>

      <template #promotionalDiscountRate="{ row, rowIndex, column }">
        <div class="flex justify-around items-center">
          <el-input-number
            v-model="row.promotionalDiscountRate"
            placeholder="请输入折扣"
            :min="0"
            :max="100"
            :precision="2"
            :controls="false"
            clearable
            style="max-width: 80%"
            :disabled="isItemDisabled(row, column)"
            :class="{
              tableCellError:
                !!column.params?.required &&
                isNull(row.promotionalDiscountRate),
            }"
            @change="
              (val: any) => changeRow(val, rowIndex, 'promotionalDiscountRate')
            "
          />
          <span>%</span>
        </div>
      </template>

      <template #directDeliverySupplierHeader="{ column }">
        <OptRequiredRowTitle :title="column.title || '选择直发'" is-required />
      </template>

      <template #directDeliverySupplier="{ row, rowIndex, column }">
        <el-select
          v-model="row.directDeliverySupplier"
          :class="{ tableCellError: !row.directDeliverySupplier && row.skuNo }"
          class="custom-class"
          placeholder="请选择"
          :disabled="disabledDirectDeliverySupplier(row, column)"
          @change="
            (val: any) => changeRow(val, rowIndex, 'directDeliverySupplier')
          "
        >
          <el-option
            v-for="item in directDeliverySupplierList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </template>

      <template #positionHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '库位'"
          :is-required="true"
        />
      </template>

      <template #position="{ row, rowIndex, column }">
        <el-select-v2
          v-model="row.position"
          :options="
            formatSelectV2Options(
              getPositionList(row, row.factory, orderType, positionListMap)
            ) || []
          "
          :class="{ tableCellError: !row.position && row.skuNo }"
          filterable
          clearable
          class="w-full text-left text-13px"
          :disabled="disabledPosition(row, column)"
          placeholder="请选择"
          @change="(val: any) => changeRow(val, rowIndex, 'position')"
        >
          <template v-if="row.directDeliverySupplier === '0'" #empty>
            <div class="text-center">
              <p class="m-t-10px color-#808080">无匹配数据</p>
              <p class="m-10px m-b-0 color-#808080">
                所选仓不在仓网内，如有疑问请联系IT
              </p>
            </div>
          </template>
        </el-select-v2>
      </template>

      <template #sysDeliveryDate="{ row }">
        <el-popover
          placement="top-start"
          trigger="hover"
          :content="`原始标准发货日期：${row.originSkuDeliveryDate || ''}`"
          width="200px"
        >
          <template #reference>
            <span>{{ row.sysDeliveryDate }}</span>
          </template>
        </el-popover>
      </template>

      <template #skuArrivalDateHeader>
        <el-tooltip
          class="item"
          effect="dark"
          content="点击更新标准送达日期"
          placement="top"
        >
          <el-button type="primary" text @click="getDeliveryDate">
            <el-icon><Refresh /></el-icon>
            <span class="font-size-13px">标准送达日期</span>
          </el-button>
        </el-tooltip>
      </template>

      <template #skuArrivalDate="{ row }">
        <el-popover
          placement="top-start"
          trigger="hover"
          :content="`原始标准送达日期：${row.originSkuArrivalDate || ''}`"
          width="200px"
        >
          <template #reference>
            <span>{{ row.skuArrivalDate }}</span>
          </template>
        </el-popover>
      </template>

      <template #customerDateHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '客户期望送达日期'"
          :is-required="isForecast"
        />
      </template>

      <template #customerDate="{ row, rowIndex, column }">
        <el-date-picker
          v-model="row.customerDate"
          :disabled="!autoBatching || isItemDisabled(row, column)"
          :disabled-date="disabledDate"
          type="date"
          class="custom-class"
          style="width: 100%"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          @change="(val: any) => changeRow(val, rowIndex, 'customerDate')"
          @focus="focusDatePicker"
        />
      </template>

      <!-- <template #refuseSystemDeliveryDateHeader="{ row }">
        <el-checkbox
          :disabled="!autoBatching || isItemDisabled(row)"
          class="text-13px"
          @change="
            (val: any) =>
              changeColumnCheckbox(
                val,
                'refuseSystemDeliveryDate' as ItemRowType.refuseSystemDeliveryDate
              )
          "
        >
          是否接受标期
        </el-checkbox>
      </template>
      <template #refuseSystemDeliveryDate="{ row, rowIndex }">
        <el-checkbox
          v-model="row.refuseSystemDeliveryDate"
          :disabled="!autoBatching || isItemDisabled(row)"
          class="text-12px font-normal"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          @change="
            (val: any) => changeRow(val, rowIndex, 'refuseSystemDeliveryDate')
          "
        >
          不接受标期
        </el-checkbox>
      </template> -->
      <template #costCenterHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '成本中心'"
          :is-required="isInnerOrderReason(orderReason)"
        />
      </template>

      <template #recentDeliveryDate="{ row, column }">
        <el-date-picker
          v-model="row.recentDeliveryDate"
          type="date"
          :disabled="isItemDisabled(row, column)"
          class="custom-class"
          style="width: 100%"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
        />
      </template>

      <template #recentDeliveryDateHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '最近客户期望送达日期'"
          is-required
        />
      </template>

      <template #deliveryFrequency="{ row, column }">
        <el-input-number
          v-model="row.deliveryFrequency"
          placeholder="请输入"
          class="custom-class"
          :min="0"
          :precision="0"
          :disabled="isItemDisabled(row, column)"
        />
      </template>

      <template #deliveryFrequencyHeader="{ column }">
        <OptRequiredRowTitle :title="column.title || '发货频次'" is-required />
      </template>

      <template #deliveryCycle="{ row, column }">
        <el-input
          v-model="row.deliveryCycle"
          :disabled="isItemDisabled(row, column)"
          placeholder="请输入"
        />
      </template>

      <template #deliveryCycleHeader="{ column }">
        <OptRequiredRowTitle :title="column.title || '发货周期'" is-required />
      </template>

      <template #costCenter="{ row, rowIndex, column }">
        <el-select-v2
          v-model="row.costCenter"
          :options="costCenterList"
          :class="{
            tableCellError: !row.costCenter && isInnerOrderReason(orderReason),
          }"
          class="custom-class w-full text-left text-13px"
          filterable
          value-key="costCenter"
          placeholder="请选择"
          :disabled="isItemDisabled(row, column)"
          @change="(val: any) => changeRow(val, rowIndex, 'costCenter')"
        />
      </template>

      <template #generalLedgerAccountHeader="{ column }">
        <OptRequiredRowTitle
          :title="column.title || '总账科目'"
          :is-required="isInnerOrderReason(orderReason)"
        />
      </template>

      <template #generalLedgerAccount="{ row, rowIndex, column }">
        <el-select
          v-model="row.generalLedgerAccount"
          :class="{
            tableCellError:
              !row.generalLedgerAccount && isInnerOrderReason(orderReason),
          }"
          class="custom-class"
          filterable
          :disabled="isItemDisabled(row, column)"
          value-key="value"
          placeholder="请选择"
          @change="
            (val: any) => changeRow(val, rowIndex, 'generalLedgerAccount')
          "
        >
          <el-option
            v-for="item in mmDictList.generalLedger"
            :key="item.value"
            :label="`${item.value} ${item.name}`"
            :value="item.value"
          />
        </el-select>
      </template>

      <template #manage="{ row, rowIndex, column }">
        <el-button
          v-if="
            getButtonAuth(
              '销售跟单',
              `${isDraftDetail ? '草稿' : '创建'}_查看商品行详情`
            )
          "
          text
          type="primary"
          size="small"
          @click="showSkuDetail(rowIndex)"
          >详情</el-button
        >
        <el-button
          v-if="
            row.itemEditable !== '0' &&
            column.params?.deleteVisible !== false &&
            getButtonAuth(
              '销售跟单',
              `${isDraftDetail ? '草稿' : '创建'}_删除商品行详情`
            )
          "
          text
          type="primary"
          size="small"
          class="!m-l--5px"
          @click="removeSku(rowIndex)"
          >删除</el-button
        >
      </template>
    </vxe-grid>
    <ProductDetail />
  </div>
</template>

<style lang="scss" scoped>
.order-sale-draft-table {
  .title-group {
    text-align: right;
  }
}
.tag-info {
  display: inline;
  span {
    background-color: #f56c6c;
    border-radius: 11px;
    color: #fff;
    padding: 3px;
  }
}
</style>

<style lang="scss">
.tableCellError {
  border: 1px solid #ff4949;
  border-radius: 4px;
}
.tableCellError .el-select-v2__wrapper {
  border: 0;
}

.tableCellError .el-input,
.tableCellError .el-input__wrapper.is-focus,
.tableCellError .el-input__wrapper {
  box-shadow: none !important;
}
.custom-class .el-input__inner {
  font-size: 13px !important;
}
.order-sale-draft-table {
  .el-checkbox__label {
    font-size: 12px;
  }
}
.star .vxe-cell--title::before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
</style>
