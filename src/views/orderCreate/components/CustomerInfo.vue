<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { isEmpty, uniq } from 'lodash';
import {
  type Customer,
  type SaleOrgList,
  type SaleOrgOptions,
  useCustomerStore,
} from '@/stores/customer';
import { useCommonStore } from '@/stores/common';
import { useOrderStore } from '@/stores/order';
import { useItemStore } from '@/stores/item';
import { useInvoiceStore } from '@/stores/invoice';
import { useDeliveryStore } from '@/stores/delivery';
import ContactInfo from '@/views/orderCreate/components/ContactInfo.vue';
import { searchClients } from '@/api/maintainment';
import { exchange, getClientDetail, getContactById } from '@/api/order';
import { getFactoryList } from '@/utils/item';
import { DraftOrderData } from '@/types/common';
import bus from '@/utils/bus';
import { initFieldsShow, isPro } from '@/utils/index';
import request from '@/utils/request';
import { orderBasisToOrderReason } from '@/constants/order';
import type { ItemType } from '@/types/item';

const route = useRoute();

type cbFunc = () => void;
type ChangeValue = Customer | string | SaleOrgList | boolean;
type Options = {
  key: string;
  value: string;
};

const props = defineProps({
  formatBoolean: { type: Function, default: () => {} },
  queryContact: { type: Function, default: () => {} },
  initContactStore: { type: Function, default: () => {} },
});
const customerStore = useCustomerStore();
const commonStore = useCommonStore();
const orderStore = useOrderStore();
const itemStore = useItemStore();
const invoiceStore = useInvoiceStore();
const deliveryStore = useDeliveryStore();

const {
  orderData,
  isDraftDetail,
  isFakeSketch,
  companyCode,
  isHeaderDisabled,
  categoryCode,
  isSAP814,
} = storeToRefs(orderStore);
const { dictList } = storeToRefs(commonStore);
const {
  customer,
  customerReferenceNo,
  creator,
  previousCustomer,
  cusDetail,
  selectedSalesRange,
  // customerDateSensitive,
  // customerDateSensitiveIndeterminate,
  saleOrgList,
} = storeToRefs(customerStore) as any;
const { itemList } = storeToRefs(itemStore);
const vflag = computed(() => customerStore.cusDetail?.vflag);
const distributionChannelName = computed(
  () => customerStore.cusDetail?.saleOrgVO?.distributionChannelName
);
const distributionChannel = computed({
  get: () => customerStore.cusDetail?.saleOrgVO?.distributionChannel,
  set: (val) =>
    customerStore.editStore({
      cusDetail: {
        ...cusDetail.value,
        saleOrgVO: {
          ...cusDetail.value?.saleOrgVO,
          distributionChannel: val,
        },
      },
    }),
});

const saleMap = computed(() => {
  let ret: Options[] = [];
  if (customer.value && customer.value.saleMap) {
    ret = Object.keys(customer.value.saleMap).map((key) => {
      const value = customer.value.saleMap[key];
      return { key, value };
    });
  }
  console.log(ret);
  return ret;
});
const draftDetailSaleRange = computed(() => {
  const {
    salesOrganization,
    distributionChannel,
    productGroup,
    salesOrganizationName,
    distributionChannelName,
    productGroupName,
  } = customerStore.cusDetail?.saleOrgVO || {};
  let ret = '';
  if (salesOrganization) ret += `${salesOrganization}/`;
  if (distributionChannel) ret += `${distributionChannel}/`;
  if (productGroup) ret += `${productGroup} `;
  if (salesOrganizationName) ret += `${salesOrganizationName} `;
  if (distributionChannelName) ret += `${distributionChannelName} `;
  if (productGroupName) ret += `${productGroupName} `;
  return ret;
});

const isCustomerAvailable = (customer: Customer) => {
  if (customer && companyCode.value && companyCode.value.length > 2) {
    const { saleOrgList } = customer;
    return saleOrgList?.find(
      (item: SaleOrgList) =>
        item.salesOrganization &&
        item.salesOrganization.length >= 2 &&
        item.salesOrganization.slice(0, 2) === companyCode.value.slice(0, 2)
    );
  }
  return false;
};

const emit = defineEmits(['updateCustomerEdit']);

const state = reactive<{
  prefix: string;
  loadingCustomer: boolean;
  num: number;
  customerList: Customer[];
}>({
  prefix: 'https://zkh360-boss.oss-cn-beijing.aliyuncs.com/so/currency/',
  loadingCustomer: false,
  num: 0,
  customerList: [],
});
const viewCustomerDetail = () => {
  if (Object.keys(customer.value).length === 0) {
    ElMessage.warning({
      message: '请选择客户',
    });
    return;
  }
  emit('updateCustomerEdit', true);
};

const customerRef = ref();
const contactRef = ref();

const queryCustomerList = async (query: string, cb: cbFunc) => {
  state.loadingCustomer = true;
  state.num++;
  const a = state.num;
  try {
    const result = await searchClients(query);
    state.loadingCustomer = false;
    if (a === state.num && result && result.code === 200) {
      state.customerList = [
        {
          customerNumber: '客户编码',
          customerName: '客户名称',
          cityName: '城市',
        },
        ...result.data,
      ];
    }
    cb && cb();
  } catch (error) {
    console.log(error);
  }
};

const handleCurrencyChange = async (data: any) => {
  let currencySymbol = '';
  let rate = 1;
  if (dictList.value && dictList.value.currencySymbol) {
    const findOne: any =
      dictList.value.currencySymbol.find((item) => item.parentCode === data) ||
      {};
    if (findOne) {
      currencySymbol = findOne.code;
    }
    if (findOne && findOne.parentCode !== 'CNY') {
      const res = await exchange({
        sourceCurrency: findOne.parentCode,
        targetCurrency: 'CNY',
      });
      if (res?.code === 200 && res.data) {
        rate = res.data.rate;
      } else {
        ElMessage.error(res.msg);
      }
    }
  }
  customerStore.editStore({
    customer: {
      ...customer.value,
      currency: data,
      currencySymbol,
      exchangeRate: rate,
    },
  });
};

const handleCustomerDateSensitiveChange = (val: boolean) => {
  const itemList = itemStore.itemList?.forEach((item: ItemType) => {
    item.customerDateSensitive = val;
  });
  itemStore.editStore({
    itemList,
  });
  customerStore.editStore({
    customerDateSensitiveIndeterminate: false,
  });
};

const resetOrderData = () => {
  customerStore.editStore({
    customer: {
      currency:
        companyCode.value === '2400' || companyCode.value === '3100'
          ? 'USD'
          : 'CNY',
      currencySymbol:
        companyCode.value === '2400' || companyCode.value === '3100'
          ? '$'
          : '￥',
    },
    customerDateSensitive: false,
    cusDetail: {
      paymentTerm: '',
      sellerMap: {},
      vflag: false,
      customerServiceMap: {},
      saleOrgVO: {},
    },
    contactData: {
      receiverContact: null,
      receiverContactList: [],
      receiverPhone: '',
      receiverAddress: '',
      orderContact: null,
      orderContactList: [],
      orderContactPhone: '',
    },
  });
  distributionChannel.value = '';
  selectedSalesRange.value = {};
  invoiceStore.clearInvoiceData();
  deliveryStore.clearDeliveryData();
};

const dispatchChangeCustomer = () => {
  orderStore.editStore({
    orderData: {},
  });
  itemStore.clearItemList();
  invoiceStore.initInvoiceData({});
  deliveryStore.initDeliveryData({});
};

const checkSelectWhsAuth = (customer: Customer) => {
  const { salesOrganization } = customerStore.cusDetail?.saleOrgVO || {};
  orderStore.checkSelectWhsAuth({
    customerNo: customer?.customerNumber || '',
    orderBasis: orderData.value.orderBasis || '',
    orderReason: deliveryStore.deliveryData?.orderReason || '',
    orderType: categoryCode.value,
    salesOrg: salesOrganization,
    orderSource: orderData.value.orderSource || '',
  });
};
const changeCustomer = async (val: Customer) => {
  if (itemList.value?.length > 0) {
    ElMessageBox.confirm('此操作将删除所选商品，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        customerRef.value.blur();
        resetOrderData();
        dispatchChangeCustomer();
        customerStore.editStore({
          customer: val,
        });
      })
      .catch(() => {
        customerRef.value.blur();
        customerStore.editStore({
          customer: previousCustomer.value,
        });
      });
  } else {
    console.log(val);
    resetOrderData();
    await customerStore.editStore({
      previousCustomer: { ...val },
      customer: { ...val },
    });
    dispatchChangeCustomer();
  }
};

const handleRefDate = (date: string) => {
  itemStore.updateItemProps({ customerDate: date });
};

const setRowCustomerDate = () => {
  // 特殊情况整单设置项目行日期, 第一行没有日期才走这个逻辑
  const { customerReferenceDate } = orderData.value;
  if (itemList.value && itemList.value.length > 0) {
    console.log('特殊情况整单设置项目行日期');
    handleRefDate(customerReferenceDate || '');
  }
};

const initStoreInfo = (data: DraftOrderData) => {
  orderStore.editStore({
    orderData: {
      ...orderData.value,
      ...data,
      orderType: categoryCode.value,
    },
  });
  props.formatBoolean(data);
  customerStore.editStore({
    cusDetail: {
      ...cusDetail.value,
      ...data,
      paymentTerm: data.paymentTermCode,
    },
  });
  invoiceStore.initInvoiceData(data);
  deliveryStore.initDeliveryData(data);
  deliveryStore.updateDeliveryData({
    specifiedReceiptDayOfWeek: orderData.value.specifiedReceiptDayOfWeek
      ?.split(',')
      ?.filter(Boolean),
    receiptTimeCategory: orderData.value.receiptTimeCategory === 'X',
  });
  const factoryList = getFactoryList(selectedSalesRange.value, dictList.value);
  itemStore.editStore({
    factoryList,
  });
  if (orderData.value.autoBatching !== 'X') {
    setRowCustomerDate();
  }
};

const initCostCenter = () => {
  const { salesOrganization } = customerStore.cusDetail?.saleOrgVO || {};
  if (categoryCode.value === 'Z007') {
    commonStore.queryCostCenter(salesOrganization);
  }
};

const changeSalesRange = async (range: SaleOrgList) => {
  if (range) {
    let { salesOrganization, productGroup, distributionChannel } = range;
    if (itemList.value && itemList.value.length > 0) {
      const goodsItem = itemList.value.find((item: ItemType) => item.skuNo);
      productGroup = goodsItem?.productGroup;
    }
    if (customer.value) {
      orderStore.editStore({
        pageLoading: true,
      });
      const { customerNumber } = customer.value;
      try {
        const res = await getClientDetail(
          customerNumber,
          distributionChannel,
          productGroup,
          salesOrganization,
          customerStore.contactData?.receiverContact?.contactId || ''
        );
        if (res?.code === 200 && res?.data) {
          await initStoreInfo(res.data);
          checkSelectWhsAuth(customer.value);
        } else {
          ElMessage.error(res.data || res.msg || '获取客户详情失败');
        }
        const contacts = await props.queryContact('');
        props.initContactStore(orderData.value, contacts);
        initCostCenter();
      } catch (error) {
        console.log(error);
      } finally {
        orderStore.editStore({
          pageLoading: false,
        });
      }
    }
  }
};
const selectChange = (val: ChangeValue, type: string) => {
  switch (type) {
    case 'customer':
      changeCustomer(val as Customer);
      break;
    case 'distributionChannel':
      selectedSalesRange.value = {};
      distributionChannel.value = val;
      break;
    case 'selectedSalesRange':
      changeSalesRange(val as SaleOrgList);
      break;
    case 'customerDateSensitive':
      handleCustomerDateSensitiveChange(val as boolean);
      break;
  }
};

const initBySAP814 = async () => {
  const { orderSource, orderBasis = '', deliveryNos = '' } = route.query || {};
  const { categoryCode } = route.params;
  if (orderSource !== 'SAP814') return;
  const orderReason = orderBasisToOrderReason[orderBasis as string] || '';
  deliveryStore.updateDeliveryData({ orderReason });
  const SAP814Info = JSON.parse(
    localStorage.getItem(`SAP_814_ORDER_${deliveryNos}`) || '{}'
  );
  localStorage.removeItem(`SAP_814_ORDER_${deliveryNos}`);
  if (Object.keys(SAP814Info).length === 0) {
    return;
  }
  console.log('SAP814Info', SAP814Info);
  const {
    customer: { customerNumber: customerNo },
    deliveryItemNos,
  } = SAP814Info;
  try {
    const res = await request({
      url: `/api-opc/v1/so/template/queryOriginOrder/deliveryNo`,
      method: 'post',
      data: {
        deliveryNos: (deliveryNos as string).replaceAll(/\s+|,|，|_/g, ','),
        orderCustomer: customerNo,
        orderType: categoryCode,
        orderBasis,
        querySkuDetail: '1', // 是否查询sku详情
      },
    });
    if (res.code === 200 && res.data) {
      const {
        distributionChannel,
        saleOrganization: salesOrganization,
        items,
      } = res.data;
      const queryCustomerCallback = async () => {
        const findCustomer = state.customerList.find(
          (customer) => customer.customerNumber === customerNo
        );
        if (findCustomer) {
          await changeCustomer(findCustomer);
        }
        if (distributionChannel) {
          await selectChange(
            distributionChannel as string,
            'distributionChannel'
          );
        }
        // 产品组默认00
        const key = `${salesOrganization}_00_${distributionChannel}`;
        let saleList = saleOrgList.value;
        if (saleList.length === 0) {
          saleList = findCustomer?.saleOrgList?.map(
            (item: SaleOrgList, idx: number) => {
              const {
                salesOrganization,
                productGroup,
                distributionChannel,
                salesOrganizationName,
                distributionChannelName,
                productGroupName,
              } = item;
              return {
                data: {
                  idx,
                  ...item,
                },
                key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
                value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`,
              };
            }
          );
        }
        const findRange = saleList?.find(
          (item: SaleOrgOptions) => item.key === key
        );
        deliveryStore.updateDeliveryData({ orderReason });
        orderStore.editStore({
          orderData: {
            ...orderData.value,
            orderBasis,
          },
        });
        if (findRange) {
          selectedSalesRange.value = findRange.data;
          await changeSalesRange(findRange.data);
        }
        const skukuInfoVOList: ItemType[] = items.reduce(
          (pre: ItemType[], cur: ItemType) => {
            const key = `${cur.deliveryNo}_${cur.deliveryItemNo}`;
            if (deliveryItemNos.includes(key)) {
              const sku = {
                ...cur,
                ...cur.skuInfoDetailVO,
                // factory: cur.factory,
                quantity: cur.deliveryAmount || 0,
                directDeliverySupplier: '0', // 默认震坤行发货
                // position: '1004', // 默认1004
                referenceOrderNo: cur.deliveryNo, // SAP订单号
                referenceOrderItemNo: cur.deliveryItemNo, // SAP行号
              };
              delete sku.skuInfoDetailVO;
              pre.push(sku);
            }
            return pre;
          },
          []
        );
        console.log(skukuInfoVOList);
        await itemStore.importSAP814Goods(skukuInfoVOList);
        setTimeout(async () => {
          bus.emit('updateCheckbox', {
            rows: itemStore.selectedItemList,
            checked: true,
          });
          // await itemStore.getDeliveryDate();
          await customerStore.changeClientDetail(true);
        });
      };
      queryCustomerList(customerNo as string, queryCustomerCallback);
    }
  } catch (error) {
    console.log(error);
  }
};
const initBySH = () => {
  const {
    customerNo, // 客户编码
    salesOrganization, // 销售范围
    distributionChannel, // 分销渠道
    productGroup, // 产品组
    receiverContact, // 收货联系人
    receivingInvoiceContact, // 收票联系人
    orderContact, // 订单联系人
    skuInfoList, // sku编码可能有多个？
    quantity,
    directSupplierType, // 直发类型
    position,
    omsNo, // 外围系统订单号
    customerReferenceNo, // 客户订单号
    orderSource,
  } = route.query || {};
  if (orderSource !== 'SH') return;
  orderStore.editStore({
    orderData: {
      ...orderData.value,
      orderSource: 'SH',
      orderNo: omsNo,
    },
  });
  console.log(
    distributionChannel,
    customerNo,
    salesOrganization,
    receivingInvoiceContact,
    productGroup,
    receiverContact,
    orderContact,
    skuInfoList,
    quantity,
    directSupplierType,
    position,
    omsNo,
    customerReferenceNo,
    orderSource
  );
  const queryCustomerCallback = async () => {
    const findCustomer = state.customerList.find(
      (customer) => customer.customerNumber === customerNo
    );
    if (findCustomer) {
      await changeCustomer(findCustomer);
    }
    if (customerReferenceNo) {
      customerStore.editStore({
        customerReferenceNo,
      });
      orderStore.editStore({
        orderData: {
          ...orderData.value,
          customerReferenceNo,
        },
      });
    }
    if (distributionChannel) {
      await selectChange(distributionChannel as string, 'distributionChannel');
    }
    const key = `${salesOrganization}_${productGroup}_${distributionChannel}`;
    let saleList = saleOrgList.value;
    if (saleList.length === 0) {
      saleList = findCustomer?.saleOrgList?.map(
        (item: SaleOrgList, idx: number) => {
          const {
            salesOrganization,
            productGroup,
            distributionChannel,
            salesOrganizationName,
            distributionChannelName,
            productGroupName,
          } = item;
          return {
            data: {
              idx,
              ...item,
            },
            key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
            value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`,
          };
        }
      );
    }
    const findRange = saleList?.find(
      (item: SaleOrgOptions) => item.key === key
    );
    if (findRange) {
      selectedSalesRange.value = findRange.data;
      await changeSalesRange(findRange.data);
    }
    if (receiverContact) {
      const a1 = async () => {
        let findContact = customerStore.contactData.receiverContactList?.find(
          (item) => item.contactId === receiverContact
        );
        if (isEmpty(findContact)) {
          const res = await getContactById({ contactId: receiverContact });
          if (res?.code === 200 && res?.data) {
            findContact = res.data;
          }
        }
        if (findContact) {
          contactRef.value.changeContact(findContact);
        }
      };
      await a1();
    }
    if (receivingInvoiceContact) {
      const a2 = async () => {
        let findContact =
          invoiceStore.invoiceData.receivingInvoiceContactList?.find(
            (item) => item.contactId === receivingInvoiceContact
          );
        if (isEmpty(findContact)) {
          const res = await getContactById({
            contactId: receivingInvoiceContact,
          });
          if (res?.code === 200 && res?.data) {
            findContact = res.data;
          }
        }
        if (findContact) {
          contactRef.value.changeReceivingInvoiceContact(findContact);
        }
      };
      await a2();
    }
    if (orderContact) {
      const a3 = async () => {
        let findContact = customerStore.contactData.orderContactList?.find(
          (item) => item.contactId === orderContact
        );
        if (isEmpty(findContact)) {
          const res = await getContactById({ contactId: orderContact });
          if (res?.code === 200 && res?.data) {
            findContact = res.data;
          }
        }
        if (findContact) {
          contactRef.value.changeOrderContact(findContact);
        }
      };
      await a3();
    }
    if (skuInfoList) {
      importSkus(skuInfoList as string);
    }
  };
  queryCustomerList(customerNo as string, queryCustomerCallback);
};

const importSkus = async (skuInfoList: string) => {
  try {
    const uploadSkuInfoVOList = JSON.parse(skuInfoList);
    const res: any = await itemStore.uploadGoods({
      factoryList: itemStore.factoryList,
      skuVOList: uploadSkuInfoVOList,
    });
    const { result } = res;
    if (result && result.code === 200) {
      const { uploadSkuInfoDetailVOList, failSkuList } = result.data;
      const successCallback = () => {
        if (uploadSkuInfoDetailVOList && uploadSkuInfoDetailVOList.length > 0) {
          setTimeout(async () => {
            bus.emit('updateCheckbox', {
              rows: itemStore.selectedItemList,
              checked: true,
            });
            await itemStore.getDeliveryDate();
            await customerStore.changeClientDetail(true);
          });
        }
      };
      if (failSkuList && failSkuList.length > 0) {
        const failLine = failSkuList.length;
        const successLine = uploadSkuInfoDetailVOList.length;
        const skus = uniq(failSkuList).join(',');
        ElMessageBox.alert(
          `导入成功商品行数量为${successLine}行，失败数量为${failLine}行。` +
            `其中${skus}为无效商品或未维护建议销售价，无法添加!`,
          '错误',
          {
            type: 'error',
            confirmButtonText: '确定',
            callback: successCallback,
          }
        );
      } else {
        successCallback();
      }
    } else if (result && (result.data || result.msg)) {
      ElMessageBox.alert(result.data || result.msg, '错误', {
        confirmButtonText: '确定',
        type: 'error',
      });
    }
  } catch (error) {
    console.log(error);
  }
};

const getOaUrl = computed(() => {
  return isPro()
    ? `https://oa.zkh360.com/zkh/action/ViewRequest.jsp?requestmark=${orderData.value.oaProcessNo}`
    : `https://testoa3.zkh360.com/zkh/action/ViewRequest.jsp?requestmark=${orderData.value.oaProcessNo}`;
});

const showField = (prop?: string) => {
  return initFieldsShow('customerInfo', prop)?.visible;
};
const disabledField = (prop?: string) => {
  return initFieldsShow('customerInfo', prop)?.disabled;
};
onMounted(() => {
  initBySH();
  initBySAP814();
});
</script>

<template>
  <div>
    <el-row :gutter="20">
      <el-col v-if="showField('customer')" :span="12" class="customer-select">
        <el-form-item label="选择客户" prop="customer">
          <span v-if="isDraftDetail" class="customer-detail">
            <el-input
              disabled
              :model-value="customer?.customerName || customer?.customerNo"
            />
          </span>
          <el-select
            v-else
            ref="customerRef"
            v-model="customer"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="请输入客户编号/名称"
            class="flex-1"
            value-key="customerNumber"
            :disabled="isSAP814 || disabledField('customer')"
            :remote-method="queryCustomerList"
            :loading="state.loadingCustomer"
            @change="(val: any) => selectChange(val, 'customer')"
          >
            <el-option
              v-for="(item, index) in state.customerList"
              :key="item.customerId"
              :label="item.customerName"
              :value="item"
              :disabled="index === 0 || !isCustomerAvailable(item)"
            >
              <div
                class="ba-row-start selectClientItem"
                :class="index === 0 ? 'font-bold' : 'font-normal'"
              >
                <div>{{ item.customerNumber }}</div>
                <div>{{ item.cityName }}</div>
                <div>{{ item.customerName }}</div>
              </div>
            </el-option>
          </el-select>
          <el-link
            type="primary"
            class="p-l-5px"
            :underline="false"
            @click="viewCustomerDetail"
          >
            查看客户详情
            <el-icon class="vertical-mid m-l-2px">
              <View />
            </el-icon>
          </el-link>
          <div v-if="vflag" class="color-#ff4949">
            请在客户详情中修改售达方与送达方信息
          </div>
        </el-form-item>
      </el-col>
      <el-col v-if="showField('selectedSalesRange')" :span="12">
        <el-form-item label="销售范围" prop="selectedSalesRange">
          <el-input
            v-if="isDraftDetail"
            disabled
            :model-value="draftDetailSaleRange"
          />
          <el-select
            v-else
            v-model="selectedSalesRange"
            class="w-full"
            value-key="idx"
            placeholder="请选择销售范围"
            :disabled="disabledField('selectedSalesRange')"
            @change="(val) => selectChange(val, 'selectedSalesRange')"
          >
            <el-option
              v-for="item in saleOrgList"
              :key="item.key"
              :label="item.value"
              :value="item.data"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="showField('customerReferenceNo')" :span="12">
        <el-form-item label="客户订单号" prop="customerReferenceNo">
          <el-input
            v-if="isDraftDetail"
            v-model="customerReferenceNo"
            :disabled="disabledField('customerReferenceNo')"
          />
          <el-input
            v-else
            v-model="customerReferenceNo"
            clearable
            maxlength="35"
            placeholder="客户订单号"
            class="w-full"
            :disabled="disabledField('customerReferenceNo')"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="showField('distributionChannel')" :span="6">
        <el-form-item label="直/分销渠道" prop="distributionChannel">
          <el-input
            v-if="isDraftDetail"
            disabled
            :model-value="distributionChannelName"
          />
          <el-select
            v-else
            v-model="distributionChannel"
            placeholder="请选择"
            class="w-full"
            :disabled="disabledField('distributionChannel')"
            @change="(val: string) => selectChange(val, 'distributionChannel')"
          >
            <el-option
              v-for="item in saleMap"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="showField('currency')" :span="6">
        <el-form-item label="货币" prop="currency">
          <div class="currency-row">
            <el-select
              :model-value="customer.currency"
              placeholder="请选择"
              :disabled="isHeaderDisabled || disabledField('currency')"
              @change="handleCurrencyChange"
            >
              <el-option
                v-for="item in dictList['currencySymbol']"
                :key="item.parentCode"
                class="m-b-5px"
                :label="item.name"
                :value="item.parentCode"
              >
                <div class="current-option">
                  <img
                    :src="`${state.prefix + item.parentCode}.png`"
                    class="flag"
                  />
                  <span>{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
            <img
              v-if="customer.currency"
              :src="`${state.prefix + customer.currency}.png`"
              class="currency-flag"
            />
          </div>
          <div
            v-if="companyCode === '2400' || companyCode === '3100'"
            class="strong"
          >
            *请确认币种是否正确，创建订单后将无法修改币种
          </div>
        </el-form-item>
      </el-col>
      <template v-if="isDraftDetail">
        <el-col v-if="showField('orderSource')" :span="6">
          <el-form-item label="订单来源" prop="orderSource">
            <el-input v-model="orderData.orderSource" disabled />
          </el-form-item>
        </el-col>
        <el-col v-if="showField('subOrderSourceToShow')" :span="6">
          <el-form-item label="子订单来源" prop="subOrderSourceToShow">
            <el-input v-model="orderData.subOrderSourceToShow" disabled />
          </el-form-item>
        </el-col>
        <el-col v-if="showField('outerSourceOrderNo')" :span="12">
          <el-form-item label="上游原始单号" prop="outerSourceOrderNo">
            <el-input v-model="orderData.outerSourceOrderNo" disabled />
          </el-form-item>
        </el-col>
      </template>
      <el-row :gutter="20">
        <ContactInfo ref="contactRef" />
      </el-row>
      <template v-if="isDraftDetail">
        <el-col v-if="showField('creator')" :span="12">
          <el-form-item label="创建者" prop="creator">
            <el-input disabled :model-value="creator" />
          </el-form-item>
        </el-col>
        <el-col v-if="showField('sketchOrderNo')" :span="12">
          <el-form-item
            :label="isFakeSketch ? '订单号' : '草稿单号'"
            prop="sketchOrderNo"
          >
            <el-input v-model="orderData.sketchOrderNo" disabled />
          </el-form-item>
        </el-col>
        <el-col v-if="showField('oaProcessNo')" :span="12">
          <el-form-item
            v-if="orderData.oaProcessNo"
            label="OA流程编号"
            prop="oaProcessNo"
          >
            <el-link :href="getOaUrl" type="primary" target="_blank">
              {{ orderData.oaProcessNo }}
            </el-link>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.customer-select {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .customer-detail {
    flex: 1;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.ba-row-start {
  display: flex;
}

.currency-row {
  display: flex;
  .currency-flag {
    height: 32px;
    width: 40px;
    margin-left: 20px;
  }
}
.strong {
  color: #ff4949;
  font-size: 12px;
}
.current-option {
  display: flex;
  align-items: center;
  .flag {
    display: inline-block;
    height: 40px;
    width: 40px;
    margin-right: 5px;
  }
}
</style>
