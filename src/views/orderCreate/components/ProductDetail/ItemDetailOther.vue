<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from 'vue';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { findNormalItemInDictList, getDisabledDate } from '@/utils/item';

import { initFieldsShow } from '@/utils/index';
import { ItemType } from '@/types/item';
import { useOrderStore } from '@/stores/order';
import { useDeliveryStore } from '@/stores/delivery';
import { useCommonStore } from '@/stores/common';
// import type { IDetailData, ItemType, MaterialRelationRow } from '@/types/item';
import type { OrderFieldsSettings, SalesDictType } from '@/types/common';

const props = defineProps<{
  modelValue: ItemType;
}>();

const emit = defineEmits(['update:modelValue']);

const data = useVModel(props, 'modelValue', emit);

const orderStore = useOrderStore();
const deliveryStore = useDeliveryStore();
const commonStore = useCommonStore();

const { deliveryData } = storeToRefs(deliveryStore);
const { dictList, holidaysList } = storeToRefs(commonStore);
const { orderData } = storeToRefs(orderStore);

const orderType = computed(() => orderData.value.orderType || '');

const autoBatching = computed(() => deliveryData.value?.autoBatching);

const specifiedReceiptDayOfWeek = computed(() =>
  deliveryData.value.specifiedReceiptDayOfWeek?.filter(Boolean)
);
const receiptTimeCategory = computed(
  () => deliveryData.value?.receiptTimeCategory
);

const bidCustomer = computed(() => deliveryData.value.bidCustomer);
const disabledDate = (time: Date) => {
  const check =
    !['Z002', 'Z014'].includes(orderType.value) &&
    !['8', 'X'].includes(bidCustomer.value);
  return getDisabledDate(
    time,
    specifiedReceiptDayOfWeek.value,
    receiptTimeCategory.value,
    holidaysList.value,
    check
  );
};
const focusDatePicker = () => {
  if (
    (receiptTimeCategory.value === 'Z' ||
      receiptTimeCategory.value === false) &&
    specifiedReceiptDayOfWeek.value?.length &&
    specifiedReceiptDayOfWeek.value?.every(
      (item: string) => !['0', '01', '02', '03', '04', '05'].includes(item)
    )
  ) {
    ElMessage.warning(
      '无可选日期，请修改客户指定日收货或工作日与周末均可收货!'
    );
  }
};

const notAcceptDemandReasons = computed(() =>
  (data.value.notAcceptDemandReasons || []).filter((code: string) =>
    findNormalItemInDictList(dictList.value.notAcceptDemandReason, code)
  )
);
const dnOrderPendingReasons = computed(() =>
  (data.value.dnOrderPendingReasons || []).filter((code: string) =>
    findNormalItemInDictList(dictList.value.dnOrderPendingReason, code)
  )
);

const boolOptions = [
  { key: '1', value: '1', label: '是' },
  { key: '0', value: '0', label: '否' },
];

const fields = ref<OrderFieldsSettings[]>([]);
const getFields = () => {
  fields.value = initFieldsShow('productDetail', 'detailOther')?.children;
};

onMounted(() => {
  getFields();
});

const formatDictOption = (list: SalesDictType[]) => {
  return list.map((item) => ({
    label: item.name,
    value: item.code,
    key: item.code,
  }));
};
watchEffect(() => {
  fields.value?.forEach((item) => {
    if (item.prop === 'notAcceptDemandReason') {
      item.options = formatDictOption(dictList.value?.notAcceptDemandReason);
    } else if (item.prop === 'dnOrderPendingReason') {
      item.options = formatDictOption(dictList.value?.dnOrderPendingReason);
    }
  });
});
</script>

<template>
  <el-row :gutter="10">
    <el-col v-for="item in fields" :key="item.prop" :span="item.span || 12">
      <el-form-item :label="item.label" :prop="item.prop">
        <el-date-picker
          v-if="item.prop === 'customerDate'"
          v-model="data.customerDate"
          :disabled-date="disabledDate"
          :disabled="!autoBatching || item.disabled"
          value-format="YYYY-MM-DD"
          type="date"
          class="w-full"
          placeholder="选择日期"
          @focus="focusDatePicker"
        />
        <el-select
          v-else-if="item.prop === 'notAcceptDemandReason'"
          v-model="notAcceptDemandReasons"
          disabled
          multiple
        >
          <el-option
            v-for="opt in dictList['notAcceptDemandReason']"
            :key="opt.code"
            :label="opt.name"
            :value="opt.code"
          />
        </el-select>
        <el-select
          v-else-if="item.prop === 'dnOrderPendingReasons'"
          v-model="dnOrderPendingReasons"
          disabled
          multiple
        >
          <el-option
            v-for="opt in dictList['dnOrderPendingReason']"
            :key="opt.code"
            :label="opt.name"
            :value="opt.code"
          />
        </el-select>
        <el-input
          v-else-if="item.prop === 'stockProviderNo'"
          :disabled="item.disabled"
          :placeholder="`请选择${item.label}`"
          clearable
          :model-value="`${data.stockProviderNo || ''} ${data.stockProviderName || ''}`"
        />
        <el-select
          v-else-if="item.type === 'booleanSelect'"
          v-model="data[item.prop]"
          :placeholder="`请选择${item.label}`"
          clearable
          :disabled="item.disabled"
        >
          <el-option
            v-for="opt in boolOptions"
            :key="opt.key"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
        <el-select
          v-else-if="item.type === 'select'"
          v-model="data[item.prop]"
          :placeholder="`请选择${item.label}`"
          clearable
          :multiple="item.multiple"
          :disabled="item.disabled"
        >
          <el-option
            v-for="opt in item.options"
            :key="opt.key"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
        <el-checkbox
          v-else-if="item.type === 'checkbox'"
          v-model="data[item.prop]"
          :disabled="item.disabled"
        />
        <el-input
          v-else-if="item.prop === 'textarea'"
          v-model="data[item.prop]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :maxlength="item.maxLength || 200"
          :disabled="item.disabled"
          type="textarea"
          show-word-limit
        />
        <el-input
          v-else
          v-model="data[item.prop]"
          clearable
          :disabled="item.disabled"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :style="item.style || ''"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
