<script lang="ts" setup>
import { computed, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { orderRecordList } from '@/api/order';

import { useItemStore } from '@/stores/item';
import { useCustomerStore } from '@/stores/customer';

const itemStore = useItemStore();
const customerStore = useCustomerStore();
const selectedItem = computed(() => itemStore.selectedItem);
const customerNumber = computed(() => customerStore.customer?.customerNumber);

const emit = defineEmits(['close']);

const reactData = reactive({
  tableData: [],
  total: 0,
  current: 1,
  loading: false,
});
onMounted(() => {
  pageChange();
});
const pageChange = () => {
  if (customerNumber.value) {
    reactData.loading = true;
    orderRecordList(
      customerNumber.value,
      selectedItem.value?.skuNo,
      reactData.current
    )
      .then((result) => {
        if (result.code === 200 && result.data) {
          reactData.total = result.data.total;
          reactData.current = result.data.current;
          reactData.tableData = result.data.records;
        }
      })
      .finally(() => {
        reactData.loading = false;
      });
  } else {
    ElMessage.error({
      message: '客户编号为空！',
    });
  }
};
</script>

<template>
  <div v-loading="reactData.loading">
    <el-table
      size="small"
      border
      :data="reactData.tableData"
      class="w-full"
      max-height="600px"
    >
      <el-table-column
        prop="soNo"
        label="OMS订单号"
        width="100"
        align="center"
      />
      <el-table-column
        prop="sapOrderNo"
        label="SAP订单号"
        width="100"
        align="center"
      />
      <el-table-column
        prop="quantity"
        label="该商品数量"
        width="150"
        align="center"
      />
      <el-table-column
        prop="conditionType"
        label="含税/未税"
        width="150"
        align="center"
      />
      <el-table-column
        prop="unitPrice"
        label="单价"
        width="100"
        align="center"
      />
      <el-table-column prop="seller" label="销售" width="100" align="center" />
      <el-table-column
        prop="saleTime"
        label="订单时间"
        width="100"
        align="center"
      />
      <el-table-column
        prop="type"
        label="订单类型"
        width="100"
        align="center"
      />
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="reactData.current"
        background
        small
        layout="prev, pager, next"
        :total="reactData.total"
        @current-change="pageChange"
      />
    </div>

    <div class="btnGroup">
      <el-button plain type="primary" @click="emit('close')">关闭</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.btnGroup {
  margin: 20px 0;
  text-align: center;
}
</style>
