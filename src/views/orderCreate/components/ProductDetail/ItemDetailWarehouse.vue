<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import endsWith from 'lodash/endsWith';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import {
  formatSelectV2Options,
  getDirectDeliverySupplierList,
  getPositionList,
  updateSkuByChangeFactory,
} from '@/utils/item';

import { initFieldsShow } from '@/utils/index';
import {
  isEnableDirectDeliverySupplier as isEnableDirectDeliverySupplierApi,
  isForecastOrder,
  isSupportedAutoPosition,
} from '@/utils/orderType';
import {
  DirectDeliverySupplierEnum,
  ItemType,
  StockSkuInfo,
} from '@/types/item';
import { useItemStore } from '@/stores/item';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
// import type { IDetailData, ItemType, MaterialRelationRow } from '@/types/item';
import { queryStockSkuInfo } from '@/api/order';
import type {
  CompanyType,
  OrderFieldsSettings,
  SalesDictType,
} from '@/types/common';

const props = defineProps<{
  modelValue: ItemType;
}>();

const emit = defineEmits(['update:modelValue', 'changePrice']);

const data = useVModel(props, 'modelValue', emit);

const orderStore = useOrderStore();
const commonStore = useCommonStore();
const itemStore = useItemStore();
const customerStore = useCustomerStore();

const { dictList } = storeToRefs(commonStore);
const { orderData, allowSelectWhsAuth } = storeToRefs(orderStore);
const { selectedSalesRange, customer, cusDetail } = storeToRefs(customerStore);
const { positionListMap, factoryList } = storeToRefs(itemStore);

const orderType = computed(() => orderData.value.orderType || '');

const isTax = computed(() => cusDetail.value?.isTax);
const taxType = computed(() => cusDetail.value?.taxType);
const currency = computed(() => customer.value?.currency || '');
const exchangeRate = computed(() => customer.value?.exchangeRate);

const salesOrganization = computed(() => {
  if (selectedSalesRange.value) {
    return selectedSalesRange.value.salesOrganization;
  }
  return '';
});

const isForecast = computed(() => {
  return isForecastOrder(orderType.value);
});

const isEnableDirectDeliverySupplier = computed(() => {
  return isEnableDirectDeliverySupplierApi(orderType.value);
});
const directDeliverySupplierList = computed(() => {
  return getDirectDeliverySupplierList(
    dictList.value,
    orderType.value,
    salesOrganization.value
  );
});

const isFactoryDisable = (row: ItemType, code: string | number) => {
  if (row && row.factoryProductPriceVOMap && code) {
    const { factoryProductPriceVOMap } = row;
    if (typeof code !== 'number') {
      code = Number.parseInt(code, 10);
    }
    return factoryProductPriceVOMap && !factoryProductPriceVOMap[code];
  }
  return true;
};

const changeFactory = () => {
  try {
    updateSkuByChangeFactory(
      data.value,
      dictList.value,
      orderType.value,
      currency.value,
      taxType.value,
      exchangeRate.value,
      isTax.value
    );
    emit('changePrice');
  } catch (error) {
    if (error instanceof Error) {
      ElMessageBox.alert(error.message, '错误');
    }
  }
};
const changeDirectDeliverySupplier = (value: DirectDeliverySupplierEnum) => {
  if (value === DirectDeliverySupplierEnum.SUPPLIER) {
    data.value.getPositionList = () => {
      return data.value.positionList.filter((item: SalesDictType) =>
        endsWith(String(item.code), '04')
      );
    };
    data.value.position = '';
  } else if (data.value.factory) {
    data.value.getPositionList = () => {
      return getPositionList(
        data.value,
        data.value.factory as CompanyType,
        orderType.value
      );
    };
    data.value.position =
      isSupportedAutoPosition(data.value.factory, orderType.value) &&
      data.value?.directDeliverySupplier === '2'
        ? '-1'
        : '';
  }
};

async function getStockSkuInfo(skuList: ItemType[]) {
  const items = (skuList || []).map((item) => ({
    batchNo: item.appointSapBatch,
    position: item.position,
    sku: item.sku,
    factory: item.factory,
    itemNo: String(item.idx),
    taxRate: item.taxRate,
  }));
  const params = {
    items,
  };
  const res = await queryStockSkuInfo(params);
  if (res.code === 200 && res.data) {
    const foundItem = (res.data || []).find(
      (item: StockSkuInfo) => String(data.value.idx) === String(item.itemNo)
    );
    if (foundItem) {
      data.value = {
        ...data.value,
        ...foundItem,
        appointSapBatch: foundItem.batchNo,
        stockProviderNo: foundItem.supplierNo,
        stockProviderName: foundItem.supplierName,
        freeTaxPrice: foundItem.unTaxMoveAveragePrice,
        taxPrice: foundItem.taxMoveAveragePrice,
      };
    }
  } else {
    ElMessage.error(res.msg || '查询失败，请稍后再试！');
  }
}
const handleChange = (_val: string, prop: string) => {
  if (prop === 'appointSapBatch') {
    getStockSkuInfo([data.value]);
  }
};

// 不在名单内客户建单时，选择直发=系统自动判断，库位=自动挑仓，且两个字段置灰不可选择
const disabledDirectAndPosition = computed(
  () =>
    !allowSelectWhsAuth.value &&
    data.value.directDeliverySupplier === '2' &&
    data.value.position === '-1'
);

const fields = ref<OrderFieldsSettings[]>([]);
const getFields = () => {
  fields.value = initFieldsShow('productDetail', 'detailWarehouse')?.children;
};

onMounted(() => {
  getFields();
});
</script>

<template>
  <el-row :gutter="10">
    <el-col v-for="item in fields" :key="item.prop" :span="item.span || 12">
      <el-form-item :label="item.label" :prop="item.prop">
        <el-select
          v-if="item.prop === 'factory'"
          v-model="data.factory"
          placeholder="请选择"
          :disabled="item.disabled"
          @change="changeFactory"
        >
          <el-option
            v-for="opt in factoryList"
            :key="opt.code"
            :label="(opt.code !== '-1' ? opt.code : '') + opt.name"
            :value="opt.code"
            :disabled="isFactoryDisable(data, opt.code)"
          />
        </el-select>
        <el-select
          v-else-if="item.prop === 'directDeliverySupplier' && !isForecast"
          v-model="data.directDeliverySupplier"
          placeholder="请选择"
          :disabled="
            isEnableDirectDeliverySupplier ||
            item.disabled ||
            disabledDirectAndPosition
          "
          @change="changeDirectDeliverySupplier"
        >
          <el-option
            v-for="opt in directDeliverySupplierList"
            :key="opt.code"
            :label="opt.name"
            :value="opt.code"
          />
        </el-select>
        <el-select-v2
          v-else-if="item.prop === 'position'"
          v-model="data.position"
          :disabled="item.disabled || disabledDirectAndPosition"
          :options="
            formatSelectV2Options(
              getPositionList(
                data,
                data.factory as CompanyType,
                orderType,
                positionListMap
              )
            ) || []
          "
          filterable
          clearable
          value-key="value"
          class="w-full text-left"
          placeholder="请选择"
        >
          <template v-if="data.value?.directDeliverySupplier === '0'" #empty>
            <div class="text-left">
              <p class="m-t-10px color-#808080">无匹配数据</p>
              <p class="m-10px m-b-0 color-#808080">
                所选仓不在仓网内，如有疑问请联系IT
              </p>
            </div>
          </template>
        </el-select-v2>
        <el-select
          v-else-if="item.type === 'select'"
          v-model="data[item.prop]"
          :placeholder="`请选择${item.label}`"
          clearable
          :disabled="item.disabled"
        >
          <el-option
            v-for="opt in item.options"
            :key="opt.key"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
        <el-input
          v-else
          v-model="data[item.prop]"
          clearable
          :disabled="item.disabled"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :style="item.style || ''"
          @change="(val) => handleChange(val, item.prop)"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
