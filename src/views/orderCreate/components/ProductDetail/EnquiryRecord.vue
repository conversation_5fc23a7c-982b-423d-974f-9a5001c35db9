<script lang="ts" setup>
import { computed, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { enquiryList } from '@/api/order';
import { useItemStore } from '@/stores/item';
import { useCustomerStore } from '@/stores/customer';

const itemStore = useItemStore();
const customerStore = useCustomerStore();
const selectedItem = computed(() => itemStore.selectedItem);
const customerId = computed(() => customerStore.customer?.customerId);

const emit = defineEmits(['close']);

const reactData = reactive({
  tableData: [],
  total: 0,
  current: 1,
  loading: false,
});
onMounted(() => {
  pageChange();
});
const pageChange = () => {
  if (customerId.value) {
    reactData.loading = true;
    enquiryList(customerId.value, selectedItem.value?.skuNo)
      .then((result) => {
        if (result.code === 200 && result.data) {
          reactData.total = result.data.total;
          reactData.current = result.data.current;
          reactData.tableData = result.data.records;
        }
      })
      .finally(() => {
        reactData.loading = false;
      });
  } else {
    ElMessage.error({
      message: '客户id为空！',
    });
  }
};
</script>

<template>
  <div v-loading="reactData.loading">
    <el-table size="small" border :data="reactData.tableData" class="w-full">
      <el-table-column
        prop="inquiryDate"
        label="询价日期"
        width="150"
        align="center"
      />
      <el-table-column prop="will" label="意向" width="100" align="center" />
      <el-table-column
        prop="taxFlag"
        label="含税/未税"
        width="150"
        align="center"
      />
      <el-table-column
        prop="finalOffer"
        label="最终报价"
        width="150"
        align="center"
      />
      <el-table-column
        prop="uomIdName"
        label="单位"
        width="100"
        align="center"
      />
      <el-table-column
        prop="quoteDate"
        label="报价日期"
        width="100"
        align="center"
      />
      <el-table-column
        prop="quotationNo"
        label="报价单"
        width="150"
        align="center"
      />
    </el-table>
    <div class="pagination">
      <el-pagination
        v-model:current-page="reactData.current"
        small
        background
        layout="prev, pager, next"
        :total="reactData.total"
        @current-change="pageChange"
      />
    </div>
    <div class="btnGroup">
      <el-button plain type="primary" @click="emit('close')">关闭</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.btnGroup {
  margin: 20px 0;
  text-align: center;
}
</style>
