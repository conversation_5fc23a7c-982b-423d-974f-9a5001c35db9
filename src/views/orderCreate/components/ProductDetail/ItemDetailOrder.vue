<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watchEffect } from 'vue';
import { ElMessageBox } from 'element-plus';
import cloneDeep from 'lodash/cloneDeep';
import { InfoFilled } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { isDiffCustomerRelation } from '@/utils/item';

import { accurateQuery, searchCustomerMaterialRelation } from '@/api/order';
import { formatPrice, initFieldsShow } from '@/utils/index';
import {
  IDetailData,
  ItemType,
  MaterialRelationRow,
  SearchItemType,
} from '@/types/item';
import SelectSkus from '@/components/SelectSkus.vue';
import DiffCustomerMaterial from '@/components/DiffCustomerMaterial.vue';
import { useItemStore } from '@/stores/item';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import bus from '@/utils/bus';
import customerMaterialTable from '@/components/CustomerMaterialTable.vue';
import { OrderFieldsSettings } from '@/types/common';
import { PackageInfo } from '@/types/order';
// import type { IDetailData, ItemType, MaterialRelationRow } from '@/types/item';

const props = defineProps<{
  modelValue: ItemType;
}>();

const emit = defineEmits(['update:modelValue', 'changePrice']);

const data = useVModel(props, 'modelValue', emit);

const orderStore = useOrderStore();
const commonStore = useCommonStore();
const itemStore = useItemStore();
const customerStore = useCustomerStore();

const { dictList } = storeToRefs(commonStore);
const { customer } = storeToRefs(customerStore);
const { selectedIndex, factoryList } = storeToRefs(itemStore);

const selectSkuRef = ref<any>(null);

const reactData: IDetailData = reactive({
  currentConversion: null,
  materialList: [],
  rate: '',
  rules: {},
  loading: false,
  showDiffDialog: false,
  formatedSku: {},
  oldSku: {}, // 记录没搜索sku之前填的信息
  resultMaterialObj: {}, // 查出来的客户物料关系
  inputMaterialObj: {}, // 页面手动输入的客户物料关系
});

const selectedSku = computed(() => {
  const {
    skuNo,
    materialDescribe,
    customerMaterialNo,
    customerMaterialName,
    customerSkuUnitCount,
    customerMaterialUnit,
    customerSpecificationModel,
  } = data.value;
  const sku = {
    skuNo,
    materialDescribe,
    customerSkuNo: customerMaterialNo,
    customerSkuName: customerMaterialName,
    customerSkuUnitCount,
    customerSkuUnit: customerMaterialUnit,
    customerSkuSpecification: customerSpecificationModel,
    index: 1,
  };
  return sku;
});

const isItemDisabled = computed(() => data.value.itemEditable === '0');

onMounted(() => {
  getFields();
  initRate();
  searchMaterialRelation();
});

// watch(
//   () => selectedItem.value,
//   (newVal) => {
//     if (newVal) {
//       data.value = {
//         ...data.value,
//         ...newVal,
//       };
//       initRate();
//       searchMaterialRelation();
//     }
//   }
// );

const changeQuantityUnit = (unit: string) => {
  const { quantity, packageInfoList } = data.value;
  const findItem = packageInfoList?.find((pkg: any) => pkg.unit === unit);
  if (findItem && findItem.conversion) {
    const conversion = findItem.conversion;
    let oldConversion = 1;
    if (reactData.currentConversion) {
      oldConversion = reactData.currentConversion;
    }
    if (conversion) {
      data.value.quantity = Number.parseFloat(
        formatPrice((oldConversion * quantity) / conversion, 3)
      );
    }
    reactData.currentConversion = conversion;
  }
};

const handleQuantityChange = (val: number | undefined) => {
  emit('changePrice');
  if (val !== null && data.value.materialRate) {
    data.value.customerMaterialQuantity = formatPrice(
      (val as number) * data.value.materialRate,
      6
    );
  }
};

const searchMaterialRelation = async () => {
  try {
    const { skuNo } = data.value;
    const { customerNumber } = customer.value;
    const params = [
      {
        customerCode: customerNumber,
        zkhSkuNo: skuNo,
      },
    ];
    reactData.loading = true;
    const res = await searchCustomerMaterialRelation({ queryList: params });
    if (res.code === 200) {
      if (Array.isArray(res.data) && res.data.length > 0) {
        data.value.customerMaterialTableData = res.data;
        // if (data.value.customerMaterialTableData.length === 1) {
        //   const [item] = data.value.customerMaterialTableData;
        //   initMaterialInfo(item);
        // }
      } else {
        data.value.customerMaterialTableData = [];
      }
    } else {
      data.value.customerMaterialTableData = [];
    }
  } catch (error) {
    console.log(error);
  } finally {
    reactData.loading = false;
  }
};
const handleMaterialClick = (item: MaterialRelationRow) => {
  if (!isItemDisabled.value) {
    data.value.customerMaterialNo = item.customerMaterialNo;
    data.value.customerSpecificationModel = item.customerMaterialSpecification;
    data.value.customerMaterialName = item.customerMaterialName;
    data.value.customerMaterialUnit = item.customerMaterialUnit;
    if (
      item.customerMaterialStandardQuantity &&
      item.zkhSkuStandardQuantity &&
      Number.parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
      Number.parseFloat(item.zkhSkuStandardQuantity) !== 0
    ) {
      data.value.materialRate = formatPrice(
        Number.parseFloat(item.customerMaterialStandardQuantity) /
          Number.parseFloat(item.zkhSkuStandardQuantity),
        6
      );
      data.value.customerMaterialQuantity = formatPrice(
        data.value.quantity * data.value.materialRate,
        6
      );
    } else {
      data.value.materialRate = '';
    }
  }
};

const initRate = () => {
  const { skuUnitCount, customerSkuUnitCount } = data.value;
  if (
    skuUnitCount &&
    customerSkuUnitCount &&
    Number.parseFloat(skuUnitCount) !== 0 &&
    Number.parseFloat(customerSkuUnitCount) !== 0
  ) {
    data.value.materialRate = formatPrice(
      Number.parseFloat(customerSkuUnitCount) / Number.parseFloat(skuUnitCount),
      6
    );
    if (!data.value.customerMaterialQuantity) {
      data.value.customerMaterialQuantity = formatPrice(
        data.value.quantity * data.value.materialRate,
        6
      );
    }
  }
};
const handleDiffMaterialRelation = async (type: string, obj: ItemType) => {
  try {
    // 取消：cancel，覆盖：yes，不覆盖：no
    if (type === 'cancel') {
      data.value.skuNo = reactData.oldSku.skuNo || '';
    } else {
      await handleSku();
      data.value.customerMaterialNo = obj.customerMaterialNo;
      data.value.customerSpecificationModel = obj.customerSpecificationModel;
      data.value.customerMaterialName = obj.customerMaterialName;
      data.value.customerMaterialUnit = obj.customerMaterialUnit;
    }
    reactData.showDiffDialog = false;
  } catch (error) {
    console.log(error);
  }
};
const handleDiffCustomerRelation = (result: ItemType, detail: ItemType) => {
  reactData.resultMaterialObj = {
    customerMaterialNo: result.customerMaterialNo,
    customerMaterialName: result.customerMaterialName,
    customerMaterialUnit: result.customerMaterialUnit,
    customerSpecificationModel: result.customerSpecificationModel,
  };
  reactData.inputMaterialObj = {
    customerMaterialNo: detail.customerMaterialNo,
    customerMaterialName: detail.customerMaterialName,
    customerMaterialUnit: detail.customerMaterialUnit,
    customerSpecificationModel: detail.customerSpecificationModel,
  };
  return isDiffCustomerRelation(
    reactData.resultMaterialObj,
    reactData.inputMaterialObj
  );
};

const handleSku = async () => {
  if (reactData.formatedSku) {
    data.value = {
      ...data.value,
      ...reactData.formatedSku,
      quantity: reactData.oldSku.quantity,
      customerMaterialQuantity: reactData.oldSku.customerMaterialQuantity,
    };
    initRate();
    searchMaterialRelation();
    emit('changePrice');
    await itemStore.getDeliveryDate();
    bus.emit('updateCheckbox', {
      rows: itemStore.selectedItemList,
      checked: true,
    });
  }
};

const handleDiff = () => {
  const isDiff = handleDiffCustomerRelation(reactData.formatedSku, data.value);
  if (isDiff) {
    reactData.showDiffDialog = true;
  } else {
    handleSku();
  }
};

const changeSkuInTable = async (currentSelectSku: SearchItemType) => {
  if (currentSelectSku) {
    reactData.loading = true;
    // 将下拉框失焦，避免下拉框展开
    setTimeout(() => {
      selectSkuRef.value?.select?.blur();
    }, 80);
    try {
      orderStore.editStore({
        pageLoading: true,
      });
      console.log(currentSelectSku, console.log(factoryList.value));
      const res: any = await itemStore.addGoodsFromDetail({
        factoryList: factoryList.value,
        currentSelectSku,
        index: selectedIndex.value,
      });
      const { foundItem } = res;
      reactData.formatedSku = foundItem;
      reactData.oldSku = cloneDeep(data.value);
      data.value.skuNo = foundItem.skuNo;
      if (currentSelectSku.dataSource !== '商品中心') {
        handleDiff();
      } else {
        // 来源为商品中心，查询是否有客户物料关系，有则比较不同，没有则按不覆盖处理
        const params = {
          sku: currentSelectSku.skuNo,
          customerCode: customer.value?.customerNumber,
        };
        const res = await accurateQuery(params);
        if (res.data.length > 0) {
          handleDiff();
        } else {
          await handleSku();
          const {
            customerMaterialNo,
            customerSpecificationModel,
            customerMaterialName,
            customerMaterialUnit,
          } = reactData.oldSku;
          data.value.customerMaterialNo = customerMaterialNo;
          data.value.customerSpecificationModel = customerSpecificationModel;
          data.value.customerMaterialName = customerMaterialName;
          data.value.customerMaterialUnit = customerMaterialUnit;
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        ElMessageBox.alert((error && error.message) || '操作失败！', '错误', {
          confirmButtonText: '确定',
          type: 'error',
        });
      }
    } finally {
      orderStore.editStore({
        pageLoading: false,
      });
      reactData.loading = false;
    }
  } else {
    data.value.skuNo = '';
  }
};

// const showField = (prop?: string) => {
//   return initFieldsShow('productDetail', prop)?.visible;
// };
// const getFieldLabel = (prop?: string) => {
//   return initFieldsShow('productDetail', prop)?.label;
// };
// const isDisabledField = (prop?: string) => {
//   return initFieldsShow('productDetail', prop)?.disabled;
// };

const handleSelectChange = (prop: string, value: any) => {
  switch (prop) {
    case 'quantityUnit':
      changeQuantityUnit(value);
      break;
  }
};

const computedMax = (row: ItemType, column: OrderFieldsSettings) => {
  if (column?.maxProp) {
    const foundItem = orderStore.orderData?.items?.find(
      (item: ItemType) => item.orderItemNo === row.orderItemNo
    );
    if (foundItem) {
      return foundItem[column?.maxProp];
    }
  }
  return Number.POSITIVE_INFINITY;
};

const fields = ref<OrderFieldsSettings[]>([]);
const getFields = () => {
  fields.value = initFieldsShow('productDetail', 'detailOrder')?.children;
};

watchEffect(() => {
  fields.value?.forEach((item) => {
    if (item.prop === 'quantityUnit') {
      item.options = data.value?.packageInfoList?.map((opt: PackageInfo) => ({
        key: `${opt.skuNo}${opt.unit}`,
        label: opt.unitName,
        value: opt.unit,
      }));
    }
  });
});
</script>

<template>
  <el-row :gutter="10">
    <el-col v-for="item in fields" :key="item.prop" :span="item.span">
      <el-form-item
        v-if="item.prop === 'customerMaterialNo'"
        label="客户物料号"
        prop="customerMaterialNo"
      >
        <template v-if="data.customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户物料号</span>
            <customerMaterialTable
              :table-list="data.customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-input
          v-model="data.customerMaterialNo"
          placeholder="客户物料号"
          clearable
          :disabled="item.disabled"
        />
      </el-form-item>
      <el-form-item
        v-else-if="item.prop === 'customerSpecificationModel'"
        label="客户规格型号"
        prop="customerSpecificationModel"
      >
        <template v-if="data.customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户规格型号</span>
            <customerMaterialTable
              :table-list="data.customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-input
          v-model="data.customerSpecificationModel"
          placeholder="客户规格型号"
          :disabled="item.disabled"
        />
      </el-form-item>
      <el-form-item
        v-else-if="item.prop === 'customerMaterialName'"
        label="客户物料名称"
        prop="customerMaterialName"
      >
        <template v-if="data.customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户物料名称</span>
            <customerMaterialTable
              :table-list="data.customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-input
          v-model="data.customerMaterialName"
          maxlength="100"
          placeholder="客户物料名称"
          :disabled="item.disabled"
        />
      </el-form-item>
      <el-form-item
        v-else-if="item.prop === 'customerMaterialQuantity'"
        prop="customerMaterialQuantity"
      >
        <template #label>
          <div class="label">
            <span>客户物料数量</span>
            <customerMaterialTable
              v-if="data.customerMaterialTableData.length > 1"
              :table-list="data.customerMaterialTableData"
              @change="handleMaterialClick"
            />
            <el-tooltip
              v-if="data.materialRate"
              :content="`当前商品数量与客户物料数量的比例为${data.materialRate}`"
              effect="dark"
              placement="top"
            >
              <template #default>
                <el-icon><InfoFilled /></el-icon>
              </template>
            </el-tooltip>
          </div>
        </template>
        <el-input-number
          v-model="data.customerMaterialQuantity"
          :precision="6"
          :step="1"
          :min="0"
          class="w-full"
          :disabled="item.disabled"
        />
      </el-form-item>
      <el-form-item
        v-else-if="item.prop === 'customerMaterialUnit'"
        label="客户物料单位"
        prop="customerMaterialUnit"
      >
        <template v-if="data.customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户物料单位</span>
            <customerMaterialTable
              :table-list="data.customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-select
          v-model="data.customerMaterialUnit"
          placeholder="请选择"
          clearable
          filterable
          allow-create
          :disabled="item.disabled"
        >
          <el-option
            v-for="item in dictList['customerQuantityUnit']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-else :label="item.label" :prop="item.prop">
        <SelectSkus
          v-if="item.prop === 'selectSku'"
          ref="selectSkuRef"
          :selected-sku="selectedSku"
          :customer-no="customer.customerNumber"
          :customer-material-no="data.customerMaterialNo"
          :disabled="item.disabled"
          @handle-select-sku="changeSkuInTable"
        />
        <el-input-number
          v-else-if="item.prop === 'quantity'"
          v-model="data.quantity"
          class="w-full"
          :precision="0"
          :step="1"
          :min="0"
          :max="computedMax(data, item)"
          :disabled="item.disabled"
          @change="handleQuantityChange"
        />
        <el-select
          v-else-if="item.type === 'select'"
          v-model="data[item.prop]"
          :placeholder="`请选择${item.label}`"
          clearable
          :disabled="item.disabled"
          @change="(value) => handleSelectChange(item.prop, value)"
        >
          <el-option
            v-for="opt in item.options"
            :key="opt.key"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
        <el-input
          v-else
          v-model="data[item.prop]"
          clearable
          :disabled="item.disabled"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :style="item.style || ''"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <DiffCustomerMaterial
    v-model:show-dialog="reactData.showDiffDialog"
    :result-material-obj="reactData.resultMaterialObj"
    :input-material-obj="reactData.inputMaterialObj"
    @submit="handleDiffMaterialRelation"
  />
</template>

<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
