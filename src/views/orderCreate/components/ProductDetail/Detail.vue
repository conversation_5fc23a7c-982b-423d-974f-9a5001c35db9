<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import endsWith from 'lodash/endsWith';
import cloneDeep from 'lodash/cloneDeep';
import { CirclePlus, InfoFilled } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { formatPrice, isNull } from '@/utils/index';
import {
  findNormalItemInDictList,
  formatSelectV2Options,
  getDirectDeliverySupplierList,
  getDisabledDate,
  getPositionList,
  isDiffCustomerRelation,
  sensorsSku,
  updateSkuByChangeFactory,
} from '@/utils/item';

import {
  accurateQuery,
  calculatePrice as calculatePriceApi,
  searchCustomerMaterialRelation,
  searchMaterial,
} from '@/api/order';
import {
  isEnableDirectDeliverySupplier as isEnableDirectDeliverySupplierApi,
  isForecastOrder,
  isFreeOrder,
  isSupportedAutoPosition,
} from '@/utils/orderType';
import {
  DirectDeliverySupplierEnum,
  IDetailData,
  ItemRowType,
  ItemType,
  MaterialRelationRow,
  SearchItemType,
} from '@/types/item';
import DividerHeader from '@/components/DividerHeader.vue';
import SelectSkus from '@/components/SelectSkus.vue';
import DiffCustomerMaterial from '@/components/DiffCustomerMaterial.vue';
import { useItemStore } from '@/stores/item';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useDeliveryStore } from '@/stores/delivery';
import { useCommonStore } from '@/stores/common';
import bus from '@/utils/bus';
import customerMaterialTable from '@/components/CustomerMaterialTable.vue';
// import type { IDetailData, ItemType, MaterialRelationRow } from '@/types/item';
import { PriceItems } from '@/types/price';
import type { CompanyType, SalesDictType } from '@/types/common';

const emit = defineEmits([
  'close',
  'reset',
  'changeUrgent',
  'changeCustomerDateSensitive',
]);

const orderStore = useOrderStore();
const deliveryStore = useDeliveryStore();
const commonStore = useCommonStore();
const itemStore = useItemStore();
const customerStore = useCustomerStore();

const { deliveryData } = storeToRefs(deliveryStore);
const { dictList, searchSkuSwitch, holidaysList } = storeToRefs(commonStore);
const { orderData, isSAP814, isDraftDetail, isFakeSketch, allowSelectWhsAuth } =
  storeToRefs(orderStore);
const { selectedSalesRange, customer, cusDetail } = storeToRefs(customerStore);
const { selectedItem, selectedIndex, itemList, positionListMap, factoryList } =
  storeToRefs(itemStore);

const orderType = computed(() => orderData.value.orderType || '');

const isTax = computed(() => cusDetail.value?.isTax);
const taxType = computed(() => cusDetail.value?.taxType);
const currency = computed(() => customer.value?.currency || '');
const exchangeRate = computed(() => customer.value?.exchangeRate);
const autoBatching = computed(() => deliveryData.value?.autoBatching);

const distributionChannel = computed(
  () => selectedSalesRange.value.distributionChannel
);
const salesOrganization = computed(() => {
  if (selectedSalesRange.value) {
    return selectedSalesRange.value.salesOrganization;
  }
  return '';
});

const skuList = computed(() => itemList.value || []);

const isTaxedCustomer = computed(() => {
  return isTax.value === '1';
});
const isForecast = computed(() => {
  return isForecastOrder(orderType.value);
});
const isFree = computed(() => {
  return isFreeOrder(orderType.value);
});
const isEnableDirectDeliverySupplier = computed(() => {
  return isEnableDirectDeliverySupplierApi(orderType.value);
});
const directDeliverySupplierList = computed(() => {
  return getDirectDeliverySupplierList(
    dictList.value,
    orderType.value,
    salesOrganization.value
  );
});

const specifiedReceiptDayOfWeek = computed(() =>
  deliveryData.value.specifiedReceiptDayOfWeek?.filter(Boolean)
);
const receiptTimeCategory = computed(
  () => deliveryData.value?.receiptTimeCategory
);

const bidCustomer = computed(() => deliveryData.value.bidCustomer);
const disabledDate = (time: Date) => {
  const check =
    !['Z002', 'Z014'].includes(orderType.value) &&
    !['8', 'X'].includes(bidCustomer.value);
  return getDisabledDate(
    time,
    specifiedReceiptDayOfWeek.value,
    receiptTimeCategory.value,
    holidaysList.value,
    check
  );
};
const focusDatePicker = () => {
  if (
    (receiptTimeCategory.value === 'Z' ||
      receiptTimeCategory.value === false) &&
    specifiedReceiptDayOfWeek.value?.length &&
    specifiedReceiptDayOfWeek.value?.every(
      (item: string) => !['0', '01', '02', '03', '04', '05'].includes(item)
    )
  ) {
    ElMessage.warning(
      '无可选日期，请修改客户指定日收货或工作日与周末均可收货!'
    );
  }
};

const showCustom = computed(() => {
  return (
    /z001/gim.test(orderType.value) &&
    selectedItem.value?.customPropertyList?.length > 0
  );
});

const notAcceptDemandReasons = computed(() =>
  (reactData.sku.notAcceptDemandReasons || []).filter((code: string) =>
    findNormalItemInDictList(dictList.value.notAcceptDemandReason, code)
  )
);
const dnOrderPendingReasons = computed(() =>
  (reactData.sku.dnOrderPendingReasons || []).filter((code: string) =>
    findNormalItemInDictList(dictList.value.dnOrderPendingReason, code)
  )
);

const refs_goodsDetailForm = ref<any>(null);
const selectSkuRef = ref<any>(null);

const defaultRules: any = {
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  directDeliverySupplier: [
    { required: true, message: '请输入直发信息', trigger: 'blur' },
  ],
  position: [{ required: true, message: '请选择库位', trigger: 'blur' }],
  factory: [{ required: true, message: '请选择工厂', trigger: 'blur' }],
};
const nullSkuRules: any = {
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  customerMaterialName: [
    { required: true, message: '请输入客户物料名称', trigger: 'blur' },
  ],
};

const setRules = () => {
  if (!reactData.sku.skuNo) {
    reactData.rules = nullSkuRules;
    reactData.rules.quantity = { required: false };
  } else {
    reactData.rules = defaultRules;
  }
  if (isTaxedCustomer.value) {
    reactData.rules = {
      ...reactData.rules,
      taxPrice: [
        { required: true, message: '请选择含税单价', trigger: 'blur' },
      ],
    };
  } else {
    reactData.rules = {
      ...reactData.rules,
      freeTaxPrice: [
        { required: true, message: '请选择未税单价', trigger: 'blur' },
      ],
    };
  }
  if (orderType.value === 'Z018') {
    reactData.rules = {
      ...reactData.rules,
      rentalDueDate: [
        { required: true, message: '请选择租赁截止日期', trigger: 'blur' },
      ],
      fixedAssetsId: [
        { required: true, message: '请输入固定资产编号', trigger: 'blur' },
      ],
    };
  }
  if (orderType.value === 'Z014') {
    reactData.rules = {
      ...reactData.rules,
      customerDate: [
        { required: true, message: '请选择客户期望送达日期', trigger: 'blur' },
      ],
    };
  }
  refs_goodsDetailForm.value.clearValidate();
};

const reactData: IDetailData = reactive({
  sku: {
    discountAmount: 0,
    discountConditionType: 'ZK01',
    rentalDueDate: '',
    fixedAssetsId: '',
    customerSpecificationModel: '',
    customerOrderNo: '',
    customerMaterialName: '',
    customerMaterialUnit: '',
    customerMaterialQuantity: 0,
    customerMaterialNo: '',
    quantity: 0,
    remark: '',
    purchaseNote: '',
    comment: '',
    position: '',
    sku: '',
    freeTaxPrice: 0,
    taxAmount: 0,
    taxRate: 0,
    taxPrice: 0,
    manufacturerNo: '',
    materialDescribe: '',
    quantityUnit: '',
    customerDateSensitive: false,
    demandUser: '',
    demandDepartment: '',
    customerDate: '',
    skuNo: '',
    packageInfoList: [],
    positionList: [],
    factory: '',
    directDeliverySupplier: {},
    factoryProductPriceVOMap: {},
    ...selectedItem.value,
    customerMaterialTableData: [],
  },
  currentConversion: null,
  materialList: [],
  rate: '',
  rules: {},
  loading: false,
  showDiffDialog: false,
  formatedSku: {},
  oldSku: {}, // 记录没搜索sku之前填的信息
  resultMaterialObj: {}, // 查出来的客户物料关系
  inputMaterialObj: {}, // 页面手动输入的客户物料关系
});

const selectedSku = computed(() => {
  const {
    skuNo,
    materialDescribe,
    customerMaterialNo,
    customerMaterialName,
    customerSkuUnitCount,
    customerMaterialUnit,
    customerSpecificationModel,
  } = reactData.sku;
  const sku = {
    skuNo,
    materialDescribe,
    customerSkuNo: customerMaterialNo,
    customerSkuName: customerMaterialName,
    customerSkuUnitCount,
    customerSkuUnit: customerMaterialUnit,
    customerSkuSpecification: customerSpecificationModel,
    index: 1,
  };
  return sku;
});

const isItemDisabled = computed(() => reactData.sku.itemEditable === '0');
// const taxedPriceTotal = computed(() => {
//   return reactData.sku.taxPrice
//     ? formatPrice(reactData.sku.quantity * reactData.sku.taxPrice)
//     : 0;
// });
// const unTaxedPriceTotal = computed(() => {
//   return reactData.sku.freeTaxPrice
//     ? formatPrice(reactData.sku.quantity * reactData.sku.freeTaxPrice)
//     : 0;
// });
const unTaxedTable = computed(() => {
  const { factoryProductPriceVOMap, factory } = reactData.sku as ItemType;
  if (factory && factoryProductPriceVOMap) {
    const code = Number(factory as CompanyType);
    const priceMap = factoryProductPriceVOMap[code];
    if (priceMap) {
      const { dealerMinPrice, suggestPrice, taxRate } = priceMap;
      const res = [
        {
          name: '建议销售价',
          price: suggestPrice ? formatPrice(suggestPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        },
      ];
      if (distributionChannel.value === '02') {
        res.push({
          name: '最低折扣价',
          price: dealerMinPrice ? formatPrice(dealerMinPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        });
      }
      return res;
    }
  }
  return [];
});
const taxedTable = computed(() => {
  const { factoryProductPriceVOMap, factory } = reactData.sku;
  if (factory && factoryProductPriceVOMap) {
    const code = Number(factory as CompanyType);
    const priceMap = factoryProductPriceVOMap[code];
    if (priceMap) {
      const { dealerMinPrice, suggestPrice, taxRate } = priceMap;
      const res = [
        {
          name: '建议销售价',
          price: suggestPrice ? formatPrice(suggestPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        },
      ];
      if (distributionChannel.value === '02') {
        res.push({
          name: '最低折扣价',
          price: dealerMinPrice ? formatPrice(dealerMinPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        });
      }
      return res;
    }
  }
  return [];
});

onMounted(() => {
  setRules();
  // 灰度，在白名单内走新的查询物料关系接口，否则走老的查询接口
  if (searchSkuSwitch.value) {
    initRate();
    searchMaterialRelation();
  } else {
    search();
  }
});

watch(
  () => selectedItem.value,
  (newVal) => {
    if (newVal) {
      reactData.sku = {
        ...reactData.sku,
        ...newVal,
      };
      initRate();
      searchMaterialRelation();
      setRules();
    }
  }
);

const search = () => {
  const { skuNo } = reactData.sku;
  const { customerNumber } = customer.value;
  if (customerNumber) {
    const params = {
      customerNo: customerNumber,
      skuNo,
      current: 1,
      size: 20,
    };
    searchMaterial(params).then((result) => {
      if (result.code === 200) {
        if (Array.isArray(result.data) && result.data.length > 0) {
          reactData.materialList = result.data;
          let data = result.data[0];
          if (reactData.sku.customerMaterialNo) {
            const foundItem = result.data.find(
              (item: any) =>
                item.customerSkuNo === reactData.sku.customerMaterialNo
            );
            if (foundItem) {
              data = foundItem;
            }
          }
          const {
            customerSkuName,
            customerSkuNo,
            customerSkuSpecification,
            customerSkuUnit,
            customerSkuUnitCount,
            skuUnitCount,
          } = data;
          if (!reactData.sku.customerMaterialNo) {
            reactData.sku.customerMaterialNo = customerSkuNo;
          }
          if (!reactData.sku.customerMaterialName) {
            reactData.sku.customerMaterialName = customerSkuName;
          }
          if (!reactData.sku.customerSpecificationModel) {
            reactData.sku.customerSpecificationModel = customerSkuSpecification;
          }
          if (!reactData.sku.customerMaterialUnit) {
            reactData.sku.customerMaterialUnit = customerSkuUnit;
          }
          if (
            !reactData.sku.customerMaterialQuantity &&
            customerSkuUnitCount !== null &&
            skuUnitCount !== null
          ) {
            reactData.sku.materialRate = `${skuUnitCount}:${customerSkuUnitCount}`;
            const quantity = Number.parseFloat(`${reactData.sku.quantity}`);
            reactData.sku.customerMaterialQuantity = Number.parseFloat(
              formatPrice((quantity * customerSkuUnitCount) / skuUnitCount, 6)
            );
          }
        } else {
          reactData.materialList = [];
        }
        // 处理单位
        if (
          reactData.sku &&
          reactData.sku.packageInfoList &&
          reactData.sku.packageInfoList.length === 0
        ) {
          // sku 中没有 unitCode, unitName
          const { basicUnit, basicUnitName } = reactData.sku;
          if (basicUnit && basicUnitName) {
            reactData.sku.packageInfoList.push({
              skuNo,
              unitName: basicUnitName,
              ruleDes: `1${basicUnitName}/${basicUnitName}`,
              conversion: 1,
              unit: basicUnit,
            });
          }
        }
        if (
          reactData.sku &&
          reactData.sku.packageInfoList &&
          reactData.sku.packageInfoList.length > 0
        ) {
          reactData.sku.quantityUnit = reactData.sku.packageInfoList[0].unit;
        }
      } else {
        ElMessage.error({
          message: result.msg,
        });
      }
    });
  }
};
const isFactoryDisable = (row: ItemType, code: string | number) => {
  if (row && row.factoryProductPriceVOMap && code) {
    const { factoryProductPriceVOMap } = row;
    if (typeof code !== 'number') {
      code = Number.parseInt(code, 10);
    }
    return factoryProductPriceVOMap && !factoryProductPriceVOMap[code];
  }
  return true;
};

const changeQuantityUnit = (unit: string) => {
  const { quantity, packageInfoList } = reactData.sku;
  const findItem = packageInfoList?.find((pkg: any) => pkg.unit === unit);
  if (findItem && findItem.conversion) {
    const conversion = findItem.conversion;
    let oldConversion = 1;
    if (reactData.currentConversion) {
      oldConversion = reactData.currentConversion;
    }
    if (conversion) {
      reactData.sku.quantity = Number.parseFloat(
        formatPrice((oldConversion * quantity) / conversion, 3)
      );
    }
    reactData.currentConversion = conversion;
  }
};

const calculatePrice = async () => {
  const isTax = customerStore.cusDetail?.isTax;
  const item = {
    discountAmount: reactData.sku.discountAmount || 0,
    itemNo: reactData.sku.idx,
    promotionalDiscountRate: isNull(reactData.sku.promotionalDiscountRate)
      ? 100
      : reactData.sku.promotionalDiscountRate,
    quantity: reactData.sku.quantity || 0,
    sku: reactData.sku.skuNo,
    taxRate: reactData.sku.taxRate || 0,
    unitPrice:
      isTax === '1' ? reactData.sku.taxPrice : reactData.sku.freeTaxPrice,
  };
  const data = {
    orderBasis: orderStore.orderData?.orderBasis,
    isTax,
    items: [item],
  };
  const res = await calculatePriceApi(data);
  if (res.code === 200) {
    return res.data;
  }
};

const changePrice = async () => {
  const priceData = await calculatePrice();
  if (priceData?.priceItems) {
    const foundItem = priceData.priceItems.find(
      (item: PriceItems) => String(reactData.sku.idx) === String(item.itemNo)
    );
    reactData.sku = {
      ...reactData.sku,
      ...foundItem,
      freeTaxPrice: foundItem?.untaxedUnitPrice || 0,
    };
  }
};
const changeFactory = () => {
  try {
    updateSkuByChangeFactory(
      reactData.sku,
      dictList.value,
      orderType.value,
      currency.value,
      taxType.value,
      exchangeRate.value,
      isTax.value
    );
    changePrice();
  } catch (error) {
    if (error instanceof Error) {
      ElMessageBox.alert(error.message, '错误');
    }
  }
};
const changeDirectDeliverySupplier = (value: DirectDeliverySupplierEnum) => {
  if (value === DirectDeliverySupplierEnum.SUPPLIER) {
    reactData.sku.getPositionList = () => {
      return reactData.sku.positionList.filter((item: SalesDictType) =>
        endsWith(String(item.code), '04')
      );
    };
    reactData.sku.position = '';
  } else if (reactData.sku.factory) {
    reactData.sku.getPositionList = () => {
      return getPositionList(
        reactData.sku,
        reactData.sku.factory,
        orderType.value
      );
    };
    reactData.sku.position =
      isSupportedAutoPosition(reactData.sku.factory, orderType.value) &&
      reactData.sku.directDeliverySupplier === '2'
        ? '-1'
        : '';
  }
};

// const taxedChange = (value: number | undefined) => {
//   const taxRate = Number.parseFloat(reactData.sku.taxRate);
//   const quantity = reactData.sku.quantity;
//   reactData.sku.freeTaxPrice = formatPrice((value as number) / (1 + taxRate));
//   reactData.sku.taxAmount = reactData.sku.freeTaxPrice * taxRate * quantity;
// };
// const unTaxedChange = (value: number | undefined) => {
//   value = Number(value);
//   const taxRate = Number.parseFloat(reactData.sku.taxRate);
//   const quantity = reactData.sku.quantity;
//   reactData.sku.taxPrice = formatPrice((value as number) * (1 + taxRate));
//   reactData.sku.taxAmount = (value as number) * taxRate * quantity;
// };

const updateCheckbox = (type: string) => {
  const everyValid = skuList.value.every((sku: ItemType) => sku[type]);
  const everyInvalid = skuList.value.every((sku: ItemType) => !sku[type]);

  const valid = skuList.value.some((sku: ItemType) => sku[type]);
  const invalid = skuList.value.some((sku: ItemType) => !sku[type]);
  console.log(
    type,
    everyValid,
    everyInvalid,
    valid,
    invalid,
    skuList.value.map((i: ItemType) => i[type])
  );
  if (everyValid) {
    customerStore.editStore({
      [type]: true,
    });
  } else {
    customerStore.editStore({
      [type]: false,
    });
  }
  if (valid && invalid) {
    customerStore.editStore({
      [type]: false,
    });
  }
  switch (type) {
    case 'customerDateSensitive':
      customerStore.editStore({
        customerDateSensitiveIndeterminate: false,
      });
      if (valid && invalid) {
        customerStore.editStore({
          customerDateSensitiveIndeterminate: true,
        });
      }
      break;
  }
};
const updateCustomerCheckbox = () => {
  updateCheckbox('customerDateSensitive');
};
const handleSubmit = async () => {
  // customerMaterialName:客户物料名称
  // customerMaterialNo  客户物料号
  // customerSpecificationModel  客户规格型号
  // customerOrderNo 客户行号
  const toHandleArr = [
    'customerMaterialNo',
    'customerMaterialName',
    'customerSpecificationModel',
    'customerOrderNo',
  ];
  toHandleArr.forEach((key) => {
    if ((reactData.sku as ItemType)[key]) {
      (reactData.sku as ItemType)[key] = (reactData.sku as ItemType)[key]
        .trim()
        .replaceAll(/\r\n|\r|\n/g, '');
    }
  });
  itemStore.updateItem({
    type: ItemRowType.all,
    index: selectedIndex.value,
    isTax: isTax.value,
    value: {
      ...reactData.sku,
    },
  });
  itemStore.getDeliveryDate();
  if (selectedIndex.value === 0) {
    await customerStore.changeClientDetail(true);
  }
  updateCustomerCheckbox();
};

const checkForm = (callback: any) => {
  refs_goodsDetailForm.value.validate((valid: boolean) => {
    const { skuNo, quantity, customerMaterialQuantity } = reactData.sku;
    if (
      !skuNo &&
      (!quantity || Number.parseFloat(quantity) === 0) &&
      (!customerMaterialQuantity ||
        Number.parseFloat(customerMaterialQuantity) === 0)
    ) {
      ElMessage.error('数量和客户物料数量不能同时为0！');
      return;
    }
    if (valid) {
      callback && callback();
    }
  });
};

const submit = () => {
  checkForm(() => {
    handleSubmit();
    emit('close');
  });
};

const submitAndToLast = () => {
  checkForm(() => {
    handleSubmit();
    if (selectedIndex.value > 0) {
      itemStore.editStore({
        selectedIndex: selectedIndex.value - 1,
      });
      emit('reset');
    } else {
      ElMessageBox.alert('上一行为空！', { type: 'error' });
    }
  });
};

const submitAndToNext = () => {
  checkForm(() => {
    handleSubmit();
    const len = itemList.value.length;
    if (len > selectedIndex.value + 1) {
      itemStore.editStore({
        selectedIndex: selectedIndex.value + 1,
      });
      emit('reset');
    } else {
      ElMessageBox.alert('下一行为空！', { type: 'error' });
    }
  });
};

const cancel = () => {
  refs_goodsDetailForm.value.resetFields();
  emit('close');
};

const handleQuantityChange = (val: number | undefined) => {
  changePrice();
  if (val !== null && reactData.sku.materialRate) {
    reactData.sku.customerMaterialQuantity = formatPrice(
      (val as number) * reactData.sku.materialRate,
      6
    );
  }
};

const searchMaterialRelation = async () => {
  try {
    const { skuNo } = reactData.sku;
    const { customerNumber } = customer.value;
    const data = [
      {
        customerCode: customerNumber,
        zkhSkuNo: skuNo,
      },
    ];
    reactData.loading = true;
    const res = await searchCustomerMaterialRelation({ queryList: data });
    if (res.code === 200) {
      if (Array.isArray(res.data) && res.data.length > 0) {
        reactData.sku.customerMaterialTableData = res.data;
        // if (reactData.sku.customerMaterialTableData.length === 1) {
        //   const [item] = reactData.sku.customerMaterialTableData;
        //   initMaterialInfo(item);
        // }
      } else {
        reactData.sku.customerMaterialTableData = [];
      }
    } else {
      reactData.sku.customerMaterialTableData = [];
    }
  } catch (error) {
    console.log(error);
  } finally {
    reactData.loading = false;
  }
};
const handleMaterialClick = (item: MaterialRelationRow) => {
  if (!isItemDisabled.value) {
    reactData.sku.customerMaterialNo = item.customerMaterialNo;
    reactData.sku.customerSpecificationModel =
      item.customerMaterialSpecification;
    reactData.sku.customerMaterialName = item.customerMaterialName;
    reactData.sku.customerMaterialUnit = item.customerMaterialUnit;
    if (
      item.customerMaterialStandardQuantity &&
      item.zkhSkuStandardQuantity &&
      Number.parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
      Number.parseFloat(item.zkhSkuStandardQuantity) !== 0
    ) {
      reactData.sku.materialRate = formatPrice(
        Number.parseFloat(item.customerMaterialStandardQuantity) /
          Number.parseFloat(item.zkhSkuStandardQuantity),
        6
      );
      reactData.sku.customerMaterialQuantity = formatPrice(
        reactData.sku.quantity * reactData.sku.materialRate,
        6
      );
    } else {
      reactData.sku.materialRate = '';
    }
  }
};

const initRate = () => {
  const { skuUnitCount, customerSkuUnitCount } = reactData.sku;
  if (
    skuUnitCount &&
    customerSkuUnitCount &&
    Number.parseFloat(skuUnitCount) !== 0 &&
    Number.parseFloat(customerSkuUnitCount) !== 0
  ) {
    reactData.sku.materialRate = formatPrice(
      Number.parseFloat(customerSkuUnitCount) / Number.parseFloat(skuUnitCount),
      6
    );
    if (!reactData.sku.customerMaterialQuantity) {
      reactData.sku.customerMaterialQuantity = formatPrice(
        reactData.sku.quantity * reactData.sku.materialRate,
        6
      );
    }
  }
};
const handleDiffMaterialRelation = async (type: string, obj: ItemType) => {
  try {
    // 取消：cancel，覆盖：yes，不覆盖：no
    if (type === 'cancel') {
      reactData.sku.skuNo = reactData.oldSku.skuNo || '';
    } else {
      await handleSku();
      reactData.sku.customerMaterialNo = obj.customerMaterialNo;
      reactData.sku.customerSpecificationModel = obj.customerSpecificationModel;
      reactData.sku.customerMaterialName = obj.customerMaterialName;
      reactData.sku.customerMaterialUnit = obj.customerMaterialUnit;
    }
    reactData.showDiffDialog = false;
  } catch (error) {
    console.log(error);
  }
};
const handleDiffCustomerRelation = (result: ItemType, detail: ItemType) => {
  reactData.resultMaterialObj = {
    customerMaterialNo: result.customerMaterialNo,
    customerMaterialName: result.customerMaterialName,
    customerMaterialUnit: result.customerMaterialUnit,
    customerSpecificationModel: result.customerSpecificationModel,
  };
  reactData.inputMaterialObj = {
    customerMaterialNo: detail.customerMaterialNo,
    customerMaterialName: detail.customerMaterialName,
    customerMaterialUnit: detail.customerMaterialUnit,
    customerSpecificationModel: detail.customerSpecificationModel,
  };
  return isDiffCustomerRelation(
    reactData.resultMaterialObj,
    reactData.inputMaterialObj
  );
};

const handleSku = async () => {
  if (reactData.formatedSku) {
    reactData.sku = {
      ...reactData.sku,
      ...reactData.formatedSku,
      quantity: reactData.oldSku.quantity,
      customerMaterialQuantity: reactData.oldSku.customerMaterialQuantity,
    };
    initRate();
    searchMaterialRelation();
    setRules();
    changePrice();
    await itemStore.getDeliveryDate();
    bus.emit('updateCheckbox', {
      rows: itemStore.selectedItemList,
      checked: true,
    });
  }
};

const handleDiff = () => {
  const isDiff = handleDiffCustomerRelation(
    reactData.formatedSku,
    reactData.sku
  );
  if (isDiff) {
    reactData.showDiffDialog = true;
  } else {
    handleSku();
  }
};

const changeSkuInTable = async (
  currentSelectSku: SearchItemType,
  searchKeyWord: string
) => {
  if (currentSelectSku) {
    // 新增/替换商品添加埋点
    sensorsSku('OrderProductDetailsProductReplaceClick', {
      ...currentSelectSku,
      searchKeyWord,
      orderSkuNo: reactData.oldSku.skuNo || '',
      productImportSource: isDraftDetail
        ? '草稿修改页面-详情'
        : '订单创建页面(草稿/正式)-详情',
    });
    reactData.loading = true;
    // 将下拉框失焦，避免下拉框展开
    setTimeout(() => {
      selectSkuRef.value.select.blur();
    }, 80);
    try {
      orderStore.editStore({
        pageLoading: true,
      });
      console.log(currentSelectSku, console.log(factoryList.value));
      const res: any = await itemStore.addGoodsFromDetail({
        factoryList: factoryList.value,
        currentSelectSku,
        index: selectedIndex.value,
      });
      const { foundItem } = res;
      reactData.formatedSku = foundItem;
      reactData.oldSku = cloneDeep(reactData.sku);
      reactData.sku.skuNo = foundItem.skuNo;
      if (currentSelectSku.dataSource !== '商品中心') {
        handleDiff();
      } else {
        // 来源为商品中心，查询是否有客户物料关系，有则比较不同，没有则按不覆盖处理
        const data = {
          sku: currentSelectSku.skuNo,
          customerCode: customer.value?.customerNumber,
        };
        const res = await accurateQuery(data);
        if (res.data.length > 0) {
          handleDiff();
        } else {
          await handleSku();
          const {
            customerMaterialNo,
            customerSpecificationModel,
            customerMaterialName,
            customerMaterialUnit,
          } = reactData.oldSku;
          reactData.sku.customerMaterialNo = customerMaterialNo;
          reactData.sku.customerSpecificationModel = customerSpecificationModel;
          reactData.sku.customerMaterialName = customerMaterialName;
          reactData.sku.customerMaterialUnit = customerMaterialUnit;
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        ElMessageBox.alert((error && error.message) || '操作失败！', '错误', {
          confirmButtonText: '确定',
          type: 'error',
        });
      }
    } finally {
      orderStore.editStore({
        pageLoading: false,
      });
      reactData.loading = false;
    }
  } else {
    reactData.sku.skuNo = '';
    setRules();
  }
};

// 不在名单内客户建单时，选择直发=系统自动判断，库位=自动挑仓，且两个字段置灰不可选择
const disabledDirectAndPosition = computed(
  () =>
    !allowSelectWhsAuth.value &&
    reactData.sku.directDeliverySupplier === '2' &&
    reactData.sku.position === '-1'
);
</script>

<template>
  <el-form
    ref="refs_goodsDetailForm"
    v-loading="reactData.loading"
    :model="reactData.sku"
    :rules="reactData.rules"
    label-width="140px"
    :disabled="isItemDisabled"
  >
    <DividerHeader>商品信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="客户物料号" prop="customerMaterialNo">
          <template
            v-if="reactData.sku.customerMaterialTableData.length > 1"
            #label
          >
            <div class="label">
              <span>客户物料号</span>
              <customerMaterialTable
                :table-list="reactData.sku.customerMaterialTableData"
                @change="handleMaterialClick"
              />
            </div>
          </template>
          <el-input
            v-model="reactData.sku.customerMaterialNo"
            placeholder="客户物料号"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户行号" prop="customerOrderNo">
          <el-input
            v-model="reactData.sku.customerOrderNo"
            placeholder="客户行号"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="制造商型号">
          <el-input
            :model-value="reactData.sku.manufacturerNo"
            disabled
            placeholder="制造商型号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户规格型号" prop="customerSpecificationModel">
          <template
            v-if="reactData.sku.customerMaterialTableData.length > 1"
            #label
          >
            <div class="label">
              <span>客户规格型号</span>
              <customerMaterialTable
                :table-list="reactData.sku.customerMaterialTableData"
                @change="handleMaterialClick"
              />
            </div>
          </template>
          <el-input
            v-model="reactData.sku.customerSpecificationModel"
            placeholder="客户规格型号"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="商品编号">
          <SelectSkus
            ref="selectSkuRef"
            :selected-sku="selectedSku"
            :customer-no="customer.customerNumber"
            :customer-material-no="reactData.sku.customerMaterialNo"
            @handle-select-sku="changeSkuInTable"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="商品描述" prop="materialDescribe">
          <el-input v-model="reactData.sku.materialDescribe" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="客户物料名称" prop="customerMaterialName">
          <template
            v-if="reactData.sku.customerMaterialTableData.length > 1"
            #label
          >
            <div class="label">
              <span>客户物料名称</span>
              <customerMaterialTable
                :table-list="reactData.sku.customerMaterialTableData"
                @change="handleMaterialClick"
              />
            </div>
          </template>
          <el-input
            v-model="reactData.sku.customerMaterialName"
            maxlength="100"
            placeholder="客户物料名称"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="reactData.sku.quantity"
            class="w-full"
            :precision="0"
            :step="1"
            :min="0"
            @change="handleQuantityChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="customerMaterialQuantity">
          <template #label>
            <div class="label">
              <span>客户物料数量</span>
              <customerMaterialTable
                v-if="reactData.sku.customerMaterialTableData.length > 1"
                :table-list="reactData.sku.customerMaterialTableData"
                @change="handleMaterialClick"
              />
              <el-tooltip
                v-if="reactData.sku.materialRate"
                :content="`当前商品数量与客户物料数量的比例为${reactData.sku.materialRate}`"
                effect="dark"
                placement="top"
              >
                <template #default>
                  <el-icon><InfoFilled /></el-icon>
                </template>
              </el-tooltip>
            </div>
          </template>
          <el-input-number
            v-model="reactData.sku.customerMaterialQuantity"
            :precision="6"
            :step="1"
            :min="0"
            class="w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="单位" prop="quantityUnit">
          <el-select
            v-model="reactData.sku.quantityUnit"
            placeholder="请选择单位"
            clearable
            @change="changeQuantityUnit"
          >
            <el-option
              v-for="item in reactData.sku.packageInfoList"
              :key="item.skuNo + item.unit"
              :label="item.unitName"
              :value="item.unit"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户物料单位" prop="customerMaterialUnit">
          <template
            v-if="reactData.sku.customerMaterialTableData.length > 1"
            #label
          >
            <div class="label">
              <span>客户物料单位</span>
              <customerMaterialTable
                :table-list="reactData.sku.customerMaterialTableData"
                @change="handleMaterialClick"
              />
            </div>
          </template>
          <el-select
            v-model="reactData.sku.customerMaterialUnit"
            placeholder="请选择"
            clearable
            filterable
            allow-create
          >
            <el-option
              v-for="item in dictList['customerQuantityUnit']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>仓位信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="工厂" prop="factory">
          <el-select
            v-model="reactData.sku.factory"
            placeholder="请选择"
            @change="changeFactory"
          >
            <el-option
              v-for="item in factoryList"
              :key="item.code"
              :label="(item.code !== '-1' ? item.code : '') + item.name"
              :value="item.code"
              :disabled="isFactoryDisable(reactData.sku, item.code)"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="!isForecast" :span="12">
        <el-form-item label="选择直发" prop="directDeliverySupplier">
          <el-select
            v-model="reactData.sku.directDeliverySupplier"
            placeholder="请选择"
            :disabled="
              isEnableDirectDeliverySupplier ||
              isSAP814 ||
              disabledDirectAndPosition
            "
            @change="changeDirectDeliverySupplier"
          >
            <el-option
              v-for="item in directDeliverySupplierList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="库位" prop="position">
          <el-select-v2
            v-model="reactData.sku.position"
            :options="
              formatSelectV2Options(
                getPositionList(
                  reactData.sku,
                  reactData.sku.factory,
                  orderType,
                  positionListMap
                )
              ) || []
            "
            filterable
            clearable
            value-key="value"
            class="w-full text-left"
            :disabled="isSAP814 || disabledDirectAndPosition"
            placeholder="请选择"
          >
            <template
              v-if="reactData.sku.directDeliverySupplier === '0'"
              #empty
            >
              <div class="text-left">
                <p class="m-t-10px color-#808080">无匹配数据</p>
                <p class="m-10px m-b-0 color-#808080">
                  所选仓不在仓网内，如有疑问请联系IT
                </p>
              </div>
            </template>
          </el-select-v2>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>价格信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12" :class="{ tax: !isTaxedCustomer }">
        <el-form-item required label="未税单价" prop="freeTaxPrice">
          <el-input-number
            v-model="reactData.sku.freeTaxPrice"
            :disabled="isTaxedCustomer || isFree"
            class="w-full"
            :min="0"
            :precision="6"
            :step="1"
            @change="changePrice"
          />
        </el-form-item>

        <el-dropdown v-if="!isTaxedCustomer" trigger="click">
          <el-link class="link" size="mini" :underline="false" type="primary">
            建议价格
            <el-icon><CirclePlus /></el-icon>
          </el-link>
          <template #dropdown>
            <el-dropdown-menu :show-timeout="50">
              <el-dropdown-item class="p0">
                <el-table
                  :data="unTaxedTable"
                  border
                  class="w-full"
                  size="small"
                >
                  <el-table-column
                    prop="name"
                    label="价格项"
                    width="150"
                    align="center"
                  />
                  <el-table-column
                    prop="price"
                    label="未税单价"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    prop="rate"
                    label="税率"
                    width="100"
                    align="center"
                  />
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
      <el-col :span="12" :class="{ tax: isTaxedCustomer }">
        <el-form-item label="含税单价">
          <el-input-number
            v-model="reactData.sku.taxPrice"
            :disabled="!isTaxedCustomer || isFree"
            class="w-full"
            :min="0"
            :precision="6"
            :step="1"
            @change="changePrice"
          />
        </el-form-item>
        <el-dropdown v-if="isTaxedCustomer" trigger="click">
          <el-link class="link" size="mini" :underline="false" type="primary">
            建议价格
            <el-icon><CirclePlus /></el-icon>
          </el-link>
          <template #dropdown>
            <el-dropdown-menu :show-timeout="50">
              <el-dropdown-item class="p0">
                <el-table border :data="taxedTable" class="w-full" size="small">
                  <el-table-column
                    prop="name"
                    label="价格项"
                    width="150"
                    align="center"
                  />
                  <el-table-column
                    prop="price"
                    label="含税单价"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    prop="rate"
                    label="税率"
                    width="100"
                    align="center"
                  />
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="税率">
          <el-input
            :model-value="
              reactData.sku.taxRate
                ? `${Math.round(reactData.sku.taxRate * 100)}%`
                : ''
            "
            disabled
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="税额">
          <el-input
            disabled
            :model-value="formatPrice(reactData.sku.taxAmount)"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="商品未税总金额">
          <el-input disabled :model-value="reactData.sku.freeTotalPrice" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="商品含税总金额">
          <el-input disabled :model-value="reactData.sku.taxTotalPrice" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="折扣类型">
          <el-select
            v-model="reactData.sku.discountConditionType"
            placeholder="请选择"
            disabled
            clearable
            filterable
          >
            <el-option
              v-for="item in dictList['discountConditionType']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣金额">
          <el-input-number
            v-model="reactData.sku.discountAmount"
            :precision="2"
            :step="1"
            :min="0"
            class="w-full"
            placeholder="请输入折扣金额"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>其他</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="6" class="text-center">
        <el-checkbox v-model="reactData.sku.customerDateSensitive" size="large"
          >客户交期敏感</el-checkbox
        >
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="客户期望送达日期" prop="customerDate">
          <el-date-picker
            v-model="reactData.sku.customerDate"
            :disabled-date="disabledDate"
            :disabled="!autoBatching"
            value-format="YYYY-MM-DD"
            type="date"
            class="w-full"
            placeholder="选择日期"
            @focus="focusDatePicker"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="领用人" prop="demandUser">
          <el-input
            v-model="reactData.sku.demandUser"
            placeholder="请输入领用人"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="需求部门" prop="demandDepartment">
          <el-input
            v-model="reactData.sku.demandDepartment"
            placeholder="请输入需求部门"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="reactData.sku.remark"
            placeholder="请输入备注"
            maxlength="1000"
            type="textarea"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="采购备注" prop="purchaseNote">
          <el-input
            v-model="reactData.sku.purchaseNote"
            placeholder="请输入采购备注"
            maxlength="100"
            type="textarea"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item v-if="!isFakeSketch" label="草稿单行备注" prop="comment">
          <el-input
            v-model="reactData.sku.comment"
            placeholder="请输入草稿单行备注"
            maxlength="100"
            type="textarea"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="不纳入需求挂起原因" prop="notAcceptDemandReasons">
          <el-select v-model="notAcceptDemandReasons" disabled multiple>
            <el-option
              v-for="item in dictList['notAcceptDemandReason']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="交货挂起原因" prop="dnOrderPendingReasons">
          <el-select v-model="dnOrderPendingReasons" disabled multiple>
            <el-option
              v-for="item in dictList['dnOrderPendingReason']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="showCustom">
      <el-row
        v-for="(custom, index) in (reactData.sku &&
          reactData.sku.customPropertyList) ||
        []"
        :key="index"
        :gutter="10"
      >
        <el-col :span="12">
          <el-form-item :label="`定制属性${index + 1}`">
            <el-input readonly disabled :model-value="custom.customProperty" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="custom.placeholder"
            :label="`定制属性备注${index + 1}`"
          >
            <el-input
              v-model="custom.customPropertyRemark"
              :placeholder="custom.placeholder"
              maxlength="50"
            />
          </el-form-item>
          <el-form-item v-else :label="`定制属性备注${index + 1}`">
            <el-input v-model="custom.customPropertyRemark" maxlength="50" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <el-row v-if="orderType === 'Z018'" :gutter="10">
      <el-col :span="12">
        <el-form-item label="租赁截止日期" prop="rentalDueDate">
          <el-date-picker
            v-model="reactData.sku.rentalDueDate"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="固定资产编号" prop="fixedAssetsId">
          <el-input
            v-model="reactData.sku.fixedAssetsId"
            placeholder="请输入固定资产编号"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DiffCustomerMaterial
      v-model:show-dialog="reactData.showDiffDialog"
      :result-material-obj="reactData.resultMaterialObj"
      :input-material-obj="reactData.inputMaterialObj"
      @submit="handleDiffMaterialRelation"
    />
  </el-form>
  <div class="ba-row-center">
    <el-button type="primary" @click="submitAndToLast"
      >保存并打开上一行</el-button
    >
    <el-button type="primary" @click="submit">确认保存</el-button>
    <el-button type="primary" @click="submitAndToNext"
      >保存并打开下一行</el-button
    >
    <el-button @click="cancel">取消</el-button>
  </div>
</template>

<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
