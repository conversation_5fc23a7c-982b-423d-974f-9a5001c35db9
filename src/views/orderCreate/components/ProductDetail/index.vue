<script lang="ts" setup>
import { computed, reactive } from 'vue';
import { useItemStore } from '@/stores/item';
import { generate } from '@/utils/uuid';
import { initFieldsShow } from '@/utils';
import { useCommonStore } from '@/stores/common';
import ItemDetail from './ItemDetail.vue';
import Detail from './Detail.vue';
import EnquiryRecord from './EnquiryRecord.vue';
import OrderRecord from './OrderRecord.vue';

const itemStore = useItemStore();
const commonStore = useCommonStore();

const orderFieldsSettings = computed(() => commonStore.orderFieldsSettings);
const selectedIndex = computed(() => itemStore.selectedIndex);
const visible = computed({
  get: () => itemStore.showSelectedItem,
  set: (val) =>
    itemStore.editStore({
      showSelectedItem: val,
    }),
});

const reactData = reactive({
  key1: generate(),
  key2: generate(),
  key3: generate(),
  activeName: 'detail',
});

const openDlg = () => {
  console.log(selectedIndex.value);
  reactData.key1 = generate();
  reactData.key2 = generate();
  reactData.key3 = generate();
};

const closeDialog = () => {
  visible.value = false;
};

const handleReset = () => {
  openDlg();
};

const showField = (prop?: string) => {
  return initFieldsShow('productDetail', prop)?.visible;
};
</script>

<template>
  <el-dialog
    v-model="visible"
    :show-close="false"
    title="商品详情"
    top="10px"
    width="860px"
    size="small"
    class="draft-detail"
    @open="openDlg"
    @closed="closeDialog"
  >
    <el-tabs v-model="reactData.activeName">
      <el-tab-pane v-if="showField('detail')" label="详细信息" name="detail">
        <div :key="reactData.key1">
          <ItemDetail
            v-if="Object.keys(orderFieldsSettings).length > 0"
            @close="closeDialog"
            @reset="handleReset"
          />
          <Detail v-else @close="closeDialog" @reset="handleReset" />
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="showField('enquiry')" label="询价记录" name="enquiry">
        <EnquiryRecord :key="reactData.key2" @close="closeDialog" />
      </el-tab-pane>
      <el-tab-pane v-if="showField('order')" label="订单记录" name="order">
        <OrderRecord :key="reactData.key3" @close="closeDialog" />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<style lang="scss">
.draft-detail {
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
