<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { storeToRefs } from 'pinia';

import {
  calculatePrice as calculatePriceApi,
  searchCustomerMaterialRelation,
} from '@/api/order';
import { formatPrice, initFieldsShow, isNull } from '@/utils/index';
import { IDetailData, ItemRowType, ItemType } from '@/types/item';
import DividerHeader from '@/components/DividerHeader.vue';
import { useItemStore } from '@/stores/item';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { PriceItems } from '@/types/price';
import ItemDetailOrder from './ItemDetailOrder.vue';
import ItemDetailWarehouse from './ItemDetailWarehouse.vue';
import ItemDetailPrice from './ItemDetailPrice.vue';
import ItemDetailOther from './ItemDetailOther.vue';

const emit = defineEmits([
  'close',
  'reset',
  'changeUrgent',
  'changeCustomerDateSensitive',
]);

const itemStore = useItemStore();
const customerStore = useCustomerStore();
const orderStore = useOrderStore();

const { customer, cusDetail } = storeToRefs(customerStore);
const { selectedItem, selectedIndex, itemList } = storeToRefs(itemStore);

const isTax = computed(() => cusDetail.value?.isTax);

const skuList = computed(() => itemList.value || []);

const isTaxedCustomer = computed(() => {
  return isTax.value === '1';
});

const refs_goodsDetailForm = ref<any>(null);

const defaultRules: any = {
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  directDeliverySupplier: [
    { required: true, message: '请输入直发信息', trigger: 'blur' },
  ],
  position: [{ required: true, message: '请选择库位', trigger: 'blur' }],
  factory: [{ required: true, message: '请选择工厂', trigger: 'blur' }],
};

const setRules = () => {
  const rules = initFieldsShow('productDetail', 'detail')?.rules;
  reactData.rules = Object.keys(rules).length > 0 ? rules : defaultRules;

  if (isTaxedCustomer.value) {
    reactData.rules = {
      ...reactData.rules,
      taxPrice: [
        { required: true, message: '请选择含税单价', trigger: 'blur' },
      ],
    };
  } else {
    reactData.rules = {
      ...reactData.rules,
      freeTaxPrice: [
        { required: true, message: '请选择未税单价', trigger: 'blur' },
      ],
    };
  }
  refs_goodsDetailForm.value.clearValidate();
};

const reactData: IDetailData = reactive({
  sku: {
    discountAmount: 0,
    discountConditionType: 'ZK01',
    rentalDueDate: '',
    fixedAssetsId: '',
    customerSpecificationModel: '',
    customerOrderNo: '',
    customerMaterialName: '',
    customerMaterialUnit: '',
    customerMaterialQuantity: 0,
    customerMaterialNo: '',
    quantity: 0,
    remark: '',
    purchaseNote: '',
    comment: '',
    position: '',
    sku: '',
    freeTaxPrice: 0,
    taxAmount: 0,
    taxRate: 0,
    taxPrice: 0,
    manufacturerNo: '',
    materialDescribe: '',
    quantityUnit: '',
    customerDateSensitive: false,
    demandUser: '',
    demandDepartment: '',
    customerDate: '',
    skuNo: '',
    packageInfoList: [],
    positionList: [],
    factory: '',
    directDeliverySupplier: {},
    factoryProductPriceVOMap: {},
    ...selectedItem.value,
    customerMaterialTableData: [],
  },
  currentConversion: null,
  materialList: [],
  rate: '',
  rules: {},
  loading: false,
  showDiffDialog: false,
  formatedSku: {},
  oldSku: {}, // 记录没搜索sku之前填的信息
  resultMaterialObj: {}, // 查出来的客户物料关系
  inputMaterialObj: {}, // 页面手动输入的客户物料关系
});

const isItemDisabled = computed(() => reactData.sku.itemEditable === '0');

onMounted(() => {
  setRules();
  initRate();
  searchMaterialRelation();
});

watch(
  () => selectedItem.value,
  (newVal) => {
    if (newVal) {
      reactData.sku = {
        ...reactData.sku,
        ...newVal,
      };
      initRate();
      searchMaterialRelation();
      setRules();
    }
  }
);

const updateCheckbox = (type: string) => {
  const everyValid = skuList.value.every((sku: ItemType) => sku[type]);
  const everyInvalid = skuList.value.every((sku: ItemType) => !sku[type]);

  const valid = skuList.value.some((sku: ItemType) => sku[type]);
  const invalid = skuList.value.some((sku: ItemType) => !sku[type]);
  console.log(
    type,
    everyValid,
    everyInvalid,
    valid,
    invalid,
    skuList.value.map((i: ItemType) => i[type])
  );
  if (everyValid) {
    customerStore.editStore({
      [type]: true,
    });
  } else {
    customerStore.editStore({
      [type]: false,
    });
  }
  if (valid && invalid) {
    customerStore.editStore({
      [type]: false,
    });
  }
  switch (type) {
    case 'customerDateSensitive':
      customerStore.editStore({
        customerDateSensitiveIndeterminate: false,
      });
      if (valid && invalid) {
        customerStore.editStore({
          customerDateSensitiveIndeterminate: true,
        });
      }
      break;
  }
};
const updateCustomerCheckbox = () => {
  updateCheckbox('customerDateSensitive');
};
const handleSubmit = async () => {
  // customerMaterialName:客户物料名称
  // customerMaterialNo  客户物料号
  // customerSpecificationModel  客户规格型号
  // customerOrderNo 客户行号
  const toHandleArr = [
    'customerMaterialNo',
    'customerMaterialName',
    'customerSpecificationModel',
    'customerOrderNo',
  ];
  toHandleArr.forEach((key) => {
    if ((reactData.sku as ItemType)[key]) {
      (reactData.sku as ItemType)[key] = (reactData.sku as ItemType)[key]
        .trim()
        .replaceAll(/\r\n|\r|\n/g, '');
    }
  });
  itemStore.updateItem({
    type: ItemRowType.all,
    index: selectedIndex.value,
    isTax: isTax.value,
    value: {
      ...reactData.sku,
    },
  });
  itemStore.getDeliveryDate();
  if (selectedIndex.value === 0) {
    await customerStore.changeClientDetail(true);
  }
  updateCustomerCheckbox();
};

const checkForm = (callback: any) => {
  refs_goodsDetailForm.value.validate((valid: boolean) => {
    const { skuNo, quantity, customerMaterialQuantity } = reactData.sku;
    if (
      !skuNo &&
      (!quantity || Number.parseFloat(quantity) === 0) &&
      (!customerMaterialQuantity ||
        Number.parseFloat(customerMaterialQuantity) === 0)
    ) {
      ElMessage.error('数量和客户物料数量不能同时为0！');
      return;
    }
    if (valid) {
      callback && callback();
    }
  });
};

const submit = () => {
  checkForm(() => {
    handleSubmit();
    emit('close');
  });
};

const submitAndToLast = () => {
  checkForm(() => {
    handleSubmit();
    if (selectedIndex.value > 0) {
      itemStore.editStore({
        selectedIndex: selectedIndex.value - 1,
      });
      emit('reset');
    } else {
      ElMessageBox.alert('上一行为空！', { type: 'error' });
    }
  });
};

const submitAndToNext = () => {
  checkForm(() => {
    handleSubmit();
    const len = itemList.value.length;
    if (len > selectedIndex.value + 1) {
      itemStore.editStore({
        selectedIndex: selectedIndex.value + 1,
      });
      emit('reset');
    } else {
      ElMessageBox.alert('下一行为空！', { type: 'error' });
    }
  });
};

const cancel = () => {
  refs_goodsDetailForm.value.resetFields();
  emit('close');
};

const searchMaterialRelation = async () => {
  try {
    const { skuNo } = reactData.sku;
    const { customerNumber } = customer.value;
    const data = [
      {
        customerCode: customerNumber,
        zkhSkuNo: skuNo,
      },
    ];
    reactData.loading = true;
    const res = await searchCustomerMaterialRelation({ queryList: data });
    if (res.code === 200) {
      if (Array.isArray(res.data) && res.data.length > 0) {
        reactData.sku.customerMaterialTableData = res.data;
        // if (reactData.sku.customerMaterialTableData.length === 1) {
        //   const [item] = reactData.sku.customerMaterialTableData;
        //   initMaterialInfo(item);
        // }
      } else {
        reactData.sku.customerMaterialTableData = [];
      }
    } else {
      reactData.sku.customerMaterialTableData = [];
    }
  } catch (error) {
    console.log(error);
  } finally {
    reactData.loading = false;
  }
};

const initRate = () => {
  const { skuUnitCount, customerSkuUnitCount } = reactData.sku;
  if (
    skuUnitCount &&
    customerSkuUnitCount &&
    Number.parseFloat(skuUnitCount) !== 0 &&
    Number.parseFloat(customerSkuUnitCount) !== 0
  ) {
    reactData.sku.materialRate = formatPrice(
      Number.parseFloat(customerSkuUnitCount) / Number.parseFloat(skuUnitCount),
      6
    );
    if (!reactData.sku.customerMaterialQuantity) {
      reactData.sku.customerMaterialQuantity = formatPrice(
        reactData.sku.quantity * reactData.sku.materialRate,
        6
      );
    }
  }
};

const calculatePrice = async () => {
  const isTax = customerStore.cusDetail?.isTax;
  const item = {
    discountAmount: reactData.sku.discountAmount || 0,
    itemNo: reactData.sku.idx,
    promotionalDiscountRate: isNull(reactData.sku.promotionalDiscountRate)
      ? 100
      : reactData.sku.promotionalDiscountRate,
    quantity: reactData.sku.quantity || 0,
    sku: reactData.sku.skuNo,
    taxRate: reactData.sku.taxRate || 0,
    unitPrice:
      isTax === '1' ? reactData.sku.taxPrice : reactData.sku.freeTaxPrice,
  };
  const data = {
    orderBasis: orderStore.orderData?.orderBasis,
    isTax,
    items: [item],
  };
  const res = await calculatePriceApi(data);
  if (res.code === 200) {
    return res.data;
  }
};

const changePrice = async () => {
  const priceData = await calculatePrice();
  if (priceData?.priceItems) {
    const foundItem = priceData.priceItems.find(
      (item: PriceItems) => String(reactData.sku.idx) === String(item.itemNo)
    );
    reactData.sku = {
      ...reactData.sku,
      ...foundItem,
    };
  }
};

const showField = (prop?: string) => {
  return initFieldsShow('productDetail', prop)?.visible;
};
const getFieldLabel = (prop?: string) => {
  return initFieldsShow('productDetail', prop)?.label;
};
</script>

<template>
  <el-form
    ref="refs_goodsDetailForm"
    v-loading="reactData.loading"
    :model="reactData.sku"
    :rules="reactData.rules"
    label-width="140px"
    :disabled="isItemDisabled"
  >
    <template v-if="showField('detailOrder')">
      <DividerHeader>{{
        getFieldLabel('detailOrder') || '商品信息'
      }}</DividerHeader>
      <ItemDetailOrder v-model="reactData.sku" @change-price="changePrice" />
    </template>

    <template v-if="showField('detailWarehouse')">
      <DividerHeader>{{
        getFieldLabel('detailWarehouse') || '仓位信息'
      }}</DividerHeader>
      <ItemDetailWarehouse
        v-model="reactData.sku"
        @change-price="changePrice"
      />
    </template>
    <template v-if="showField('detailPrice')">
      <DividerHeader>{{
        getFieldLabel('detailPrice') || '价格信息'
      }}</DividerHeader>
      <ItemDetailPrice v-model="reactData.sku" @change-price="changePrice" />
    </template>
    <template v-if="showField('detailOther')">
      <DividerHeader>{{
        getFieldLabel('detailOther') || '其他信息'
      }}</DividerHeader>
      <ItemDetailOther v-model="reactData.sku" />
    </template>
  </el-form>
  <div class="ba-row-center">
    <el-button type="primary" @click="submitAndToLast"
      >保存并打开上一行</el-button
    >
    <el-button type="primary" @click="submit">确认保存</el-button>
    <el-button type="primary" @click="submitAndToNext"
      >保存并打开下一行</el-button
    >
    <el-button @click="cancel">取消</el-button>
  </div>
</template>

<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
