<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from 'vue';
import { CirclePlus } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';

import { formatPrice, initFieldsShow } from '@/utils/index';
import { isFreeOrder } from '@/utils/orderType';
import { ItemType } from '@/types/item';
import { useCustomerStore } from '@/stores/customer';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
// import type { IDetailData, ItemType, MaterialRelationRow } from '@/types/item';
import type { CompanyType, OrderFieldsSettings } from '@/types/common';

const props = defineProps<{
  modelValue: ItemType;
}>();

const emit = defineEmits(['update:modelValue', 'changePrice']);

const data = useVModel(props, 'modelValue', emit);

const orderStore = useOrderStore();
const commonStore = useCommonStore();
const customerStore = useCustomerStore();

const { dictList } = storeToRefs(commonStore);
const { orderData } = storeToRefs(orderStore);
const { selectedSalesRange, cusDetail } = storeToRefs(customerStore);

const orderType = computed(() => orderData.value.orderType || '');

const isTax = computed(() => cusDetail.value?.isTax);

const distributionChannel = computed(
  () => selectedSalesRange.value.distributionChannel
);

const isTaxedCustomer = computed(() => {
  return isTax.value === '1';
});

const isFree = computed(() => {
  return isFreeOrder(orderType.value);
});

const unTaxedTable = computed(() => {
  const { factoryProductPriceVOMap, factory } = data.value as ItemType;
  if (factory && factoryProductPriceVOMap) {
    const code = Number(factory as CompanyType);
    const priceMap = factoryProductPriceVOMap[code];
    if (priceMap) {
      const { dealerMinPrice, suggestPrice, taxRate } = priceMap;
      const res = [
        {
          name: '建议销售价',
          price: suggestPrice ? formatPrice(suggestPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        },
      ];
      if (distributionChannel.value === '02') {
        res.push({
          name: '最低折扣价',
          price: dealerMinPrice ? formatPrice(dealerMinPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        });
      }
      return res;
    }
  }
  return [];
});
const taxedTable = computed(() => {
  const { factoryProductPriceVOMap, factory } = data.value;
  if (factory && factoryProductPriceVOMap) {
    const code = Number(factory as CompanyType);
    const priceMap = factoryProductPriceVOMap[code];
    if (priceMap) {
      const { dealerMinPrice, suggestPrice, taxRate } = priceMap;
      const res = [
        {
          name: '建议销售价',
          price: suggestPrice ? formatPrice(suggestPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        },
      ];
      if (distributionChannel.value === '02') {
        res.push({
          name: '最低折扣价',
          price: dealerMinPrice ? formatPrice(dealerMinPrice) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        });
      }
      return res;
    }
  }
  return [];
});

const changePrice = () => {
  emit('changePrice');
};
// const taxedChange = (value: number | undefined) => {
//   const taxRate = Number.parseFloat(data.value.taxRate);
//   const quantity = data.value.quantity;
//   data.value.freeTaxPrice = formatPrice((value as number) / (1 + taxRate));
//   data.value.taxAmount = data.value.freeTaxPrice * taxRate * quantity;
// };
// const unTaxedChange = (value: number | undefined) => {
//   value = Number(value);
//   const taxRate = Number.parseFloat(data.value.taxRate);
//   const quantity = data.value.quantity;
//   data.value.taxPrice = formatPrice((value as number) * (1 + taxRate));
//   data.value.taxAmount = (value as number) * taxRate * quantity;
// };

const fields = ref<OrderFieldsSettings[]>([]);
const getFields = () => {
  fields.value = initFieldsShow('productDetail', 'detailPrice')?.children;
};

onMounted(() => {
  getFields();
});

watchEffect(() => {
  fields.value?.forEach((item) => {
    if (item.prop === 'discountConditionType') {
      item.options = dictList.value?.discountConditionType?.map((item) => ({
        label: item.name,
        value: item.code,
        key: item.code,
      }));
    }
  });
});
</script>

<template>
  <el-row :gutter="10">
    <el-col v-for="item in fields" :key="item.prop" :span="item.span || 12">
      <div
        v-if="item.prop === 'freeTaxPrice'"
        :class="{ tax: !isTaxedCustomer }"
      >
        <el-form-item
          :label-width="item.labelWidth || '140px'"
          required
          :label="item.label || '未税单价'"
          prop="freeTaxPrice"
        >
          <el-input-number
            v-model="data.freeTaxPrice"
            :disabled="isTaxedCustomer || isFree || item.disabled"
            class="w-full"
            :min="0"
            :precision="6"
            :step="1"
            @change="changePrice"
          />
        </el-form-item>
        <el-dropdown v-if="!isTaxedCustomer" trigger="click">
          <el-link class="link" size="mini" :underline="false" type="primary">
            建议价格
            <el-icon><CirclePlus /></el-icon>
          </el-link>
          <template #dropdown>
            <el-dropdown-menu :show-timeout="50">
              <el-dropdown-item class="p0">
                <el-table
                  :data="unTaxedTable"
                  border
                  class="w-full"
                  size="small"
                >
                  <el-table-column
                    prop="name"
                    label="价格项"
                    width="150"
                    align="center"
                  />
                  <el-table-column
                    prop="price"
                    label="未税单价"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    prop="rate"
                    label="税率"
                    width="100"
                    align="center"
                  />
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div
        v-else-if="item.prop === 'taxPrice'"
        :class="{ tax: isTaxedCustomer }"
      >
        <el-form-item
          :label-width="item.labelWidth || '140px'"
          :label="item.label || '含税单价'"
        >
          <el-input-number
            v-model="data.taxPrice"
            :disabled="!isTaxedCustomer || isFree || item.disabled"
            class="w-full"
            :min="0"
            :precision="6"
            :step="1"
            @change="changePrice"
          />
        </el-form-item>
        <el-dropdown v-if="isTaxedCustomer" trigger="click">
          <el-link class="link" size="mini" :underline="false" type="primary">
            建议价格
            <el-icon><CirclePlus /></el-icon>
          </el-link>
          <template #dropdown>
            <el-dropdown-menu :show-timeout="50">
              <el-dropdown-item class="p0">
                <el-table border :data="taxedTable" class="w-full" size="small">
                  <el-table-column
                    prop="name"
                    label="价格项"
                    width="150"
                    align="center"
                  />
                  <el-table-column
                    prop="price"
                    label="含税单价"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    prop="rate"
                    label="税率"
                    width="100"
                    align="center"
                  />
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <el-form-item
        v-else
        :label="item.label"
        :prop="item.prop"
        :label-width="item.labelWidth || '140px'"
      >
        <div
          v-if="item.prop === 'promotionalDiscountRate'"
          class="flex justify-around items-center w-100%"
        >
          <el-input-number
            v-model="data.promotionalDiscountRate"
            placeholder="请输入折扣"
            :min="0"
            :max="100"
            :precision="2"
            :controls="false"
            :disabled="item.disabled"
            clearable
            style="width: 90%"
            @change="changePrice"
          />
          <span>%</span>
        </div>
        <el-select
          v-else-if="item.type === 'select'"
          v-model="data[item.prop]"
          :placeholder="`请选择${item.label}`"
          clearable
          :disabled="item.disabled"
        >
          <el-option
            v-for="opt in item.options"
            :key="opt.key"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
        <el-input-number
          v-else-if="item.type === 'number'"
          v-model="data[item.prop]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :min="item.min || 0"
          :max="item.max"
          :precision="item.precision || 6"
          :disabled="item.disabled"
          clearable
          :style="item.style || ''"
        />
        <el-input
          v-else-if="item.type === 'price'"
          v-model="data[item.prop]"
          clearable
          :disabled="item.disabled"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :style="item.style || ''"
        >
          {{ formatPrice(data[item.prop]) }}
        </el-input>
        <el-input
          v-else
          v-model="data[item.prop]"
          clearable
          :disabled="item.disabled"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :style="item.style || ''"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
