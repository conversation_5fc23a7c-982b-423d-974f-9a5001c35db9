<script lang="ts" setup>
import { computed, ref } from 'vue';
import uniq from 'lodash/uniq';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import BatchImport from '@/views/orderCreate/components/BatchImport.vue';
import { useCustomerStore } from '@/stores/customer';
import { useItemStore } from '@/stores/item';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import bus from '@/utils/bus';
import OrderPendingDialog from '@/components/OrderPendingDialog.vue';
import { initFieldsShow } from '@/utils';
import { getButtonAuth } from '@/utils/auth';
import type { ItemType } from '@/types/item';

const customerStore = useCustomerStore();
const itemStore = useItemStore();
const orderStore = useOrderStore();
const commonStore = useCommonStore();

const factoryList = computed(() => itemStore.factoryList);

const showBatchImport = ref(false);
const orderNumInput = ref('');
const uploadRef = ref();
const needCustomerRelCheck = ref(true); // 批量上传sku，是否校验多物料关系的参数
const uploadDialogVisible = ref(false);
const uploadFailContent = ref('');
const failExcelUrl = ref('');

const orderDataInfo = computed(() => orderStore.orderData);
const isHeaderDisabled = computed(() => orderStore.isHeaderDisabled);
const customerNumber = computed(
  () => customerStore.customer?.customerNumber || ''
);
const selectedSalesRange = computed(() => customerStore.selectedSalesRange);
const orderType = computed(() => orderStore.orderData?.orderType || '');
const isDraftDetail = computed(() => orderStore?.isDraftDetail);
const excelUrls = computed(() => commonStore.excelUrls || {});
const acceptFileType = computed(() => commonStore.acceptFileType || {});
const isTax = computed(() => customerStore.cusDetail?.isTax);
const skuList = computed(() => {
  return itemStore.itemList || [];
});
// 勾选可操作：过滤选中行中不可编辑的行
const multipleSelection = computed(() => {
  return (
    itemStore.selectedItemList?.filter((item) => item.itemEditable !== '0') ||
    []
  );
});

const downloadTemplate = () => {
  if (orderDataInfo.value.orderBasis === 'STOCK_CLEARANCE') {
    window.open(excelUrls.value.batchImportLine_STOCK_CLEARANCE);
  } else {
    window.open(excelUrls.value.batchImportLine);
  }
};

const setPageLoading = (loading: boolean) => {
  orderStore.editStore({
    pageLoading: loading,
  });
};

const handleBeforeUpload = () => {
  setPageLoading(true);
};

const getDeliveryDate = async () => {
  setPageLoading(true);
  await itemStore.getDeliveryDate();
  setPageLoading(false);
};

const openDetail = () => {
  if (orderNumInput.value) {
    const numInput = String(orderNumInput.value);
    if (numInput) {
      const foundIdx = skuList.value.findIndex(
        (item: ItemType) =>
          item.sketchOrderItemNo === numInput ||
          String(item.idx) === numInput ||
          item.skuNo === numInput
      );
      if (foundIdx > -1) {
        itemStore.editStore({
          showSelectedItem: true,
          selectedIndex: foundIdx,
        });
        return;
      }
    }
    ElMessage.warning('输入有误，请重新输入！');
  } else {
    ElMessage.warning('请输入项目行或商品编号！');
  }
};

const downloadFailExcelUrl = () => {
  window.open(failExcelUrl.value);
};

const fileList = ref([]);
const uploadAgain = () => {
  try {
    needCustomerRelCheck.value = false;
    const action = () => {
      if (fileList.value.length > 0) {
        uploadRef.value.fileList = fileList.value;
      }
      const length = uploadRef.value.fileList?.length - 1;
      uploadRef.value.fileList[length].status = 'ready';
      uploadRef.value.submit();
    };
    setTimeout(action, 200);
  } catch (error) {
    console.log(error);
  }
};

const handeUploadSuccess = (res: any) => {
  if (uploadDialogVisible.value) {
    uploadDialogVisible.value = false;
  }
  needCustomerRelCheck.value = true;
  if (res && res.code === 200) {
    const { failSkuList, skuDetailVOList } = res.data;
    const hasItem = skuList.value.length > 0;
    const successCallback = async () => {
      const result = await itemStore.upload({
        factoryList: factoryList.value,
        items: skuDetailVOList,
      });
      if (result && result.length > 0) {
        bus.emit('updateCheckbox', {
          rows: itemStore.selectedItemList,
          checked: true,
        });
        const goodsItem = result.find((item: ItemType) => item.skuNo);
        if (hasItem) {
          getDeliveryDate();
        } else if (goodsItem?.productGroup) {
          const { salesOrganization, distributionChannel } =
            selectedSalesRange.value || {};
          await customerStore.getClientDetail({
            customerNumber: customerNumber.value,
            productGroup: goodsItem?.productGroup,
            distributionChannel,
            salesOrganization,
            receiverContactId:
              customerStore.contactData?.receiverContact?.contactId || '',
          });
          getDeliveryDate();
        }
        if (orderDataInfo.value.orderBasis === 'STOCK_CLEARANCE') {
          await itemStore.getStockSkuInfo(itemStore.itemList);
          itemStore.updatePrice();
        }
      }
      setTimeout(() => {
        try {
          uploadRef.value.clearFiles();
        } catch (error) {
          console.log(error);
        }
      }, 800);
    };
    // 多物料关系报错提示
    if (res.data?.failExcelUrl) {
      uploadDialogVisible.value = true;
      uploadFailContent.value =
        '部分数据行匹配客户物料关系存在异常，请确认【继续建单】或者【下载物料关系】';
      failExcelUrl.value = res.data?.failExcelUrl;
      setPageLoading(false);
      return;
    }
    if (failSkuList && failSkuList.length > 0) {
      const failLine = failSkuList.length;
      const successLine = skuDetailVOList.length;
      const skus = uniq(failSkuList).join(',');
      ElMessageBox.alert(
        `导入成功商品行数量为${successLine}行，失败数量为${failLine}行。` +
          `${skus}`,
        '错误',
        { confirmButtonText: '确定' }
      );
      setPageLoading(false);
    } else {
      successCallback();
    }
  } else if (res && res.code !== 200 && res.msg) {
    if (res?.data?.failExcelUrl) {
      ElMessageBox.confirm(res.msg, '操作提示', {
        confirmButtonText: '下载报错文档',
        cancelButtonText: '确定',
        type: 'error',
      }).then(() => {
        window.open(res.data.failExcelUrl);
      });
    } else {
      ElMessageBox.alert(res.msg, '错误', {
        type: 'error',
        dangerouslyUseHTMLString: true,
      });
    }
    setPageLoading(false);
  }
};

const uploadQueryUrl = computed(() => {
  const { salesOrganization, distributionChannel, productGroup } =
    selectedSalesRange.value || {};
  const queryStr: string[] = [];
  let url = '';
  if (
    salesOrganization &&
    distributionChannel &&
    productGroup &&
    customerNumber
  ) {
    const params: any = {
      customerNo: customerNumber.value,
      salesOrganization,
      distributionChannel,
      productGroup,
      orderType: orderType.value,
      needCustomerRelCheck: needCustomerRelCheck.value,
    };
    Object.keys(params).forEach((key) => {
      queryStr.push(`${key}=${params[key]}`);
    });
    if (queryStr.length > 0) {
      const s = queryStr.join('&');
      url += `?${s}`;
    }
  }
  return url;
});

const batchImportAction = computed(() => {
  if (orderDataInfo.value.orderBasis === 'STOCK_CLEARANCE') {
    return `/api-opc/v1/excel/batchImportSku/stockClear${uploadQueryUrl.value}`;
  }
  return `/api-opc/v1/excel/batchImportSku${uploadQueryUrl.value}`;
});

const removeMultiSku = () => {
  const firstItem = skuList.value[0];
  const hasRemoveFirstLine =
    multipleSelection.value &&
    multipleSelection.value.length > 0 &&
    multipleSelection.value.find(
      (item: ItemType) => item.idx === firstItem.idx
    );
  console.log('hasRemoveFirstLine', hasRemoveFirstLine);
  ElMessageBox.confirm('此操作将删除所选的商品, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    setPageLoading(true);
    itemStore.removeItemList({
      value: multipleSelection.value.map((m: ItemType) => m.idx) as number[],
      isTax: isTax.value,
    });
    if (hasRemoveFirstLine && skuList.value.length > 0) {
      const { salesOrganization, distributionChannel } =
        selectedSalesRange.value || {};
      const goodsItem = skuList.value[0];
      if (goodsItem) {
        await customerStore.getClientDetail({
          customerNumber: customerNumber.value,
          productGroup: goodsItem.productGroup,
          distributionChannel,
          salesOrganization,
          receiverContactId:
            customerStore.contactData?.receiverContact?.contactId || '',
        });
        getDeliveryDate();
      }
    } else {
      getDeliveryDate();
    }
  });
};

const showOrderPendingDialog = ref(false);
const pendingType = ref('');
const handlePendingDialog = (type: string) => {
  showOrderPendingDialog.value = true;
  pendingType.value = type;
};
const handlePendingSubmit = (
  notAcceptDemandReasons: string[],
  dnOrderPendingReasons: string[]
) => {
  multipleSelection.value.forEach((item) => {
    const found = skuList.value.find((sku) => sku.idx === item.idx);
    if (found) {
      if (pendingType.value === 'pending') {
        found.notAcceptDemandReasons = Array.from(
          new Set(
            (found.notAcceptDemandReasons || []).concat(notAcceptDemandReasons)
          )
        );
        found.dnOrderPendingReasons = Array.from(
          new Set(
            (found.dnOrderPendingReasons || []).concat(dnOrderPendingReasons)
          )
        );
      } else {
        found.notAcceptDemandReasons = (
          found.notAcceptDemandReasons || []
        ).filter((item: string) => {
          return !notAcceptDemandReasons.includes(item);
        });
        found.dnOrderPendingReasons = (
          found.dnOrderPendingReasons || []
        ).filter((item: string) => {
          return !dnOrderPendingReasons.includes(item);
        });
      }
    }
  });
};

const findItemInDictList = (type: string, code: string) => {
  const item = commonStore.dictList[type];
  return item?.find((item) => item.status === 'normal' && item.code === code);
};

// 某一商品行存在挂起原因
const hasPendingReason = computed(() => {
  return multipleSelection.value?.some((item) => {
    const found = skuList.value.find((sku) => sku.idx === item.idx);
    const hasNotAcceptDemandReason = (found?.notAcceptDemandReasons || []).some(
      (code: string) => findItemInDictList('notAcceptDemandReason', code)
    );
    const hasDnOrderPendingReason = (found?.dnOrderPendingReasons || []).some(
      (code: string) => findItemInDictList('dnOrderPendingReason', code)
    );
    return hasNotAcceptDemandReason || hasDnOrderPendingReason;
  });
});

const checkedNotAcceptDemandReasons = computed(() => {
  const list = multipleSelection.value.flatMap((item) => {
    const found = skuList.value.find((sku) => sku.idx === item.idx);
    return found?.notAcceptDemandReasons || '';
  });
  return Array.from(new Set(list));
});
const checkedDnOrderPendingReasons = computed(() => {
  const list = multipleSelection.value.flatMap((item) => {
    const found = skuList.value.find((sku) => sku.idx === item.idx);
    return found?.dnOrderPendingReasons || '';
  });
  return Array.from(new Set(list));
});

const showField = (prop?: string) => {
  return initFieldsShow('skuInfo', prop)?.visible;
};
const disabledField = (prop?: string) => {
  return initFieldsShow('skuInfo', prop)?.disabled;
};

const showButton = (prop?: string, buttonName?: string) => {
  return (
    initFieldsShow('skuInfo', prop)?.visible &&
    getButtonAuth(
      '销售跟单',
      `${isDraftDetail.value ? '草稿' : '创建'}_${buttonName}`
    )
  );
};
</script>

<template>
  <div>
    <div class="button-group">
      <div v-if="showField('searchDetail')" class="input-order">
        <el-input v-model="orderNumInput" placeholder="项目行/商品编号">
          <template #append>
            <el-button :icon="Search" @click="openDetail" />
          </template>
        </el-input>
      </div>
      <el-button
        v-if="
          /z001|z006|z007|z012/gim.test(orderType || '') &&
          showButton('pending', '订单挂起')
        "
        type="primary"
        :disabled="multipleSelection.length === 0 || disabledField('pending')"
        @click="handlePendingDialog('pending')"
      >
        订单挂起
      </el-button>
      <el-button
        v-if="
          /z001|z006|z007|z012/gim.test(orderType || '') &&
          showButton('cancelPendinng', '取消挂起')
        "
        type="primary"
        :disabled="
          multipleSelection.length === 0 ||
          !hasPendingReason ||
          disabledField('cancelPendinng')
        "
        @click="handlePendingDialog('cancel')"
      >
        取消挂起
      </el-button>
      <el-button
        v-if="showButton('removeMultiSku', '批量删除商品行')"
        type="danger"
        plain
        :disabled="
          multipleSelection.length === 0 || disabledField('removeMultiSku')
        "
        @click="removeMultiSku"
      >
        批量删除商品行
      </el-button>
      <el-popover
        v-if="
          orderDataInfo.hasUnderApproval &&
          showButton('batchImport', '快速导入商品')
        "
        placement="top"
        title=""
        width="400"
        trigger="hover"
      >
        <span>存在正在审批中的审批单，不支持快速导入商品！</span>
        <template #reference>
          <el-button type="default" plain>快速导入商品</el-button>
        </template>
      </el-popover>
      <el-button
        v-else-if="showButton('batchImport', '快速导入商品')"
        type="primary"
        :disabled="
          !selectedSalesRange?.salesOrganization ||
          isHeaderDisabled ||
          disabledField('batchImport')
        "
        @click="showBatchImport = true"
      >
        快速导入商品
      </el-button>
      <el-button
        v-if="showButton('downloadTemplate', '下载导入模板')"
        type="primary"
        class="m-r-10px"
        :disabled="disabledField('downloadTemplate')"
        @click="downloadTemplate"
      >
        下载导入模板
      </el-button>
      <el-popover
        v-if="
          orderDataInfo.hasUnderApproval &&
          showButton('uploadTemplate', '上传导入模板')
        "
        placement="top"
        title=""
        width="400"
        trigger="hover"
      >
        <span>存在正在审批中的审批单，不支持上传导入模板！</span>
        <template #reference>
          <el-button type="default" :disabled="isHeaderDisabled" plain
            >上传导入模板</el-button
          >
        </template>
      </el-popover>
      <el-upload
        v-else-if="showButton('uploadTemplate', '上传导入模板')"
        ref="uploadRef"
        v-model:file-list="fileList"
        :accept="acceptFileType.soCommonType"
        class="upload"
        :action="batchImportAction"
        :disabled="!selectedSalesRange?.salesOrganization"
        :multiple="false"
        :limit="1"
        :show-file-list="true"
        :before-upload="handleBeforeUpload"
        :on-success="handeUploadSuccess"
      >
        <el-button
          type="primary"
          :disabled="isHeaderDisabled || disabledField('uploadTemplate')"
          >上传导入模板</el-button
        >
      </el-upload>
    </div>
    <el-dialog
      v-model="showBatchImport"
      title="快速导入商品"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
    >
      <BatchImport @close="showBatchImport = false" />
    </el-dialog>
    <el-dialog
      v-model="uploadDialogVisible"
      title="操作提示"
      append-to-body
      center
      width="600px"
    >
      <div class="tips-container">{{ uploadFailContent }}</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="uploadAgain">继续建单</el-button>
          <el-button type="warning" @click="downloadFailExcelUrl"
            >下载物料关系</el-button
          >
        </div>
      </template>
    </el-dialog>
    <OrderPendingDialog
      v-if="showOrderPendingDialog"
      v-model:show-dialog="showOrderPendingDialog"
      :pending-type="pendingType"
      :checked-not-accept-demand-reasons="checkedNotAcceptDemandReasons"
      :checked-dn-order-pending-reasons="checkedDnOrderPendingReasons"
      @submit="handlePendingSubmit"
    />
  </div>
</template>

<style lang="scss" scoped>
.input-order {
  display: inline-block;
  margin-right: 10px;
  width: 160px;
}
.button-group {
  text-align: right;
}
.upload {
  display: inline;
}
</style>
