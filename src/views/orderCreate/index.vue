<!-- eslint-disable eqeqeq -->
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { cloneDeep, flatten, isEmpty } from 'lodash';
import {
  createOrderByDraftApi,
  createOrderByFormalApi,
  createWorkOrderApi,
  getClientDetail as getClientDetailAPI,
  getDraftDetail,
  saveDraft,
  searchContactListByGroup,
  transformSoFailDataPoint,
} from '@/api/order';
import {
  Z003Transform,
  formatSubmitData,
  formatValidators,
  getOrderOptUrl,
  handleDraftResponse,
  initFieldsShow,
  isObjectNull,
} from '@/utils/index';
import Header from '@/views/orderCreate/components/Header.vue';
import CustomerInfo from '@/views/orderCreate/components/CustomerInfo.vue';
import DeliveryInfo from '@/views/orderCreate/components/DeliveryInfo.vue';
import OtherInfo from '@/views/orderCreate/components/OtherInfo.vue';
import EditCustomer from '@/views/orderCreate/components/EditCustomer.vue';
import EditOrderMore from '@/views/orderCreate/components/EditMore/index.vue';
import DeliverySite from '@/views/orderCreate/components/DeliverySite.vue';
import Item from '@/views/orderCreate/components/Item.vue';
import DividerHeader from '@/components/DividerHeader.vue';
import RowMore from '@/components/RowMore.vue';

import { id } from '@/utils/id';
import { generate } from '@/utils/uuid';
import { useDeliveryStore } from '@/stores/delivery';
import { useInvoiceStore } from '@/stores/invoice';
import { useOrderStore } from '@/stores/order';
import { useCommonStore } from '@/stores/common';
import { useCustomerStore } from '@/stores/customer';
import { useItemStore } from '@/stores/item';
import {
  isForecastOrder,
  isFreeOrder,
  isInnerOrderReason,
  isServiceOrder,
} from '@/utils/orderType';
import { getFactoryList } from '@/utils/item';
import request from '@/utils/request';
import { getButtonAuth } from '@/utils/auth';
import * as validators from '@/utils/rules';
import type {
  ApiResponseType,
  ContactType,
  DraftOrderData,
  OrderTemplateKeyJson,
  SalesDictType,
} from '@/types/common';
import type { ItemType } from '@/types/item';
import type { ClientDetail, ItemResult } from '@/types/order';

const route = useRoute();
const orderStore = useOrderStore();
const commonStore = useCommonStore();
const customerStore = useCustomerStore();
const deliveryStore = useDeliveryStore();
const invoiceStore = useInvoiceStore();

const itemStore = useItemStore();

const {
  dictList,
  orderServiceDict,
  mmDictList,
  sapReturnOrderValidator,
  costCenterList,
  holidaysList,
  orderFieldsSettings,
  isShowCombinedDelivery,
} = storeToRefs(commonStore);
const {
  customer,
  selectedSalesRange,
  cusDetail,
  customerDateSensitive,
  orderSource,
  customerReferenceNo,
  contactData,
} = storeToRefs(customerStore);
const {
  isDraftDetail,
  isFakeSketch,
  pageLoading,
  categoryCode,
  orderData,
  bossId,
  isHeaderDisabled,
} = storeToRefs(orderStore);

const { deliveryData } = storeToRefs(deliveryStore);
const { invoiceData } = storeToRefs(invoiceStore);
const { itemList, selectedItemList } = storeToRefs(itemStore);

const isAllItemDisabled = computed(() =>
  orderData.value?.items?.every((item: ItemType) => item.itemEditable === '0')
);
const orderReason = computed(() => deliveryData.value.orderReason);

const isTax = computed(() => customerStore.cusDetail?.isTax);
const isTaxedCustomer = computed(() => isTax.value === '1');
// 较好的做法是表单项使用form绑定的model，这里model只用于表单rules校验，因此下面只列出了有校验的表单项
const formModel = computed(() => ({
  customer: customer.value,
  distributionChannel: selectedSalesRange.value.distributionChannel,
  selectedSalesRange: selectedSalesRange.value,
  receiverContact: contactData.value.receiverContact,
  orderContact: contactData.value.orderContact,
  receivingInvoiceContact: invoiceData.value.receivingInvoiceContact,
  orderReason: deliveryData.value.orderReason,
  sapReturnDnNo: deliveryData.value.sapReturnDnNo,
  deliverySensitivityInterval: deliveryData.value.deliverySensitivityInterval,
  combinedDelivery: deliveryData.value.combinedDelivery,
  comment: orderData.value.comment,
}));

const compRules = computed(() => {
  // 配置化订单的校验规则
  if (Object.keys(orderFieldsSettings.value).length > 0) {
    const orderFieldsRules = initFieldsShow('formRules');
    if (Object.keys(orderFieldsRules).length > 0) {
      return formatValidators(orderFieldsRules);
    }
  }
  let rules: any = {
    customer: [{ required: true, message: '请选择客户', trigger: 'blur' }],
    distributionChannel: [
      { required: true, message: '请选择直/分销渠道', trigger: 'blur' },
    ],
    selectedSalesRange: [
      {
        required: true,
        validator: validators.checkSalesRange,
        trigger: 'blur',
      },
    ],
  };
  const contactRule = {
    receiverContact: [
      {
        required: true,
        message: '请选择收货联系人',
        trigger: ['blur', 'change'],
      },
    ],
    orderContact: [
      {
        required: true,
        message: '请选择订单联系人',
        trigger: ['blur', 'change'],
      },
    ],
    receivingInvoiceContact: [
      {
        required: true,
        message: '请选择收票联系人',
        trigger: ['blur', 'change'],
      },
    ],
  };
  if (!isForecastOrder(categoryCode.value) && categoryCode.value !== 'Z014') {
    rules = {
      ...rules,
      ...contactRule,
    };
  }

  const categoryList = ['Z001', 'Z002', 'Z012', 'Z014', 'ZEV1', 'ZEV3'];
  if (!categoryList.includes(categoryCode.value)) {
    rules = {
      ...rules,
      orderReason: [
        {
          required: true,
          message: '请选择订单原因',
          trigger: ['blur', 'change'],
        },
      ],
    };
  }
  if (!sapReturnOrderValidator.value) {
    rules = {
      ...rules,
      sapReturnDnNo: [
        { required: true, message: '请输入sap退货交货单', trigger: 'blur' },
      ],
    };
  }
  // to-do 合单发货灰度
  if (isShowCombinedDelivery.value) {
    rules = {
      ...rules,
      combinedDelivery: [
        { required: true, message: '请选择合单发货', trigger: 'blur' },
      ],
    };
  }
  if (deliveryData.value.deliverySensitivity) {
    rules = {
      ...rules,
      deliverySensitivityInterval: [
        { required: true, message: '请输入', trigger: 'blur' },
      ],
    };
  }
  // if (isDraftDetail.value) return contactRule;
  return rules;
});

watch(
  () => compRules.value,
  () => {
    console.log(compRules.value);
    setTimeout(() => {
      thisReforderForm.value.clearValidate();
    }, 200);
  }
);

const showEditMore = ref(false);

const viewEditMore = () => {
  if (Object.keys(customer.value).length === 0) {
    return ElMessage.warning({ message: '请选择客户' });
  }
  showEditMore.value = true;
};

const toggleMap = reactive({
  deliveryInfo: false,
  customerInfo: false,
  deliverySite: false,
});
const handleToggle = (key: keyof typeof toggleMap, status: boolean) => {
  toggleMap[key] = status;
};

const formatBoolean = (data: any) => {
  if (typeof data === 'object') {
    data.urgent = data.urgent === 'X' ? true : false;
    data.customerDateSensitive =
      data.customerDateSensitive === 'X' ? true : false;
  }
  if (Array.isArray(data.items)) {
    data.items.forEach((item: any) => {
      item.urgent = item.urgent === 'X' ? true : false;
      item.customerDateSensitive =
        item.customerDateSensitive === 'X' ? true : false;
      item.refuseSystemDeliveryDate =
        item.refuseSystemDeliveryDate === 'X' ? true : false;
    });
  }
};

async function getClientDetail(data: ItemType) {
  const {
    customerNo,
    distributionChannel,
    productGroup,
    salesOrganization,
    receiverContact,
  } = data;
  try {
    const res = await getClientDetailAPI(
      customerNo,
      distributionChannel,
      productGroup,
      salesOrganization,
      receiverContact
    );
    if (res.code === 200) {
      return res.data;
    }
  } catch (error) {
    console.log(error);
  }
}

const initClientDetail = (data: ClientDetail) => {
  if (data) {
    orderStore.editStore({
      orderData: {
        ...orderStore.orderData,
        dnOrderPendingReasons: data.dnOrderPendingReasons, // 默认取客户中心上的交货挂起原因
      },
    });
  }
};

const initCustomerStore = (data: DraftOrderData) => {
  const {
    distributionChannel,
    distributionChannelName,
    productGroup,
    productGroupName,
    salesOrganization,
    salesOrganizationName,
    paymentTerm,
    sellerMap,
    customerServiceMap,
    vflag,
    customerId,
    customerName,
    customerNo,
    urgent,
    currency,
    exchangeRate,
    customerDateSensitive,
    isTax,
    serviceCenterSelfTransport,
    sellerId,
    customerServiceId,
    customerReferenceNo,
    acceptSupplierDelivery,
  } = data;
  let currencySymbol = '';
  if (dictList.value && dictList.value.currencySymbol) {
    const findOne: any =
      dictList.value.currencySymbol.find(
        (item) => item.parentCode === currency
      ) || {};
    if (findOne) {
      currencySymbol = findOne.code;
    }
  }
  const saleOrgVO = {
    distributionChannel,
    distributionChannelName,
    productGroup,
    productGroupName,
    salesOrganization,
    salesOrganizationName,
  };
  const customer = {
    customerId,
    customerName,
    customerNo,
    customerNumber: customerNo,
    currency,
    exchangeRate,
    currencySymbol,
  };
  let customerDateSensitiveIndeterminate = true;
  if (data.items && data.items.length > 0) {
    const customerDateSensitive = data.items[0].customerDateSensitive;
    const isSame = data.items.every(
      (item: any) => item.customerDateSensitive === customerDateSensitive
    );
    customerDateSensitiveIndeterminate = isSame ? false : true;
  }
  customerStore.editStore({
    cusDetail: {
      paymentTerm,
      sellerMap,
      vflag,
      customerServiceMap,
      saleOrgVO,
      isTax,
      serviceCenterSelfTransport,
      sellerId,
      customerServiceId,
      acceptSupplierDelivery,
    },
    customer,
    previousCustomer: customer,
    urgent,
    customerDateSensitive,
    customerDateSensitiveIndeterminate,
    customerReferenceNo,
  });
};

const initContactStore = (data: DraftOrderData, contactList: ContactType[]) => {
  const {
    receiverAddress,
    receiverPhone,
    receiverName,
    receiverContact,
    receiverProvinceCode,
    receiverCityCode,
    receiverDistrictCode,
    receiverAddressId,
    orderContactAddress,
    orderContactPhone,
    orderContactName,
    orderContact,
    receivingInvoiceAddress,
    receivingInvoicePhone,
    receivingInvoiceName,
    receivingInvoiceContact,
  } = data;

  const receiverList: ContactType[] = cloneDeep(contactList);
  if (receiverContact) {
    const receiver = findInContactList(receiverContact, contactList);
    if (!receiver) {
      receiverList.unshift({
        address: receiverAddress,
        contactPhone: receiverPhone,
        contactName: receiverName,
        contactId: receiverContact,
      } as ContactType);
    }

    const receiverContactDetail = receiverList.find(
      (item) => item.contactId == receiverContact
    );
    customerStore.editStore({
      contactData: {
        receiverContact: {
          ...receiverContactDetail,
          receiverProvinceCode,
          receiverCityCode,
          receiverDistrictCode,
          receiverAddressId,
        },
        receiverPhone: receiverContactDetail!.contactPhone,
        receiverAddress: receiverContactDetail!.address,
      },
    });
  }
  const orderList: ContactType[] = cloneDeep(contactList);
  if (orderContact) {
    const order = findInContactList(orderContact, contactList);
    if (!order) {
      orderList.unshift({
        address: orderContactAddress,
        contactPhone: orderContactPhone,
        contactName: orderContactName,
        contactId: orderContact,
      } as ContactType);
    }
    // 用==而不是===，是因为详情接口和合同列表接口的contactId分别是number、string，例如'123456'和123456
    const orderContactDetail = orderList.find(
      (item) => item.contactId == orderContact
    );
    customerStore.editStore({
      contactData: {
        orderContact: orderContactDetail,
        orderContactName: orderContactDetail!.contactName,
        orderContactPhone: orderContactDetail!.contactPhone,
      },
    });
  }
  customerStore.editStore({
    contactData: {
      receiverContactList: [...receiverList],
      orderContactList: [...orderList],
    },
  });

  // 由于收票联系人在编辑更多-发票信息里有用到，所以存在invoice store里
  const invoiceList: ContactType[] = cloneDeep(contactList);
  if (receivingInvoiceContact) {
    const invoice = findInContactList(receivingInvoiceContact, contactList);
    if (!invoice) {
      invoiceList.unshift({
        address: receivingInvoiceAddress,
        contactPhone: receivingInvoicePhone,
        contactName: receivingInvoiceName,
        contactId: receivingInvoiceContact,
      } as ContactType);
    }

    const invoiceContactDetail = invoiceList.find(
      (item) => item.contactId == receivingInvoiceContact
    );

    invoiceStore.updateInvoiceData({
      receivingInvoiceContact: invoiceContactDetail,
      invoicePhone: invoiceContactDetail!.contactPhone,
      invoiceAddress: invoiceContactDetail!.address,
    });
  }
  invoiceStore.updateInvoiceData({
    receivingInvoiceContactList: [...invoiceList],
  });
};

const showCustomerEdit = ref(false);
const detailKey = ref('');
const updateCustomerEdit = (val: boolean) => {
  showCustomerEdit.value = val;
  detailKey.value = generate();
};

const initItemStore = async (data: DraftOrderData) => {
  const factoryList = getFactoryList(selectedSalesRange.value, dictList.value);
  const obj = {
    itemList: data.items || [],
    factoryList,
  };
  // itemStore.editStore(obj);
  itemStore.editStore({
    factoryList,
  });
  await itemStore.initDraftItemList(obj);
  itemStore.getDeliveryDate();
};

const initCostCenter = (data: DraftOrderData) => {
  const { salesOrganization, orderType } = data;
  if (orderType === 'Z007') {
    commonStore.queryCostCenter(salesOrganization);
  }
};

const thisReforderForm = ref<any>(null);
const thisRefItem = ref<any>(null);

const initDraftDetail = async () => {
  const orderNo = route.params.orderNo as string;
  if (!orderNo) return ElMessage.error('缺少订单号！');
  orderStore.editStore({
    bossId: orderNo,
  });
  const res = await getDraftDetail({ sketchOrderNo: orderNo });
  if (typeof res?.data === 'string') {
    orderStore.editStore({
      pageLoading: false,
    });
    return ElMessageBox.alert(res?.data, '操作提示', {
      type: 'warning',
      confirmButtonText: '确定',
    });
  }
  if (res.code === 200 && res.data) {
    // 订单详情
    const {
      salesOrganization,
      orderType,
      customerNo,
      orderBasis,
      orderReason,
      orderSource,
    } = res.data;
    orderStore.checkSelectWhsAuth({
      customerNo,
      orderBasis,
      orderReason,
      orderType,
      salesOrg: salesOrganization,
      orderSource,
    });
    orderStore.editStore({
      orderData: res.data,
      companyCode: Z003Transform(salesOrganization),
      categoryCode: orderType,
    });
    formatBoolean(res.data);
    // // 分发各模块的数据到对应的store,并做一些格式化处理
    initCustomerStore(res.data);
    initDeliveryStore(res.data);
    initInvoiceStore(res.data);
    const reqArr = [queryContact(''), getClientDetail(res.data)];
    const [contacts, clientDetail] = await Promise.all(reqArr);
    // initContactStore依赖queryContact查询结果
    initContactStore(res.data, contacts);
    initClientDetail(clientDetail);
    initItemStore(res.data);
    initCostCenter(res.data);
    try {
      thisReforderForm.value.clearValidate();
    } catch (error) {
      console.log(error);
    }
  } else {
    ElMessageBox.alert(res.msg, '错误', {
      confirmButtonText: '确定',
    });
  }
};

const queryContact = async (contactName: string) => {
  const { salesOrganization, productGroup, distributionChannel } =
    cusDetail.value?.saleOrgVO || {};
  const customerCode = customer.value.customerNumber || '';
  const res = await searchContactListByGroup({
    customerCode,
    contactName,
    distributionChannel,
    productGroup,
    salesOrganization,
  });
  if (res?.data?.records?.length > 0) {
    return res.data.records;
  }
  return [];
};

function findInContactList(id: string, contactList: ContactType[]) {
  // eslint-disable-next-line eqeqeq
  return contactList.find((contact: ContactType) => contact.contactId == id);
}

const initDeliveryStore = (data: DraftOrderData) => {
  deliveryStore.initDeliveryData(data);
};

const initInvoiceStore = (data: DraftOrderData) => {
  invoiceStore.initInvoiceData(data);
};

const invoiceCheck = () => {
  if (
    !isForecastOrder(categoryCode.value) &&
    isObjectNull(invoiceData.value.receivingInvoiceContact) &&
    categoryCode.value !== 'Z014'
  ) {
    const message = '收票联系人不能为空！';
    ElMessage.error({ message });
    throw { type: 'check', msg: 'setPageLoading' };
  }
};

const skuListCheck = () => {
  // 只校验勾选且未创建订单的行
  if (!selectedItemList.value || selectedItemList.value.length === 0) {
    const message = '商品信息不能为空！';
    ElMessage.error({ message });
    throw { type: 'check', msg: 'skuListCheck 商品信息不能为空！' };
  }
  if (selectedItemList.value) {
    let quantityError = false;
    let positionError = false;
    let factoryError = false;
    let customerDateError = false;
    let taxedPriceError = false;
    let freeTaxPriceError = false;
    let costCenterError = false;
    let generalLedgerError = false;
    let customerMaterialNameError = false;
    const errList: string[] = [];
    selectedItemList.value.forEach((selectItem: ItemType) => {
      const sku = (itemList.value.find(
        (item: ItemType) =>
          item.idx === selectItem.idx &&
          (!item.itemEditable || item.itemEditable === '1')
      ) || {}) as ItemType;
      if (!isObjectNull(sku)) {
        if (sku.skuNo && !quantityError && sku.quantity === 0) {
          errList.push('商品信息商品数量不能为空！');
          quantityError = true;
        }
        if (
          !sku.skuNo &&
          !quantityError &&
          (!sku.quantity || Number.parseFloat(sku.quantity) === 0) &&
          (!sku.customerMaterialQuantity ||
            Number.parseFloat(sku.customerMaterialQuantity) === 0)
        ) {
          errList.push('商品信息商品数量和客户物料数量不能同时为空！');
          quantityError = true;
        }
        if (!positionError && !sku.position && sku.skuNo) {
          errList.push('商品信息库位信息不能为空！');
          positionError = true;
        }
        if (!factoryError && !sku.factory && sku.skuNo) {
          errList.push('商品信息工厂信息不能为空！');
          factoryError = true;
        }
        if (
          !customerMaterialNameError &&
          !sku.customerMaterialName &&
          !sku.skuNo
        ) {
          errList.push('商品信息客户物料名称信息不能为空！');
          customerMaterialNameError = true;
        }
        const costCenterDesc = costCenterList.value.find(
          (item: any) => item.costCenter === sku.costCenter
        )?.description;
        if (
          categoryCode.value === 'Z007' &&
          !costCenterError &&
          (!sku.costCenter || !costCenterDesc) &&
          isInnerOrderReason(orderReason.value)
        ) {
          errList.push('成本中心/描述不能为空！');
          costCenterError = true;
        }
        if (
          categoryCode.value === 'Z007' &&
          !generalLedgerError &&
          !sku.generalLedgerAccount &&
          isInnerOrderReason(orderReason.value)
        ) {
          errList.push('总账科目不能为空！');
          generalLedgerError = true;
        }
        if (
          !isServiceOrder(categoryCode.value) &&
          !isForecastOrder(categoryCode.value) &&
          !isFreeOrder(categoryCode.value)
        ) {
          if (!taxedPriceError && !sku.taxPrice && isTaxedCustomer.value) {
            errList.push('商品信息含税价格不能为空！');
            taxedPriceError = true;
          }
          if (
            !freeTaxPriceError &&
            !sku.freeTaxPrice &&
            !isTaxedCustomer.value
          ) {
            errList.push('商品信息未税价格不能为空！');
            freeTaxPriceError = true;
          }
        }
        if (
          isForecastOrder(categoryCode.value) &&
          !customerDateError &&
          !sku.customerDate
        ) {
          errList.push('客户期望送达日期不能为空！');
          customerDateError = true;
        }
      }
    });
    const skuError =
      !quantityError &&
      !positionError &&
      !factoryError &&
      !customerDateError &&
      !taxedPriceError &&
      !freeTaxPriceError &&
      !costCenterError &&
      !generalLedgerError &&
      !customerMaterialNameError;
    if (!skuError) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: errList.join('<br>'),
      });
      throw { type: 'check', msg: errList.join(' ') };
    }
  }
};

const orderReasonCheck = () => {
  if (
    !orderReason.value &&
    categoryCode.value !== 'Z001' &&
    categoryCode.value !== 'Z002'
  ) {
    // return new Promise((resolve) => {
    return ElMessageBox.confirm('还未选择订单原因，是否继续提交?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => Promise.resolve())
      .catch(() => {
        throw { type: 'check', msg: 'orderReasonCheck' };
      });
    // });
  }
  return Promise.resolve();
};

const prechecks = reactive({
  customerDate: false,
});
// const precheckCustomerDate = (callback: any) => {
//   // 允许分批 当客户期望送达日期有值，且小于系统标准发货日期
//   let contentMsg = '<div style="max-height: 300px;overflow: auto">';
//   let hasError = false;
//   selectedItemList.value.forEach((selectItem: ItemType, index: number) => {
//     const sku = (itemList.value.find(
//       (item: ItemType) =>
//         item.idx === selectItem.idx && item.itemEditable === '1'
//     ) || {}) as ItemType;
//     if (
//       sku &&
//       sku.refuseSystemDeliveryDate !== 'X' &&
//       sku.refuseSystemDeliveryDate !== true &&
//       sku.customerDate &&
//       sku.originSkuArrivalDate &&
//       new Date(sku.customerDate) < new Date(sku.originSkuArrivalDate)
//     ) {
//       hasError = true;
//       contentMsg += `第${index + 1}行SKU:${sku.skuNo}【客户期望送达日期】${
//         sku.customerDate
//       }小于【原始标准送达日期】${
//         sku.originSkuArrivalDate
//       }，请与客户确认是否接收标期<br />`;
//     }
//   });
//   contentMsg += '</div>';
//   if (prechecks.customerDate || !hasError) {
//     return callback(true);
//   }
//   return ElMessageBox.confirm(contentMsg, '操作提示', {
//     dangerouslyUseHTMLString: true,
//     confirmButtonText: '已确认接受标期，直接创建订单',
//     cancelButtonText: '取消',
//     type: 'warning',
//   })
//     .then(() => {
//       prechecks.customerDate = true;
//       callback(true);
//     })
//     .catch(() => {
//       callback(false);
//     });
// };
// const validateDeliveryDateRules = async () => {
//   await precheckCustomerDate((valid: boolean) => {
//     if (!valid)
//       throw {
//         type: 'check',
//         msg: 'precheckCustomerDate',
//       };
//   });
// };
const preSubmitValidate = async () => {
  if (Object.keys(orderFieldsSettings.value).length > 0) {
    // 库存出清订单保存时需要根据每行所选工厂校验销售考核成本单价（未税）
    if (orderData.value.orderBasis === 'STOCK_CLEARANCE') {
      const item = itemList.value.find((item) => {
        const priceConfig =
          thisRefItem.value?.skuTableRef?.currentSalesRangeOrderBasisConfig?.find(
            (config: any) => config.factoryConfig === item.factory
          )?.freeTaxSaleAssessmentUnitPriceConfig;
        return (
          (priceConfig === 'geZero' &&
            item.freeTaxSaleAssessmentUnitPrice < 0) ||
          (priceConfig === 'gtZero' && item.freeTaxSaleAssessmentUnitPrice <= 0)
        );
      });
      if (item) {
        ElMessage.error('请检查销售考核成本单价（未税）！');
        throw { type: 'check', msg: 'skuTableCheck' };
      }
    }
    const errMsg =
      await thisRefItem.value?.skuTableRef?.tableRef?.validate(true);
    if (errMsg) {
      ElMessage.error('请检查商品信息必填项！');
      throw { type: 'check', msg: 'skuTableCheck' };
    }
  } else {
    invoiceCheck();
    skuListCheck();
    await orderReasonCheck();
  }
};
const submitOrderData = async (
  isDraft: boolean,
  createWorkList?: boolean,
  afterCreateValidateError?: boolean
) => {
  // isDraft: 保存草稿 or 直接创建订单
  const dataSource = {
    isDraft,
    cusDetail: cusDetail.value,
    customer: customer.value,
    contactData: contactData.value,
    customerDateSensitive: customerDateSensitive.value,
    orderSource: orderSource.value || 'BOSS',
    subOrderSource: orderData.value.subOrderSource || 'BOSS',
    selectedSalesRange: selectedSalesRange.value,
    receiverContact: contactData.value.receiverContact,
    orderContact: contactData.value.orderContact,
    receivingInvoiceContact: invoiceData.value.receivingInvoiceContact,
    // receivingInvoicePhone: invoiceData.value.receivingInvoiceContact,
    skuList: itemList.value,
    costCenterList: costCenterList.value,
    autoBatching: deliveryData.value.autoBatching,
    customerReferenceDate: deliveryData.value.customerReferenceDate,
    entireOrderRefuseSDD: deliveryData.value.entireOrderRefuseSDD,
    invoice: invoiceData.value,
    delivery: deliveryData.value,
    customerReferenceNo: customerReferenceNo.value,
    orderType: categoryCode.value,
    bossID: bossId.value,
    orderDataInfo: orderData.value,
  };
  const submitData = await formatSubmitData(dataSource);
  console.log(submitData);
  if (!submitData) return;
  if (isDraft) {
    submitData.sketchOrderScene = 'manualCreateSketch';
    const createDraftParams = {
      createWorkList,
      afterCreateValidateError,
    };
    return createDraft(submitData, createDraftParams);
  }
  const list: string[] =
    submitData.acceptSupplierDelivery === 1
      ? []
      : itemList.value
          .filter((a: ItemType) => a.directDeliverySupplier === '1')
          .map((a: ItemType) => `行号${a.idx}`);
  if (list.length > 0) {
    ElMessageBox.confirm(
      `存在直发冲突冻结行：${list.join('，')}，是否修改客户接受供应商直发=是`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        submitData.acceptSupplierDelivery = 1;
        customerStore.editStore({
          cusDetail: {
            ...cusDetail,
            acceptSupplierDelivery: 1,
          },
        });
        createOrder(submitData);
      })
      .catch(() => {
        // createOrder(orderUrl, orderData);
      });
  } else {
    createOrder(submitData);
  }
};

const createOrder = (submitData: ItemType) => {
  if (isDraftDetail.value) {
    createOrderByDraft(submitData);
  } else {
    createOrderByFormal(submitData);
  }
};

const createDraft = (
  orderData: any,
  createDraftParams?: {
    createWorkList?: boolean;
    afterCreateValidateError?: boolean;
  }
) => {
  orderStore.editStore({
    pageLoading: true,
  });
  console.log(orderData, createDraftParams);
  saveDraft(orderData, createDraftParams)
    .then((res: any) =>
      handleDraftResponse(res, isDraftDetail.value, orderData)
    )
    .catch((error: any) => {
      ElMessageBox.alert(error.msg || error.message || '操作失败！', '错误', {
        confirmButtonText: '确认',
      });
    })
    .finally(() => {
      orderStore.editStore({
        pageLoading: false,
      });
    });
};

const handleCreateResponseByDraft = (res: any, createQueryData: ItemType) => {
  if (res.code === 200) {
    const { successList, failList, highestPriorityFailReasonMap } = res.data;
    let message = '';
    if (successList && Array.isArray(successList) && successList.length > 0) {
      message += `${successList.map((item) => item.msgList).join('<br>')}<br>`;
    }
    if (failList && Array.isArray(failList) && failList.length > 0) {
      message += `${failList.map((item) => item.msgList).join('<br>')}<br>`;
    }
    if (
      highestPriorityFailReasonMap &&
      highestPriorityFailReasonMap.negotiatedFailed &&
      highestPriorityFailReasonMap.negotiatedFailed.length > 0
    ) {
      message += `${highestPriorityFailReasonMap.negotiatedFailed}组需要提交价格审批`;
      ElMessageBox.confirm(message, '操作提示', {
        type: 'warning',
        confirmButtonText: '提交价格审批',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          const params = {
            createWorkListIndex:
              highestPriorityFailReasonMap.negotiatedFailed?.join(','),
          };
          createSalePrice(createQueryData, params);
        })
        .catch((error) => console.log(error));
      return;
    }
    ElMessageBox.alert(message, '操作提示', {
      type: failList && failList.length > 0 ? 'error' : 'success',
      confirmButtonText: '确定',
      customStyle: { 'white-space': 'break-spaces' },
      dangerouslyUseHTMLString: true,
      callback: () => {
        window.location.reload();
      },
    });
  } else {
    let message = '';
    if (res.data && res.data.msgList) {
      if (Array.isArray(res.data.msgList)) {
        const arr = res.data.msgList.map((item: any) => {
          if (typeof item === 'string') {
            return item.split(';').filter((s) => s);
          }
          return item;
        });
        message = flatten(arr).join(';<br>');
      } else {
        message = res.data as string;
      }
    } else if (res.msg) {
      message = res.msg;
    }
    ElMessageBox.alert(message || '创建失败', '错误', {
      callback: () => {
        window.location.reload();
      },
    });
  }
};

const createSalePrice = (
  data: Record<string, string[]>,
  params: Record<string, string>
) => {
  orderStore.editStore({
    pageLoading: true,
  });
  createWorkOrderApi(data, params)
    .then((res: any) => {
      if (res.code === 200) {
        ElMessage.success(res.msg || '提交成功！');
        window.location.reload();
      } else {
        ElMessage.error(res.msg || '操作失败！');
      }
    })
    .catch((error: any) => console.log(error))
    .finally(() => {
      orderStore.editStore({
        pageLoading: false,
      });
    });
};

const createOrderByDraft = async (orderData: ItemType, params?: any) => {
  orderStore.editStore({
    pageLoading: true,
  });
  orderData.sketchOrderScene = 'manualCreateSketch';
  const draftRes = await saveDraft(orderData, params);
  if (draftRes.code === 200) {
    const items = draftRes.data.sketchOrderRespHeaderVO.items;
    if (items) {
      const createQueryData = cloneDeep(orderData);
      const newItems = createQueryData.items?.map(
        (item: ItemType, index: string | number) => {
          return {
            ...items[index],
            ...item,
          };
        }
      );
      createQueryData.items = newItems;
      createQueryData.version = draftRes.data.sketchOrderRespHeaderVO.version;
      createQueryData.sketchOrderScene = 'transformSoFailFromSketch';
      if (orderData.orderBasis === 'STOCK_CLEARANCE') {
        params = {
          ...params,
          needTryCreateOrderAgain: false,
        };
      }
      createOrderByDraftApi(createQueryData, params)
        .then((res: ApiResponseType) =>
          handleCreateResponseByDraft(res, createQueryData)
        )
        .catch((error: any) => {
          ElMessageBox.alert(
            error.msg || error.message || '操作失败！',
            '错误',
            {
              confirmButtonText: '确认',
            }
          );
        })
        .finally(() => {
          orderStore.editStore({
            pageLoading: false,
          });
        });
    } else {
      ElMessageBox.alert('创建失败');
    }
  } else {
    ElMessageBox.alert(draftRes.msg || '创建失败', '提示', {
      type: 'error',
      callback: () => {
        window.location.reload();
      },
    });
    orderStore.editStore({
      pageLoading: false,
    });
  }
};

const handleCreateResponseByFormal = (
  res: ApiResponseType,
  orderData: ItemType
) => {
  const {
    errMsgList,
    remindList,
    headerResultList,
    itemResultList,
    sketchOrderNo,
  } = res.data || {};
  const saveDraftData = cloneDeep(orderData);
  saveDraftData.validatorResultAllDTO = {
    headerResultList,
    itemResultList,
  };
  // 触发协议价审批的条件
  const negotiatedDiscountValidator =
    headerResultList?.length === 0 &&
    itemResultList?.every(
      (item: ItemResult) =>
        item?.itemValidatorResultList?.length === 1 &&
        [
          'negotiatedDiscountValidator',
          'negotiatedDiscountValidatorV2',
        ].includes(item.itemValidatorResultList[0].validateCode)
    );
  if (res.code === 200) {
    const str = `订单创建成功！对应的${sketchOrderNo ? '草稿单号' : '外围订单号'}为：${sketchOrderNo ? sketchOrderNo : orderData.orderNo}`;
    const msg =
      remindList && remindList.length > 0
        ? `${str};` + `${remindList.join(' ')}`
        : str;
    ElMessageBox.alert(msg, '订单创建成功', {
      confirmButtonText: '确定',
      callback: () => {
        if (sketchOrderNo) {
          const url =
            orderData.orderBasis === 'STOCK_CLEARANCE'
              ? `/sr/stockClearance/list?voucherNoList=${sketchOrderNo}`
              : `/sr/draft/list?voucherNoList=${sketchOrderNo}`;
          window.location.replace(url);
        } else {
          window.location.replace(
            `/sr/draft/list?voucherNoList=${orderData.orderNo}&orderCreateStatus=`
          );
        }
      },
    });
  } else if (res.code === 500 && negotiatedDiscountValidator) {
    ElMessageBox.confirm(errMsgList?.join('<br/>'), '操作提示', {
      type: 'warning',
      dangerouslyUseHTMLString: true,
      confirmButtonText: '一键提交价格审批并保存草稿',
      cancelButtonText: '确认',
    })
      .then(() => {
        saveDraftData.sketchOrderScene = 'manualCreateOrderFailToSketch';
        const createDraftParams = {
          createWorkList: true,
          afterCreateValidateError: false,
        };
        createDraft(saveDraftData, createDraftParams);
      })
      .catch(() => {
        saveDraftData.actionSource = '1';
        transformSoFailDataPoint(saveDraftData);
      });
  } else if (errMsgList && Array.isArray(errMsgList)) {
    const message = errMsgList.join('<br/>');
    ElMessageBox.confirm(
      `<div style="max-height:400px;overflow:auto;white-space: break-spaces;">${message}</div>`,
      '错误提示',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '尝试创建订单并保存草稿',
        cancelButtonText: '取消',
        type: 'error',
      }
    )
      .then(() => {
        saveDraftData.sketchOrderScene = 'manualCreateOrderFailToSketch';
        const createDraftParams = {
          createWorkList: false,
          afterCreateValidateError:
            orderData.orderBasis === 'STOCK_CLEARANCE' ? false : true,
        };
        createDraft(saveDraftData, createDraftParams);
      })
      .catch(() => {
        saveDraftData.actionSource = '1';
        transformSoFailDataPoint(saveDraftData);
      });
  } else {
    ElMessageBox.alert(res.msg, '错误', {
      type: 'error',
      customClass: 'custom-return-error',
      dangerouslyUseHTMLString: true,
    });
  }
};

const createOrderByFormal = (orderData: ItemType) => {
  const orderUrl = getOrderOptUrl(dictList.value, categoryCode.value, 'create');
  if (orderUrl) {
    orderStore.editStore({
      pageLoading: true,
    });
    createOrderByFormalApi(orderUrl, orderData)
      .then((res) => {
        handleCreateResponseByFormal(res, orderData);
      })
      .catch((error: any) => {
        ElMessageBox.alert(error.msg || error.message || '操作失败！', '错误', {
          confirmButtonText: '确认',
        });
      })
      .finally(() => {
        orderStore.editStore({
          pageLoading: false,
        });
      });
  }
};

const validateDraft = () => {
  let res = true;
  if (!isEmpty(customer.value)) {
    ElMessage.error('请选择客户！');
    res = false;
  }
  if (!isEmpty(selectedSalesRange.value)) {
    ElMessage.error('请选择销售范围！');
    res = false;
  }
  return res;
};
const submit = (isDraft: boolean) => {
  // 库存出清订单校验商品不超过500行
  if (
    orderStore.orderData.orderBasis === 'STOCK_CLEARANCE' &&
    itemList.value.length > 500
  ) {
    return ElMessage.error('商品行不能超过500行！');
  }
  if (isDraft) {
    // isDraft 为 true, action 为保存草稿
    if (!isDraftDetail && !validateDraft()) return;
    submitOrderData(isDraft);
    return;
  }
  thisReforderForm.value.validate(async (valid: boolean) => {
    try {
      if (!valid) return ElMessage.error('请填写必填项！');
      const discount = thisRefItem.value.assignRef?.discount;
      if (categoryCode.value === 'Z001' && discount) {
        ElMessage.error('整单折扣金额待分配！');
        return false;
      }
      await preSubmitValidate();
      submitOrderData(isDraft);
      prechecks.customerDate = false;
    } catch (error) {
      console.log(error);
    }
  });
};

const cancelOrder = async () => {
  const res = await request({
    url: `/api-opc/v2/sketch/updateToFinishState`,
    method: 'post',
    params: {
      sketchOrderNo: route.params.orderNo,
    },
  });
  if (res.code === 200) {
    ElMessage.success('取消成功!');
    window.location.reload();
  } else {
    ElMessage.error(res.data || '取消失败!');
  }
};

const initFormalOrderCreate = () => {
  const { companyCode, categoryCode } = route.params;
  const { orderBasis, orderSource } = route.query || {};
  orderStore.editStore({
    companyCode,
    categoryCode,
    bossId: id(),
    orderData: {
      ...orderStore.orderData,
      orderType: categoryCode,
    },
  });
  // 订单来源为SAP814时，创建的时候不传orderBasis，因为后续会使用isSAP814来判断一些禁用逻辑
  if (orderSource !== 'SAP814') {
    orderStore.editStore({
      orderData: {
        ...orderStore.orderData,
        orderBasis,
      },
    });
  }
  const currency =
    companyCode === '2400' || companyCode === '3100' ? 'USD' : 'CNY';
  let currencySymbol = '';
  if (dictList.value && dictList.value.currencySymbol) {
    const findOne: Partial<SalesDictType> =
      dictList.value.currencySymbol.find(
        (item) => item.parentCode === currency
      ) || {};
    if (findOne) {
      currencySymbol = findOne.code || '';
    }
  }
  customerStore.editStore({
    customer: {
      ...customer.value,
      currency,
      currencySymbol,
    },
  });
  try {
    setTimeout(() => {
      thisReforderForm.value.clearValidate();
    }, 200);
  } catch (error) {
    console.log(error);
  }
};

const getOrderStage = async (configType: string) => {
  const res = await request({
    url: '/api-opc/v1/so/template/query/order/stage',
    method: 'post',
    data: {
      configType,
      orderBasis: orderStore.orderData.orderBasis,
      orderNo: route.params.orderNo || '',
    },
  });
  if (res.code === 200 && res.data) {
    return {
      configType: res.data.configType,
      keyJson: { ...res.data },
    };
  }
  return null;
};

// 获取订单配置
const initTemplate = async (configType: string) => {
  if (orderStore.orderData.orderBasis) {
    const configData = (await getOrderStage(
      configType
    )) as OrderTemplateKeyJson;
    await commonStore.getOrderFieldsSettings(configData);
    await commonStore.getOrderBasisFactory();
  }
};

onMounted(async () => {
  const pList = [];
  if (JSON.stringify(dictList.value) === '{}') {
    pList.push(commonStore.getDictList());
  }
  if (JSON.stringify(orderServiceDict.value) === '{}') {
    pList.push(commonStore.getOrderServiceDict());
  }
  if (JSON.stringify(mmDictList.value) === '{}') {
    pList.push(commonStore.getMMDictList());
  }
  if (holidaysList.value.length === 0) {
    pList.push(commonStore.getHolidays());
  }
  commonStore.getSearchSkuSwitch();
  commonStore.getGrayWhiteSwitch();
  setTimeout(() => {
    commonStore.getSapReturnOrderValidator();
  }, 500);
  orderStore.editStore({
    pageLoading: true,
  });
  try {
    await Promise.all(pList).then(async () => {
      if (isDraftDetail.value) {
        await initDraftDetail();
        await initTemplate('OrderDraft');
      } else {
        initFormalOrderCreate();
        await initTemplate('OrderCreate');
        // initByQuery();
      }
    });
  } catch (error: any) {
    ElMessageBox.alert((error && error.message) || '操作失败！', '错误', {
      confirmButtonText: '确定',
      type: 'error',
    });
  } finally {
    orderStore.editStore({
      pageLoading: false,
    });
  }
});

const watermarkConfig = reactive({
  content: ['上游推送草稿（不能修改）', '如转单异常，线下解决后去上游重推订单'],
  font: {
    fontSize: 26,
    color: 'rgba(0, 0, 0, 0.15)',
  },
});

const showWatermark = computed(
  () => orderData.value.sketchOnlyRead === '1' && !orderData.value.orderBasis
);

const showField = (prop: string) => {
  return initFieldsShow(prop)?.visible;
};
const getButtonLabel = (prop: string) => {
  return initFieldsShow('buttons', prop)?.label;
};
const disabledButton = (prop: string) => {
  return initFieldsShow('buttons', prop)?.disabled;
};

const showButton = (prop?: string, buttonName?: string) => {
  return (
    initFieldsShow('buttons', prop)?.visible &&
    getButtonAuth(
      '销售跟单',
      `${isDraftDetail.value ? '草稿' : '创建'}_${buttonName}`
    )
  );
};
</script>

<template>
  <el-scrollbar height="100%">
    <div v-if="isDraftDetail && !isFakeSketch" class="watermark">草稿订单</div>
    <el-watermark
      :content="showWatermark ? watermarkConfig.content : ''"
      :font="watermarkConfig.font"
    >
      <div v-loading="pageLoading" class="px-10px mt-1">
        <Header />
        <el-form
          ref="thisReforderForm"
          class="pb-50px"
          label-width="130px"
          :rules="compRules"
          :model="formModel"
          @submit.prevent
        >
          <template v-if="showField('customerInfo')">
            <DividerHeader>
              <div class="flex justify-between">
                <span>客户信息</span>
                <RowMore
                  @fold="(status) => handleToggle('customerInfo', status)"
                />
              </div>
            </DividerHeader>
            <div v-show="!toggleMap.customerInfo">
              <CustomerInfo
                :format-boolean="formatBoolean"
                :query-contact="queryContact"
                :init-contact-store="initContactStore"
                @update-customer-edit="updateCustomerEdit"
              />
            </div>
          </template>
          <template v-if="showField('deliveryInfo')">
            <DividerHeader>
              <div class="flex justify-between">
                <span>交期要素</span>
                <RowMore
                  @fold="(status) => handleToggle('deliveryInfo', status)"
                />
              </div>
            </DividerHeader>
            <div v-show="!toggleMap.deliveryInfo">
              <DeliveryInfo />
            </div>
          </template>
          <template v-if="showField('deliverySite')">
            <DividerHeader>
              <div class="flex justify-between">
                <span>交付要素</span>
                <RowMore
                  @fold="(status) => handleToggle('deliverySite', status)"
                />
              </div>
            </DividerHeader>
            <div v-show="!toggleMap.deliverySite">
              <DeliverySite />
            </div>
          </template>
          <DividerHeader>
            <span>商品信息</span>
            <el-popover placement="right" title="" width="200" trigger="hover">
              <div
                v-for="(item, index) in orderData.attributeTagConfig"
                :key="index"
              >
                <div class="pb-1">
                  <div class="tag-info">
                    <span>{{ index }}</span>
                  </div>
                  <span>{{ item }}</span>
                </div>
              </div>
              <template #reference>
                <el-tag
                  v-if="orderData.attributeTagConfig"
                  type="danger"
                  style="margin-left: 15px"
                >
                  * 查看打标说明
                </el-tag>
              </template>
            </el-popover>
          </DividerHeader>
          <Item ref="thisRefItem" />
          <OtherInfo v-if="showField('customerInfo')" />
        </el-form>
        <EditCustomer
          :key="detailKey"
          :show-customer-edit="showCustomerEdit"
          width="800px"
          @update-customer-edit="updateCustomerEdit"
        />

        <EditOrderMore v-model:show-dialog="showEditMore" />
      </div>
    </el-watermark>
    <div class="draft-bottom">
      <el-button
        v-if="!isFakeSketch && showButton('editMore', '编辑更多')"
        type="primary"
        plain
        :disabled="disabledButton('editMore')"
        @click="viewEditMore"
      >
        {{ getButtonLabel('editMore') || '编辑更多' }}
      </el-button>
      <el-popover
        v-if="
          orderData.hasUnderApproval && showButton('submitOrder', '确认创建')
        "
        placement="top"
        title=""
        width="400"
        trigger="hover"
      >
        <span>存在正在审批中的审批单，不支持创建订单！</span>
        <template #reference>
          <el-button type="default" plain>确认创建</el-button>
        </template>
      </el-popover>
      <el-button
        v-else-if="!isFakeSketch && showButton('submitOrder', '确认创建')"
        type="primary"
        :disabled="
          orderData.showCreateOrderButton === '0' ||
          disabledButton('submitOrder')
        "
        @click="submit(false)"
      >
        {{ getButtonLabel('submitOrder') || '确认创建' }}
      </el-button>
      <el-button
        v-if="!isFakeSketch && showButton('saveDraft', '保存草稿')"
        type="primary"
        :disabled="
          (isHeaderDisabled && isAllItemDisabled) || disabledButton('saveDraft')
        "
        @click="submit(true)"
      >
        {{ getButtonLabel('saveDraft') || '保存草稿' }}
      </el-button>
      <!-- 配置化展示的按钮 -->
      <template v-if="Object.keys(orderFieldsSettings).length > 0">
        <el-button
          v-if="showButton('cancelOrder', '取消申请')"
          type="primary"
          :disabled="disabledButton('cancelOrder')"
          @click="cancelOrder"
        >
          {{ getButtonLabel('cancelOrder') || '取消申请' }}
        </el-button>
      </template>
    </div>
  </el-scrollbar>
</template>

<style lang="scss" scoped>
.draft-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #fff;
  padding: 10px;
  z-index: 999;
}
.watermark {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 100px;
  height: 100px;
  color: rgba(0, 0, 0, 0.15);
  font-size: 1.8em;
  transform: rotate(-45deg);
  z-index: 1000;
  pointer-events: none; /* 禁止水印文字响应鼠标事件 */
  border: 3px solid rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  padding: 10px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tag-info {
  display: inline;
  margin: 3px;
  span {
    background-color: #f56c6c;
    border-radius: 11px;
    color: #fff;
    padding: 3px;
  }
}
</style>
