<!--
 * @Author: luozhikai
 * @Date: 2024-07-25 16:06:55
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-09-12 10:07:48
 * @Description: 库存出清申请单列表
-->
<script setup lang="ts">
import { onMounted, reactive, ref, watchEffect } from 'vue';
import '@bpmn/schema-table/dist/style.css';
import {
  PagePartialInfo,
  Search,
  Table,
  type SearchOptions,
  type TableOptions,
} from '@bpmn/schema-table';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import { groupBy } from 'lodash';
import request from '@/utils/request';
import { useDraftListStore } from '@/stores/draft/list';

import CreateOrderDlg from './components/CreateOrderDlgV2.vue';
// import Table from './components/GridTable.vue'
// import { SearchOptions, TableOptions } from '.';

const diffArr = [
  'skuList',
  'voucherNoList',
  'orderSourceList',
  'subOrderSourceList',
] as const;

type DiffType = (typeof diffArr)[number];

type SomeType = {
  customerNo: string;
  orderCreateStatus: string;
  eventCodeList: string[];
  productManagerNameList: string[];
  materialGroupId: string;
  stockClearanceProcessNo: string;
  // orderSourceList: string[];
};

type FormData = {
  gmtCreate: string[];
  totalUntaxedDiscountAmount: number[];
  totalPromotionalDiscountRate: number[];
} & {
  [P in DiffType]: string;
} & SomeType;

type QueryData = SomeType & {
  gmtCreateStart: string;
  gmtCreateEnd: string;
  totalUntaxedDiscountAmountStart?: number;
  totalUntaxedDiscountAmountEnd?: number;
  totalPromotionalDiscountRateStart?: number;
  totalPromotionalDiscountRateEnd?: number;
} & {
  [P in DiffType]: string[];
};

interface RowVo {
  customerName: string;
  customerNo: string;
  sketchOrderNo: string;
  soNo: string;
  orderSource: string;
  orderCreateStatus: string;
  materiel: string;
  materialGroup: string
  event: string;
  gmtCreate: string;
  sketchEventSet: string[];
  comment: string;
  unifyApprovalNo: string;
  totalUntaxedPrice: string;
  totalUntaxedDiscountAmount: number;
  totalPromotionalDiscountRate: number;
  stockClearanceProcessNo: string;
  unifyApprovalStatus: string;
  orderBasis: string;
  subOrderSourceToShow: string;

}

const listStore = useDraftListStore();

const commonFields = [
    {
      label: '客户名称',
      prop: 'customerNo',
      type: 'customer',
      span: 8,
      style: 'width: 100%',
    },
    {
      label: '订单号',
      prop: 'voucherNoList',
      type: 'textarea',
      span: 8,
      placeholder: '草稿单号/外围订单号/oms单号/客户订单号',
    },
    {
      label: 'SKU',
      prop: 'skuList',
      placeholder: '多个检索',
      type: 'textarea',
      span: 8,
    },
    {
      label: '草稿单创建时间',
      prop: 'gmtCreate',
      type: 'dateRange',
      span: 8,
      rules: [{ required: true, message: '请选择时间范围' }],
      disabledDate: (time: any, value: any) => {
        if (value) {
          // 最大可选范围 半年
          return (
            time.getTime() < value.getTime() - 180 * 8.64e7 ||
            time.getTime() > value.getTime() + 180 * 8.64e7
          );
        }
        return false;
      },
    },
    {
      label: '整单转单完成',
      prop: 'orderCreateStatus',
      type: 'select',
      span: 8,
      options: listStore.dict?.orderCreateStatus || [],
      style: 'width: 100%',
    },
    {
      label: '创建人',
      prop: 'productManagerNameList',
      type: 'staff',
      span: 8,
      valueProp: 'name',
      multiple: true
    },
    // {
    //   label: '订单来源',
    //   prop: 'orderSourceList',
    //   type: 'soRelatedSelect',
    //   typeValue: 'orderSourceList',
    //   detailValue: 'subOrderSourceList',
    //   typeOptions: listStore.dict?.orderSource || [],
    //   detailOptions: {},
    //   typePlaceholder: '请选择订单来源',
    //   detailPlaceholder: '请选择子订单来源',
    //   span: 8,
    //   // multiple: true,
    //   // options: listStore.dict?.orderSource || [],
    //   style: 'width: 100%',
    // },
    {
      label: '预计总亏损金额(未税)',
      prop: 'totalUntaxedDiscountAmount',
      type: 'amountRange',
      span: 8,
    },
    {
      label: '促销折扣',
      prop: 'totalPromotionalDiscountRate',
      type: 'amountRange',
      span: 8,
    },
    // {
    //   label: '草稿单事件名称',
    //   prop: 'eventCodeList',
    //   type: 'select',
    //   span: 8,
    //   multiple: true,
    //   options: listStore.dict?.eventName || [],
    //   style: 'width: 100%',
    // },
    {
      label: '物料组',
      prop: 'materialGroupId',
      type: 'materialGroup',
      multiple: true,
      span: 8,
      options: [],
    },
    {
      label: '协同事件流程编号',
      prop: 'stockClearanceProcessNo',
      type: 'input',
      span: 8,
    },
]

const columns = [
    { type: 'seq', width: 50, title: '序号', fixed: 'left' },
    { field: 'sketchOrderNo', title: '草稿订单', width: '240px' },
    {
      field: 'customerName',
      title: '客户名称',
      width: '240px',
      slots: {
        default: 'so_item_customer',
      },
    },
    {
      field: 'soNo',
      title: '正式订单号',
      width: '240px',
      slots: {
        default: 'so_no',
      },
    },
    { field: 'materiel', title: 'SKU', width: '180px' },
    { field: 'materialGroup', title: '物料组', width: '180px', slots: { default: 'material_group' } },
    { field: 'comment', title: '促销原因', width: '180px' },
    { field: 'totalUntaxedPrice', title: '促销总成本（未税）', width: '180px' },
    { field: 'totalUntaxedDiscountAmount', title: '预计总亏损金额（未税）', width: '180px' },
    { field: 'totalPromotionalDiscountRate', title: '促销折扣', width: '180px' },
    // {
    //   field: 'sketchEventSet',
    //   title: '草稿单事件名称',
    //   width: '200px',
    //   formatter({ cellValue }: any) {
    //     return (cellValue || []).join(';\n');
    //   },
    // },
    { field: 'stockClearanceProcessNo', title: '协同事件流程编号', width: '180px', slots: {default: 'process_no'} },
    // {
    //   field: 'unifyApprovalNo',
    //   title: '审批单号',
    //   width: '240px',
    //   slots: {
    //     default: 'unify_approval_no',
    //   },
    // },
    // {
    //   field: 'unifyApprovalStatus',
    //   title: '审批状态',
    //   width: '240px',
    //   slots: {
    //     default: 'unify_approval_status',
    //   },
    // },
    {
      field: 'orderCreateStatus',
      title: '整单转单完成',
      width: '100px',
      formatter({ cellValue }: any) {
        return orderCreateStatusMap[cellValue as 0 | 1] || '';
      },
    },
    { 
      field: 'orderBasis', 
      title: '订单依据', 
      width: '120px',  
      formatter() {
        return '库存出清申请流程';
      },
    },
    { field: 'orderSource', title: '订单来源', width: '120px' },
    { field: 'subOrderSourceToShow', title: '子订单来源', width: '120px' },
    { field: 'customerServiceName', title: '客服', width: '80px' },
    { field: 'gmtCreate', title: '创建时间', width: '160px' },
    {
      title: '操作',
      width: '160px',
      fixed: 'right',
      slots: {
        default: 'table_row_action',
      },
    },
] as any
const route = useRoute();

const orderCreateStatusMap = {
  1: '是',
  0: '否',
};

const userInfo = JSON.parse(localStorage.getItem('currentUserInfo') || '{}');

const searchProps = reactive<SearchOptions>({
  labelWidth: 140,
  rowFoldNum: 2,
  fields: commonFields as any,
  searchKey: 'stock-clearance-search',
  colSpan: 8,
  showSearchFilter: true,
  keyJson: {
    configType: 'stock-clearance-search',
    owner: userInfo.username || 'sys',
    type: 'search',
  },
  keyResolveRule: 'configType-type-owner'
})
const tableProps = reactive<TableOptions>({
  gridOptions: {
    id: 'stock-clearance-list',
    align: 'center',
    height: 'auto',
    customConfig: {
      storage: true,
    },
  },
  columns,
  data: [],
  loading: false,
  current: 1,
  total: 0,
  pageSize: 10,
  tableKey: 'stock-clearance-list',
  keyJson: {
    configType: 'stock-clearance-list',
    owner: userInfo.username || 'sys',
    type: 'table',
  },
  keyResolveRule: 'configType-type-owner'
});
const searchRef = ref(null);
const total = ref(0);
const visibleCreateOrderDlg = ref(false);
const initFormData = {
  customerNo: '',
  orderCreateStatus: '0',
  eventCodeList: [],
  gmtCreate: [
    dayjs().subtract(7, 'd').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ],
  orderSourceList: '',
  skuList: '',
  voucherNoList: '',
  productManagerNameList: [],
  totalUntaxedDiscountAmount: [],
  totalPromotionalDiscountRate: [],
  materialGroupId: '',
  stockClearanceProcessNo: ''
};
const formFields = ref<Partial<FormData>>({
  ...initFormData,
});

watchEffect(() => {
  if (listStore.dict) {
    searchProps.fields.forEach((item) => {
      if (item.prop === 'eventCodeList') {
        item.options = listStore.dict?.eventName;
      } else if (item.prop === 'orderCreateStatus') {
        item.options = listStore.dict?.orderCreateStatus;
      } else if (item.prop === 'orderSourceList') {
        item.typeOptions = listStore.dict?.orderSource;
        item.detailOptions = groupBy(
          listStore.dict?.subOrderSourceMap,
          'parent'
        );
      }
    });
  }
});

const handlePageChange = (params: PagePartialInfo) => {
  const { current, pageSize } = params;
  tableProps.current = current;
  tableProps.pageSize = pageSize;
  const data: FormData = (searchRef.value as any).formFields;
  const result = convert(data);
  query(current, pageSize, result);
};

const convert = (data: Partial<FormData>) => {
  const {
    customerNo,
    orderCreateStatus,
    eventCodeList,
    productManagerNameList,
    materialGroupId,
    stockClearanceProcessNo,
    totalUntaxedDiscountAmount,
    totalPromotionalDiscountRate
  } = data;
  const result: any = {
    customerNo,
    orderCreateStatus,
    eventCodeList,
    productManagerNameList,
    materialGroupId,
    stockClearanceProcessNo,
  };
  if (data.gmtCreate && data.gmtCreate.length === 2) {
    result.gmtCreateStart = `${data.gmtCreate[0]} 00:00:00`;
    result.gmtCreateEnd = `${data.gmtCreate[1]} 23:59:59`;
  }
  if(totalUntaxedDiscountAmount && totalUntaxedDiscountAmount.length) {
    result.totalUntaxedDiscountAmountStart = totalUntaxedDiscountAmount[0]
    result.totalUntaxedDiscountAmountEnd = totalUntaxedDiscountAmount[1]
  }
  if(totalPromotionalDiscountRate && totalPromotionalDiscountRate.length) {
    result.totalPromotionalDiscountRateStart = totalPromotionalDiscountRate[0]
    result.totalPromotionalDiscountRateEnd = totalPromotionalDiscountRate[1]
  }
  diffArr.forEach((str: DiffType) => {
    if (data[str]) {
      result[str] =
        (data[str] || '')
          .trim()
          .split(/[\s,;，；]/g)
          .filter((s) => s && s.trim()) || [];
    }
  });
  return result;
};

const handleSubmit = (data: Partial<FormData>) => {
  const result = convert(data);
  query(1, tableProps.pageSize, result);
};

const query = async (
  current: number,
  size: number,
  data: Partial<QueryData>
) => {
  tableProps.loading = true;
  try {
    const res = await request({
      url: `/api-opc/v2/sketch/list/order`,
      method: 'POST',
      data: {
        current,
        size,
        ...data,
        includeOrderBasisSet: ['STOCK_CLEARANCE']
      },
    });
    if (res?.code !== 200 && res?.msg) {
      ElMessageBox.alert(res?.msg, '提示', {
        confirmButtonText: '取消',
        type: 'warning',
        callback: () => {
          tableProps.data = [];
          tableProps.total = 0;
          total.value = 0;
        },
      });
    } else if (res?.data) {
      tableProps.data = res?.data.records;
      tableProps.total = res?.data.total;
      total.value = res?.data.total;
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    tableProps.loading = false;
  }
};

const handleOpenCreateDlg = () => {
  request({
    url: '/api-opc/v1/so/template/so/authority/permission',
  }).then((res) => {
    if (res && res.code === 200) {
      visibleCreateOrderDlg.value = true;
    } else if (res && res.data) {
      ElMessageBox.alert(res.data, '操作提示', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        type: 'warning',
      });
    }
  });
};

const handleDelete = (row: RowVo) => {
  ElMessageBox.confirm(
    '此操作将删除所选的库存申请单，是否继续?<br/>删除库存申请单后，当前库存申请单的草稿行所生成的待办任务也将同时取消。',
    '操作提示',
    {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await request({
        url: 'api-opc/v2/sketch/doLogicDeleteSketchOrderBatchWithCheck',
        method: 'POST',
        data: [row.sketchOrderNo],
      });
      if (res?.code === 200) {
        ElMessage.success('删除成功！');
        handlePageChange({ current: 1, pageSize: tableProps.pageSize });
      } else {
        ElMessage.error(res?.data?.join() || '删除失败！');
      }
    })
    .catch((error) => console.log(error));
};

const toOrderDetail = (soNo: string) => {
  if (soNo) {
    window.open(`/orderSale/formal/detail/${soNo}?soNo=${soNo}`);
  }
};
const toPriceAudit = (unifyApprovalNo: string) => {
  if (unifyApprovalNo) {
    window.open(`/priceAudit/audit/${unifyApprovalNo}`);
  }
};

// 转换路由带的参数为筛选项条件
const transformRouteQuery = (data: Record<string, string>) => {
  const res: any = {};

  const transformBoolData = (param: string) => {
    if (param === 'true') {
      return true;
    } else if (param === 'false') {
      return false;
    } else {
      return param;
    }
  };

  for (const [key, value] of Object.entries(data)) {
    if (key.endsWith('Start') || key.endsWith('End')) {
      // 把类似arrivalDateStart: '2023-11-01'，arrivalDateEnd: '2023-11-03'这样的参数转成arrivalDate: ['2023-11-01', '2023-11-03']
      const resKey = key.replace(/(Start|End)$/, '');
      if (!res[resKey]) res[resKey] = [];
      if (value) {
        res[resKey][key.endsWith('Start') ? 0 : 1] = value;
      }
    } else {
      const field = searchProps.fields.find((item) => item.prop === key) as any;
      // arrivalDate始终只从arrivalDateStart，arrivalDateEnd转换而来
      if (field?.type !== 'dateRange') {
        res[key] = field?.multiple
          ? value.split(',')
          : transformBoolData(value);
      }
    }
  }
  // 删掉open=true的入参
  delete res.open;
  return res;
};

const formatUnifyApprovalNo = (row: RowVo, type: 'no' | 'status') => {
  if (type === 'no') {
    return row.unifyApprovalNo
      ?.split(',')
      ?.map((item: string) => item.split(':')[0]);
  } else {
    return row.unifyApprovalNo
      ?.split(',')
      ?.map((item: string) => item.split(':')[1]);
  }
};

onMounted(async () => {
  const queryData = route.query;
  
  const formatQueryData = transformRouteQuery(queryData as any);
  tableProps.current = Number(queryData?.current || 1);
  tableProps.pageSize = Number(queryData?.size || 10);
  if (!listStore.dict) {
    await listStore.query();
  }
  formFields.value = {
    ...formFields.value,
    ...formatQueryData,
  };
  // 只有默认条件时，不查询数据
  if (JSON.stringify(formFields.value) !== JSON.stringify(initFormData)) {
    const result = convert(formFields.value);
    query(tableProps.current, tableProps.pageSize, result);
  }
  // 进页面后使用url参数查询后需要将query清除
  history.replaceState(null, '', window.location.href.split('?')[0]);
});
</script>

<template>
  <div class="bg-#EFF4FF px-27px py-15px">
    <div class="search-container px-15px py-20px">
      <Search
        ref="searchRef"
        v-bind="searchProps"
        :initial-search-options="formFields"
        @submit="handleSubmit"
      />
    </div>
    <div class="table-container">
      <Table v-bind="tableProps" @page-change="handlePageChange">
        <template #table_tools_slot_slot>
          <div class="m-r-10px">
            <el-button type="primary" size="small" @click="handleOpenCreateDlg"
              >创建订单
            </el-button>
          </div>
        </template>
        <template #so_item_customer="{ row }">
          <span>{{ `${row.customerNo} ${row.customerName}` }}</span>
        </template>
        <template #so_no="{ row }">
          <div v-for="item in row.soNo?.split(',')" :key="item">
            <span
              v-if="item"
              class="link-to-detail"
              @click="toOrderDetail(item)"
              >{{ item }}
            </span>
          </div>
        </template>
        <template #material_group="{ row }">
          <span>{{ row.materialGroupName }}</span>
        </template>
        <template #process_no="{ row }">
          <span>
            <el-link type="primary" target="_blank" :href="`/wb/so-task/list?processInstanceId=${row.stockClearanceProcessNo}&status=`">
              {{ row.stockClearanceProcessNo }}
            </el-link>
          </span>
        </template>
        <template #unify_approval_no="{ row }">
          <div v-for="item in formatUnifyApprovalNo(row, 'no')" :key="item">
            <span v-if="item" class="link-to-detail" @click="toPriceAudit(item)"
              >{{ item }}
            </span>
          </div>
        </template>
        <template #unify_approval_status="{ row }">
          <div v-for="item in formatUnifyApprovalNo(row, 'status')" :key="item">
            <span>{{ item }}</span>
          </div>
        </template>
        <template #table_row_action="{ row }">
          <div class="flex justify-center items-center">
            <router-link
              class="text-#409eff decoration-none inline-block"
              :to="`/draft/${row.sketchOrderNo}`"
              target="_blank"
              >编辑</router-link
            >
            <el-button
              size="small"
              type="primary"
              text
              @click="handleDelete(row)"
              >删除</el-button
            >
          </div>
        </template>
      </Table>
    </div>
    <CreateOrderDlg v-model:visible="visibleCreateOrderDlg" is-stock-clearance-modal />
  </div>
</template>

<style scoped>
.search-container {
  background-color: #fff;
  margin-bottom: 15px;
  border-radius: 8px;
}
.table-container {
  background-color: #fff;
  padding-left: 15px;
  padding-right: 15px;
  height: calc(100vh - 160px);
  border-radius: 8px;
}

.link-to-detail {
  color: #597bee;
  cursor: pointer;
}
</style>
