<!--
 * @Author: luozhikai
 * @Date: 2024-08-29 15:05:05
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-09-10 16:57:53
 * @Description: 自定义操作列弹窗
-->
<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue';
import { VxeGridPropTypes, VxeTableDefines } from 'vxe-table';
import { VueDraggable } from 'vue-draggable-plus'
import { cloneDeep, isEmpty } from 'lodash'

interface StoreData {
  storeData: VxeTableDefines.CustomStoreData
}
interface Props {
  config: VxeTableDefines.CustomStoreData;
  visible: boolean;
  columns: VxeGridPropTypes.Columns;
  handleOk: (data: StoreData) => any
}

const emit = defineEmits(['update:visible']);

const props = defineProps<Props>()
const hiddenColumns = ref<VxeGridPropTypes.Column[]>([])
const fixLeftColumns = ref<VxeGridPropTypes.Column[]>([])
const fixRightColumns = ref<VxeGridPropTypes.Column[]>([])
const sortedNormalColumns = ref<VxeGridPropTypes.Column[]>([])

const showDlg = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

const handleOk = () => {
  console.log(hiddenColumns.value, fixLeftColumns.value, fixRightColumns.value, sortedNormalColumns.value);
  let visibleData: any = {}
  hiddenColumns.value.every(col => visibleData[col.field!] = false)

  let fixedData: any = {}
  fixLeftColumns.value.every(col => fixedData[col.field!] = 'left')
  fixRightColumns.value.every(col => fixedData[col.field!] = 'right')

  let sortData: any = {};
  [...fixLeftColumns.value, ...sortedNormalColumns.value, ...fixRightColumns.value].forEach((col, index) => {
    sortData[col.field!] = index + 1
  })

  const submitData = {
    fixedData,
    sortData,
    visibleData,
    resizableData: props.config.resizableData
  }

  props.handleOk({
    storeData: submitData
  })
  // const defaultFixedLeftCols = props.columns.filter(col => col.fixed === 'left')
  // defaultFixedLeftCols.forEach((col, index) => sortData[col.field!] === index)
}

function onUpdate() {
  console.log('update')
}
function onAdd() {
  console.log('add')
}
function remove() {
  console.log('remove')
}

onBeforeMount(() => {
  // 根据自定义列配置把所有的columns划分成四种
  const cloneCols = cloneDeep(props.columns)

  // 过滤掉没有field的项，例如type = seq type = checkbox；过滤掉默认fixed的项
  let sortedCols = cloneCols.filter(col => col.field && !col.fixed)
  if(!isEmpty(props.config.sortData)) {
    const sortData = props.config.sortData
    sortedCols = sortedCols.sort((i,j) => sortData[i.field!] - sortData[j.field!])
  }

  const fixedData: any = props.config.fixedData || {}
  fixLeftColumns.value = sortedCols.filter(col => fixedData[col.field!] === 'left')
  fixRightColumns.value = sortedCols.filter(col => fixedData[col.field!] === 'right')

  const visibleData = Object.keys(props.config.visibleData || {})
  hiddenColumns.value = sortedCols.filter(col => visibleData.includes(col.field!))

  const temp = [...Object.keys(fixedData), ...Object.keys(visibleData)]
  sortedNormalColumns.value = sortedCols.filter(col => !temp.includes(col.field!))
})
</script>
<template>
  <el-dialog
    v-model="showDlg"
    title="自定义列"
    width="1265"
  >
    <span class="font-500 text-14px color-red">提示：在不同分组间互相拖动来调整列表展示</span>
    <div class="flex gap-2 m-t-10px">
      <div>
        <span class="font-500 text-16px">不展示的列</span>
        <VueDraggable
          class="flex flex-col gap-2 p-4 w-300px h-500px m-auto bg-gray-500/5 rounded overflow-auto"
          v-model="hiddenColumns"
          :animation="150"
          easing="ease-in-out"
          ghostClass="ghost"
          group="column"
          @update="onUpdate"
          @add="onAdd"
          @remove="remove"
        >
          <div
            v-for="item in hiddenColumns"
            :key="item.field"
            class="cursor-move bg-gray-500/5 rounded p-8px"
          >
            {{ item.title }}
          </div>
        </VueDraggable>
      </div>
      <div>
        <span class="font-500 text-16px">固定在左侧的列</span>
        <VueDraggable
          class="flex flex-col gap-2 p-4 w-300px h-500px m-auto bg-gray-500/5 rounded overflow-auto"
          v-model="fixLeftColumns"
          :animation="150"
          easing="ease-in-out"
          ghostClass="ghost"
          group="column"
          @update="onUpdate"
          @add="onAdd"
          @remove="remove"
        >
          <div
            v-for="item in fixLeftColumns"
            :key="item.field"
            class="cursor-move bg-gray-500/5 rounded p-8px"
          >
            {{ item.title }}
          </div>
        </VueDraggable>
      </div>
      <div>
        <span class="font-500 text-16px">正常展示的列</span>
        <VueDraggable
          class="flex flex-col gap-2 p-4 w-300px h-500px m-auto bg-gray-500/5 rounded overflow-auto"
          v-model="sortedNormalColumns"
          :animation="150"
          easing="ease-in-out"
          ghostClass="ghost"
          group="column"
          @update="onUpdate"
          @add="onAdd"
          @remove="remove"
        >
          <div
            v-for="item in sortedNormalColumns"
            :key="item.field"
            class="cursor-move bg-gray-500/5 rounded p-8px"
          >
            {{ item.title }}
          </div>
        </VueDraggable>
      </div>
      <div>
        <span class="font-500 text-16px">固定在右侧的列</span>
        <VueDraggable
          class="flex flex-col gap-2 p-4 w-300px h-500px m-auto bg-gray-500/5 rounded overflow-auto"
          v-model="fixRightColumns"
          :animation="150"
          easing="ease-in-out"
          ghostClass="ghost"
          group="column"
          @update="onUpdate"
          @add="onAdd"
          @remove="remove"
        >
          <div
            v-for="item in fixRightColumns"
            :key="item.field"
            class="cursor-move bg-gray-500/5 rounded p-8px"
          >
            {{ item.title }}
          </div>
        </VueDraggable>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showDlg = false">取消</el-button>
        <el-button type="primary" @click="handleOk">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
