<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus';
import { storeToRefs } from 'pinia';
import { computed, onMounted, reactive, ref } from 'vue';
import { useCommonStore } from '@/stores/common';
import {
  type CompanyType,
  type OrderType,
  orderTypeToCompany,
  supportedCompanyList,
  supportedOrderTypeList,
} from '@/constants';

const props = defineProps({
  visible: { required: true, type: Boolean },
  isStockClearanceModal: { required: false, type: Boolean },
});

const emit = defineEmits(['update:visible']);

const commonStore = useCommonStore();

const { dictList } = storeToRefs(commonStore);

const showDlg = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

const createOrderForm = ref<FormInstance>();

const formData = reactive({
  companyRange: '1000',
  orderType: 'Z001',
  orderBasis: 'CUSTOMER_ORDER',
});

const rules = reactive<FormRules<typeof formData>>({
  companyRange: { required: true, message: '必填', trigger: 'blur' },
  orderType: { required: true, message: '必填', trigger: 'blur' },
});

const isShowOrderBasis = computed(() =>
  /z001|z005|z012/gim.test(formData.orderType)
);

const companyList = computed(() => {
  const company = props.isStockClearanceModal
    ? ['1000', '1300', '2900']
    : supportedCompanyList;
  return (dictList.value.companyScope || []).filter((item) =>
    company.includes(item.code as CompanyType)
  );
});

const orderTypeList = computed(() => {
  return (dictList.value.soCategory || []).filter((item) => {
    return (
      supportedOrderTypeList.includes(item.code as OrderType) &&
      item.code !== 'Z013'
    );
  });
});

const isSupportedCompany = (item: string) => {
  const orderType = formData.orderType as OrderType;
  if (orderType) {
    const companyList = orderTypeToCompany[orderType];
    if (companyList && companyList.length > 0) {
      return !companyList.includes(item);
    }
  }
  return false;
};
const isSupportedOrderType = (item: OrderType) => {
  const forecastList = ['Z002'];
  if (forecastList.includes(item)) return true;
  const companyCode = formData.companyRange;
  if (companyCode) {
    const companyList = orderTypeToCompany[item];
    if (companyList && companyList.length > 0) {
      return !companyList.includes(companyCode);
    }
  }
  return false;
};
const closeDialog = () => {
  showDlg.value = false;
};

const handleSubmit = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      const companyValue = formData.companyRange;
      const orderTypeValue = formData.orderType;
      const orderBasis = formData.orderBasis;
      if (isShowOrderBasis.value && orderBasis !== 'CUSTOMER_ORDER') {
        window.open(
          `/sr/createSpecialOrder?companyValue=${companyValue}&orderTypeValue=${orderTypeValue}&orderBasis=${orderBasis}`,
          '_blank'
        );
        return;
      }
      if (companyValue && companyValue) {
        if (props.isStockClearanceModal) {
          window.open(
            `/sr/create/${companyValue}/${orderTypeValue}?orderBasis=STOCK_CLEARANCE`,
            '_blank'
          );
          return;
        }
        window.open(`/sr/create/${companyValue}/${orderTypeValue}`, '_blank');
        showDlg.value = false;
      }
    } else {
      return false;
    }
  });
};

const handleClose = (formEl: FormInstance | undefined) => {
  showDlg.value = false;
  if (!formEl) return;
};

onMounted(() => {
  if (JSON.stringify(dictList.value) === '{}') {
    commonStore.getDictList();
  }
  if (props.isStockClearanceModal) {
    formData.orderType = 'Z001';
    formData.companyRange = '1000';
  }
});
</script>

<template>
  <el-dialog
    v-if="showDlg"
    v-model="showDlg"
    title="创建订单"
    :before-close="closeDialog"
    :show-close="false"
    width="600px"
    center
    destroy-on-close
  >
    <el-form
      ref="createOrderForm"
      :model="formData"
      status-icon
      :rules="rules"
      label-width="120px"
      class="demo-ruleForm"
    >
      <el-form-item label="选择公司范围" prop="companyRange">
        <el-select
          v-model="formData.companyRange"
          placeholder="请选择公司范围"
          style="width: 100%"
        >
          <el-option
            v-for="item in companyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="isSupportedCompany(item.code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择订单类型" prop="orderType">
        <el-select
          v-model="formData.orderType"
          :disabled="isStockClearanceModal"
          placeholder="请选择订单类型"
          style="width: 100%"
        >
          <el-option
            v-for="item in orderTypeList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="isSupportedOrderType(item.code as OrderType)"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="isShowOrderBasis"
        label="选择订单依据"
        prop="orderBasis"
      >
        <el-select
          v-model="formData.orderBasis"
          clearable
          filterable
          style="width: 100%"
          placeholder="请选择订单依据"
        >
          <el-option
            v-for="item in dictList['orderBasis']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <div class="flex justify-center mt-10">
        <el-button @click="handleClose(createOrderForm)">取消</el-button>
        <el-button type="primary" @click="handleSubmit(createOrderForm)"
          >确定</el-button
        >
      </div>
    </el-form>
  </el-dialog>
</template>
