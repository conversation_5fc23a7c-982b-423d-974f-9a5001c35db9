<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-07-27 17:02:19
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-09-10 17:03:18
 * @Description: 支持自定义列存储在服务端
-->
<script lang="ts" setup>
import {
computed,
  nextTick,
  onBeforeMount,
  reactive,
  ref,
  watch,
} from 'vue';
import { VxeGridInstance, VxeGridProps, type VxePagerDefines, type VxeTableDefines } from 'vxe-table';
import type {
  PageInfo,
  TableOptions,
} from '../';
import { cloneDeep, isEqual, merge } from 'lodash';
import { getTemplateList, editTemplate, addTemplate, deleteTemplate, getShareTemplate } from '@/api/listConfig';
import { ElMessage } from 'element-plus';
import { isEmpty } from 'lodash';
import TableColumnsConfig from './TableColumnsConfig.vue';
import clip from '@/utils/clipboard'

// templateChange: 视图发生改变时，抛出事件，有些页面需要响应视图变化
const emits = defineEmits(['pageChange', 'templateChange']);

const props = withDefaults(defineProps<TableOptions>(), {
  showFilter: true
})

const gridOptions = ref<VxeGridProps>({
  id: props.tableKey,
  border: true,
  align: null,
  size: 'mini',
  loading: false,
  minHeight: 400,
  columnConfig: {
    resizable: true,
    maxFixedSize: 100,
    useKey: true, // 必须，否则表头拖拽后会乱掉
    minWidth: 50,
  },
  rowConfig: {
    isHover: true,
  },
  editConfig: {
    trigger: 'click',
    mode: 'cell',
  },
  columns: props.columns || [],
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'table_toolbar_slot',
      tools: 'table_tools_slot',
    },
  },
  customConfig: {
    mode: 'modal',
    storage: true,
    async updateStore(data) {
      handleEditTemplate(data)
    },
    async restoreStore(params) {
      if(!props.showFilter) return Promise.resolve(params.storeData);

      console.log('restoreStore', params);
      if(currentTemplate.value)
        return Promise.resolve(currentTemplate.value?.templateInfo || {})
      else {
        const res = await getTemplates()
        if(res.length)
          return Promise.resolve(res[0]?.templateInfo)
        else {
          const templateData = isEmpty(params.storeData) ? {
              fixedData: {},
              resizableData: {},
              sortData: {},
              visibleData: {}
            } : params.storeData
          const res = await addTemplate({
            customizedAlias: '默认配置',
            isDefault: 'Y',
            configType: props.tableKey,
            keyJson: props.keyJson as any,
            keyResolveRule: props.keyResolveRule as any,
            templateInfo: JSON.stringify(templateData)
          })
          if(res.code === 200) {
            // ElMessage.success('新增成功')
            getTemplates()
          } else {
            ElMessage.error(res.data)
          }
        }
      }
      // return Promise.resolve({})
    }
  },
  data: [],
  headerCellClassName: 'header-cell',
});

const pageInfo = reactive<PageInfo>({
  current: 1,
  pageSize: 0,
  total: 0,
});

const gridRef = ref<VxeGridInstance<any>>();

const allTemplates = ref<any[]>([]);
const currentTemplate = ref();
const showAddDialog = ref(false);
const newTemplateName = ref('')
const shareCode = ref('')
const setAsDefault = ref<any>()
const showEditDialog = ref(false)
const customConfig = ref<VxeTableDefines.CustomStoreData>({})

const currenTemplateId = computed({
  get: () => {
    return currentTemplate.value?.id || ''
  },
  set: (val: any) => {
    // loadTableColumn(val)
    console.log('currenTemplateId', val);
    
    currentTemplate.value = allTemplates.value.find(item => item.id ===val)
    if(!val) return
    nextTick(() => {
      // loadColumn中会执行restoreStore方法，详见vxeTable源码
      gridRef.value?.loadColumn(props.columns as any)
      emits('templateChange', currentTemplate.value)
    })
  }
})

watch(
  [() => props.data, () => props.loading, () => props.columns],
  ([data, loading, columns], [_oldData, _oldLoading, oldColumns]) => {
    console.log('gridOptions', gridOptions.value);
    
    gridOptions.value.data = data;
    gridOptions.value.loading = loading;
    if (!isEqual(columns, oldColumns)) {
      gridOptions.value.columns = columns;
    }
  }
);

// keyJson改变时，重新查询
// keyResolveRule可能也需要监听，但是目前的场景看来，一个页面可能keyJson会变，但keyResolveRule一般是不会变化的
watch(() => props.keyJson!, (val) => {
  console.log('props.keyJson', val);
  getTemplates()
},
{deep: true}
)

watch(
  [
    () => props.current,
    () => props.total,
    () => props.pageSize,
  ],
  ([current, total, pageSize]) => {
    pageInfo.current = current;
    pageInfo.pageSize = pageSize;
    pageInfo.total = total;
  }
);

// const gridEvents = {
//   custom(params: any) {
//     console.log('params', params);
//     // console.log(params.$grid.getCustomStoreData());
//     console.log(gridRef.value?.getTableColumn());
    
//   }
// }

const handlePageChange = ({
  currentPage,
  pageSize,
}: VxePagerDefines.PageChangeParams) => {
  emits('pageChange', { current: currentPage, pageSize });
};

/**
 * 
 * @param id 当前页面选中的template，每次打开配置弹窗的时候，重新获取所有模板，若当前已有选中模板，则定位到该模板
 */
 const getTemplates = async (id?: any) => {
  try {
    const userInfo = JSON.parse(localStorage.getItem('currentUserInfo') || '{}');

    const res = await getTemplateList({
      configType: props.tableKey,
      customizedAlias: '',
      keyJson: props.keyJson || {
        configType: props.tableKey,
        owner: userInfo.username || 'sys',
      },
      keyResolveRule: props.keyResolveRule || 'configType-owner',
      pageNum: 1,
      pageSize: 100
    } as any);

    if (res.code === 200 && res.data?.records) {
      allTemplates.value = res.data.records;
      let t
      if(id) {
        t = res.data.records.find((item: any) => item.id === id)
      } else {
        t = res.data.records[0]
      }
      currentTemplate.value = t
      currenTemplateId.value = t?.id || ''
      return res.data.records
      // loadTableColumn(t.id)
    }
  } catch (error) {
    console.log('cache restore error', error);
  }
}

const saveAs = async() => {
  if(!!newTemplateName.value === !!shareCode.value) ElMessage.error('请输入名称或分享码！选择一项输入即可')
  else {
    if(newTemplateName.value) {
      const res = await addTemplate({
        customizedAlias: newTemplateName.value,
        configType: props.tableKey,
        isDefault: setAsDefault.value,
        keyJson: props.keyJson as any,
        keyResolveRule: props.keyResolveRule as any,
        templateInfo: JSON.stringify({
          fixedData: {},
          resizableData: {},
          sortData: {},
          visibleData: {}
        })
      })
      if(res.code === 200) {
        ElMessage.success('新增成功')
        showAddDialog.value = false
        newTemplateName.value = ''
        getTemplates(currenTemplateId.value)
      } else {
        ElMessage.error(res.data)
      }
    } else {
      const res = await getShareTemplate(shareCode.value)
      if(res.code === 200) {
        ElMessage.success('获取分享模板成功')
        showAddDialog.value = false
        shareCode.value = ''
        getTemplates(currenTemplateId.value)
      } else {
        ElMessage.error(res.data)
      }
    }
  }
}

const delTemplate = async (id: any) => {
  const res = await deleteTemplate({
    id
  })
  if(res.code === 200) {
    ElMessage.success('删除成功')
    getTemplates()
  } else {
    ElMessage.error(res.data)
  }
}

const handleEditTemplate = async (data: any) => {
  if(!props.showFilter) return Promise.resolve();
      console.log('updateStore', data);
      // 没有任何配置的时候，保存为默认
      if(!currenTemplateId.value) {
        const res = await addTemplate({
          customizedAlias: '默认配置',
          isDefault: 'Y',
          configType: props.tableKey,
          keyJson: props.keyJson as any,
          keyResolveRule: props.keyResolveRule as any,
          templateInfo: JSON.stringify(data.storeData)
        })
        if(res.code === 200) {
          ElMessage.success('新增成功')
          showEditDialog.value = false
          getTemplates()
          return Promise.resolve()
        } else {
          ElMessage.error(res.data)
        }
      }
      const res = await editTemplate({
        appointmentKeyId: currenTemplateId.value,
        templateInfo: JSON.stringify(data.storeData)
      } as any)
      if(res.code === 200){
        getTemplates(currenTemplateId.value)
        showEditDialog.value = false
        return Promise.resolve()
      } else {
        ElMessage.error('保存失败，请重试')
      }
}

const setDefault = async (id: any) => {
  const res = await editTemplate({
    appointmentKeyId: id,
    isDefault: 'Y'
  } as any)
  if(res.code === 200) {
    ElMessage.success('更新成功')
    getTemplates()
  } else {
    ElMessage.error(res.data)
  }
}

const showEdit = () => {
  showEditDialog.value = true;
  customConfig.value = gridRef.value!.getCustomStoreData()
}

const shareTemplate = (id: string) => {
  clip(id)
}

// 生命周期
onBeforeMount(async() => {
  // 合并gridOptions
  const defaultOptions = cloneDeep(gridOptions.value)
  merge(defaultOptions, props.gridOptions)
  // console.log('defaultOptions', defaultOptions);
  gridOptions.value = defaultOptions
  console.log('gridoptions-mounted', gridOptions.value);

  if(!props.showFilter) return 
  await getTemplates()
});

defineExpose({
  gridRef,
});
</script>

<template>
  <vxe-grid v-bind="gridOptions" ref="gridRef" v-on="props.gridEvents">
    <template v-for="(_, slotName) in $slots" #[slotName]="scope">
      <slot :name="slotName" v-bind="scope" />
    </template>
    <template #table_tools_slot>
      <div v-if="showFilter" style="display: flex;align-items: center">
        <span style="font-weight: bold;">列表视图: </span>
        <el-select size="small" placeholder="请选择列表视图" v-model="currenTemplateId" filterable style="margin-right: 5px;width: 200px;">
          <el-option
            v-for="item in allTemplates"
            :key="item.id"
            :value="item.id"
            :label="item.customizedAlias"
          >
          <div class="customer-select-template">
            <span class="title-wrapper">
              <el-tooltip :content="item.customizedAlias" placement="left">
                <span class="select-option-title">{{ item.customizedAlias }}</span>
              </el-tooltip>
              <el-tag v-if="item.isDefault === 'Y'" size="small">默认</el-tag>
            </span>
            <span class="btn-wrapper">
              <el-button size="small" :disabled="item.isDefault === 'Y'" link type="primary" @click.stop="() => setDefault(item.id)">设为默认</el-button>
              <el-button size="small" link type="primary" @click.stop="showEdit">编辑</el-button>
              <el-button size="small" link type="primary" @click.stop="() => shareTemplate(item.id)">分享</el-button>
              <el-button size="small" :disabled="allTemplates.length <= 1" link type="danger" style="margin-left: 5px;" @click.stop="() => delTemplate(item.id)">删除</el-button>
            </span>
          </div>
          </el-option>
        </el-select>
        <el-button size="small" style="margin-right: 10px;" @click="showAddDialog = true">新增视图</el-button>
        <!-- <el-button style="margin-right: 10px;" size="small" @click="showEdit">编辑视图</el-button> -->
      </div>
      <slot name="table_tools_slot_slot"></slot>
    </template>
    <template #pager>
      <vxe-pager
        v-model:current-page="pageInfo.current"
        v-model:page-size="pageInfo.pageSize"
        :layouts="[
          'Total',
          'Sizes',
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'FullJump',
          'PageCount',
        ]"
        :page-sizes="props.pageSizes || [5, 10, 20, 50, 100]"
        :border="true"
        :total="pageInfo.total"
        class-name="vxe-pager"
        @page-change="handlePageChange"
      />
    </template>
  </vxe-grid>
  <el-dialog v-model="showAddDialog" width="600" title="新增列表视图">
    <span class="color-red text-14px">注: "输入名称"保存则新增空白配置；"输入分享码"保存则新增他人分享的配置</span>
    <div class="flex m-t-10px">
      <label>输入名称: </label>
      <el-input size="small" v-model="newTemplateName" placeholder="请输入名称" style="width: 200px;margin-right: 10px;" />
      <el-checkbox size="small" v-model="setAsDefault" true-label="Y" false-label="N">设为默认</el-checkbox>
    </div>
    <div class="m-y-5px font-500">或</div>
    <div>
      <label>输入分享码: </label>
      <el-input size="small" v-model="shareCode" placeholder="请输入分享码" style="width: 200px;margin-right: 10px;" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="showAddDialog = false">取消</el-button>
        <el-button size="small" type="primary" @click="saveAs">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
  <TableColumnsConfig v-if="showEditDialog" v-model:visible="showEditDialog" :columns="columns" :config="customConfig" :handle-ok="handleEditTemplate"/>
</template>

<style scoped lang="scss">
.customer-select-template{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-wrapper{
    margin-right: 20px;
    display: flex;
    align-items: center;
  }
  .select-option-title{
    margin-right: 5px;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
  }
}
.title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-cell {
  color: #333;
  background-color: #f5f7fa;
}
</style>

<style>
/* 拖拽背景色设置 */
.sortable-chosen {
  color: rgba(80, 80, 80, 0.4);
  background-color: rgba(0, 255, 255, 0.3);
}

.sortable-drag {
  color: rgba(80, 80, 80, 0.4);
  background-color: rgba(0, 255, 255, 0.3);
}
</style>
.
