<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-07-26 13:57:20
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-07-29 19:11:12
 * @Description: file content
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { computed } from 'vue';

const props = defineProps<{
  modelValue: number[]
}>()

const emits = defineEmits(['update:modelValue'])

const data = useVModel(props, 'modelValue', emits)

const min = computed({
  get: () => data.value?.[0],
  set: (val) => data.value = [val, data.value?.[1]]
})

const max = computed({
  get: () => data.value?.[1],
  set: (val) => data.value = [data.value?.[0], val]
})

</script>
<template>
  <div>
    <span class="m-r-5px">></span>
    <el-input-number v-model="min" :min="0" controls-position="right" style="width: 150px;"/>
    <span class="m-x-5px">且</span>
    <span class="m-r-5px">≤</span>
    <el-input-number v-model="max" :min="min+1" controls-position="right" style="width: 150px;"/>
  </div>
</template>