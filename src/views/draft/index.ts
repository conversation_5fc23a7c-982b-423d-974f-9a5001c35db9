import type {
  VxeGridPropTypes,
  VxeTablePropTypes,
  VxeGridProps,
  VxeGridListeners
} from 'vxe-table';
// import Search from './components/Search.vue';
// import Table from './components/TableV2.vue';
// import SearchTable from './components/SearchTable.vue';
// import SelectStaff from './components/SelectStaff.vue';
// import AmountRange from './components/AmountRange.vue';
// import CustomDateRange from './components/CustomDateRange.vue';
// import SelectBrand from './components/SelectBrand.vue';
// import SelectCustomer from './components/SelectCustomer.vue';
// import SelectMaterialGroup from './components/SelectMaterialGroup.vue';
// import SoItemProcessStatus from './components/SoItemProcessStatus.vue';
// import SoRelatedNo from './components/SoRelatedNo.vue';
// import SoRelatedSelect from './components/SoRelatedSelect.vue';
import type { CascaderProps, FormItemRule } from 'element-plus';

// 将业务组件都暴露出去
// export { 
//   Search, Table, SearchTable, SelectStaff, AmountRange, 
//   CustomDateRange, SelectBrand, SelectCustomer, SelectMaterialGroup, 
//   SoItemProcessStatus, SoRelatedNo, SoRelatedSelect
// };

export interface SearchFormOption {
  name: string;
  value: string | boolean | number;
}

/**
 * @description 日期范围选择时的disabled-date属性, 不需要时不传该属性即可;
 * @param time: 参考https://element-plus.org/zh-CN/component/date-picker.html#attributes disabled-date属性的time参数
 * @param value: 用户选择的第一个日期值，用于动态改变可选范围，
 * @example 例：结束日期 - 起始日期最多间隔7天，那么你的disabled-date函数就应该是
 * (time, value) =>
 *  time.getTime() < value.getTime() - 7 * 8.64e7 ||
 *  time.getTime() > value.getTime() + 7 * 8.64e7
 * @example 如果不需要动态改变可选范围，则忽略value参数,例：今天之前的日期不可选
 * (time) => time.getTime() < (new Date()).getTime()
 */
type DisabledDateFunc = (time: Date, value?: Date) => boolean;
export interface SearchFormField {
  label: string;
  labelWidth?: number;
  prop: string;
  type:
    | 'input'
    | 'customer'
    | 'staff'
    | 'brand'
    | 'materialGroup'
    | 'soItemProcessStatus'
    | 'soRelatedNo'
    | 'soRelatedSelect'
    | 'dateRange'
    | 'select'
    | 'cascader'
    | 'textarea'
    | 'selectV2'
    | 'span'
    | 'amountRange'
  span?: number;
  placeholder?: string;
  style?: string;
  multiple?: boolean;
  options?: SearchFormOption[];
  cascaderProps?: CascaderProps;
  valueProp?: 'label' | 'value' | 'name';
  rules?: FormItemRule[];
  disabledDate?: DisabledDateFunc;
  clearOptions?: string[]; // 已清包括的履约状态，定制化组件
  notClearOptions?: string[]; // 未清包括的履约状态
  // soRelatedNo 组件相关属性
  relatedType?: string; // 关联类型（如 关联主单据）
  relatedNo?: string; // 关联具体内容（如 关联单号）
  relatedTypePlaceholder?: string; // 关联类型placeholder
  relatedNoPlaceholder?: string; // 关联内容placeholder
  // soRelatedSelect 组件相关属性
  typeValue?: string; // 第一个搜索框的值
  detailValue?: string; // 第二个搜索框的值
  typeOptions?: any[]; // 第一个下拉框选项
  detailOptions?: Record<string, SearchFormOption[]>; // 第二个下拉框选项
  typePlaceholder?: string; // 第一个搜索框的placeholder
  detailPlaceholder?: string; // 第二个搜索框的placeholder
}

export interface Column {
  field?: string;
  title?: string;
  type?: string;
  width?: number;
  editRender?: Record<string, string>;
  slots?: {
    default?: string;
    edit?: string;
  };
}

export interface PagePartialInfo {
  current: number;
  pageSize: number;
}

export type PageInfo = PagePartialInfo & {
  total: number;
  pageSizes?: number[];
};

export interface TablePartialOptions<T = VxeTablePropTypes.Row> {
  columns: VxeGridPropTypes.Columns<T>;
  data: VxeTablePropTypes.Data<T>;
  loading: boolean;
}

export interface TableCacheProps {
  tableKey: string;
}

export type KeyJson = {
  configType: string;
  owner: string;
} & Record<string, string>

interface SearchOptionsWithFilter {
  rowFoldNum?: number;
  labelWidth?: number;
  fields: SearchFormField[];
  initialSearchOptions?: any;
  cacheVersion?: string;
  searchKey?: string;
  showSearchFilter?: true;
  colSpan?: number;
  keyJson: KeyJson; // 保存视图时的keyJson，详情见 https://zkh-form-center-uat.zkh360.com/doc.html#/default/%E5%89%8D%E7%AB%AF%E5%B1%95%E7%A4%BA%E9%85%8D%E7%BD%AE%E6%A8%A1%E7%89%88/addFormTemplateUsingPOST
  keyResolveRule: string;
}

interface SearchOptionsWithoutFilter {
  rowFoldNum?: number;
  labelWidth?: number;
  fields: SearchFormField[];
  initialSearchOptions?: any;
  cacheVersion?: string;
  searchKey?: string;
  showSearchFilter: false;
  colSpan?: number;
  keyJson?: KeyJson;
  keyResolveRule?: string;
}


export type SearchOptions = SearchOptionsWithFilter | SearchOptionsWithoutFilter

export interface SaveTemplateJson {
  keyJson: KeyJson; 
  keyResolveRule: string // 同上
}

interface ShowFilterProps {
  showFilter?: true;
  keyJson: KeyJson;
  keyResolveRule: string;
}

interface NotShowFilterProps {
  showFilter: false
  keyJson?: KeyJson;
  keyResolveRule?: string;
}

export type TableOptions<T = VxeTablePropTypes.Row> = {
  gridOptions?: Partial<VxeGridProps<T>>
  gridEvents?: VxeGridListeners<any>
} &
  TablePartialOptions &
  PageInfo &
  TableCacheProps & (ShowFilterProps | NotShowFilterProps);