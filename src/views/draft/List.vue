<script setup lang="ts">
import { onMounted, reactive, ref, watchEffect } from 'vue';
import '@bpmn/schema-table/dist/style.css';
import {
  PagePartialInfo,
  Search,
  type SearchOptions,
  Table,
  type TableOptions,
} from '@bpmn/schema-table';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import { groupBy } from 'lodash';
import request from '@/utils/request';
import { useDraftListStore } from '@/stores/draft/list';

import Upload from '@/components/Upload.vue';
import { getButtonAuth } from '@/utils/auth';
import CreateOrderDlg from './components/CreateOrderDlg.vue';
import type { VxeGridPropTypes } from 'vxe-table';

const diffArr = [
  'skuList',
  'voucherNoList',
  'customerServiceNameList',
  'sellerNameList',
  'orderSourceList',
  'subOrderSourceList',
] as const;

type DiffType = (typeof diffArr)[number];

type SomeType = {
  attachStatus: string;
  customerNo: string;
  orderCreateStatus: string;
  sketchItemCreateStatus: string;
  eventCodeList: string[];
  // orderSourceList: string[];
};

type FormData = {
  gmtCreate: string[];
} & {
  [P in DiffType]: string;
} & SomeType;

type QueryData = SomeType & {
  gmtCreateStart: string;
  gmtCreateEnd: string;
} & {
  [P in DiffType]: string[];
};

interface RowVo {
  customerName: string;
  customerNo: string;
  sketchOrderNo: string;
  soNo: string;
  customerOrderNo: string;
  orderNo: string;
  sketchOrderItemNo: string;
  orderSource: string;
  attachStatus: string;
  orderCreateStatus: string;
  materiel: string;
  customerServiceName: string;
  sellerName: string;
  event: string;
  gmtCreate: string;
  gmtModified: string;
  sketchEventSet: string[];
  comment: string;
  unifyApprovalNo: string;
}

const route = useRoute();

const listStore = useDraftListStore();

const orderCreateStatusMap = {
  1: '是',
  0: '否',
};

const commonFields = [
  {
    label: '客户名称',
    prop: 'customerNo',
    type: 'customer',
    span: 6,
    style: 'width: 100%',
  },
  {
    label: '订单号',
    prop: 'voucherNoList',
    type: 'textarea',
    span: 6,
    placeholder: '草稿单号/外围订单号/oms单号/客户订单号',
  },
  {
    label: 'SKU',
    prop: 'skuList',
    placeholder: '多个检索',
    type: 'textarea',
    span: 6,
  },
  {
    label: '草稿单创建时间',
    prop: 'gmtCreate',
    type: 'dateRange',
    span: 6,
    rules: [{ required: true, message: '请选择时间范围' }],
    disabledDate: (time: any, value: any) => {
      if (value) {
        // 最大可选范围 半年
        return (
          time.getTime() < value.getTime() - 180 * 8.64e7 ||
          time.getTime() > value.getTime() + 180 * 8.64e7
        );
      }
      return false;
    },
  },
  {
    label: '附件状态',
    prop: 'attachStatus',
    type: 'select',
    span: 6,
    options: [],
  },
  {
    label: '整单转单完成',
    prop: 'orderCreateStatus',
    type: 'select',
    span: 6,
    options: listStore.dict?.orderCreateStatus || [],
    style: 'width: 100%',
  },
  {
    label: '行转单完成',
    prop: 'sketchItemCreateStatus',
    type: 'select',
    span: 6,
    options: listStore.dict?.sketchItemCreateStatus || [],
    style: 'width: 100%',
  },
  {
    label: '客服',
    prop: 'customerServiceNameList',
    type: 'input',
    span: 6,
  },
  {
    label: '销售',
    prop: 'sellerNameList',
    type: 'input',
    span: 6,
  },
  {
    label: '草稿单事件名称',
    prop: 'eventCodeList',
    type: 'select',
    span: 6,
    multiple: true,
    options: listStore.dict?.eventName || [],
    style: 'width: 100%',
  },
  {
    label: '订单来源',
    prop: 'orderSourceList',
    type: 'soRelatedSelect',
    typeValue: 'orderSourceList',
    detailValue: 'subOrderSourceList',
    typeOptions: listStore.dict?.orderSource || [],
    detailOptions: {},
    typePlaceholder: '请选择订单来源',
    detailPlaceholder: '请选择子订单来源',
    span: 12,
    // multiple: true,
    // options: listStore.dict?.orderSource || [],
    style: 'width: 100%',
  },
];

const leftCol: VxeGridPropTypes.Columns = [
  { type: 'seq', width: 50 },
  { field: 'sketchOrderNo', title: '草稿订单', width: '240px' },
  {
    field: 'customerName',
    title: '客户名称',
    width: '240px',
    slots: {
      default: 'so_item_customer',
    },
  },
  {
    field: 'soNo',
    title: '正式订单号',
    width: '240px',
    slots: {
      default: 'so_no',
    },
  },
  {
    field: 'customerReferenceNo',
    title: '客户订单号',
    width: '240px',
  },
];

const optionOrderCol: VxeGridPropTypes.Columns = [
  { field: 'materiel', title: 'SKU', width: '180px' },
];
const optionItemCol: VxeGridPropTypes.Columns = [
  { field: 'customerOrderNo', title: '客户行号', width: '120px' },
  { field: 'customerMaterialNo', title: '客户物料号', width: '80px' },
  { field: 'customerMaterialName', title: '客户物料名称', width: '120px' },
  {
    field: 'customerSpecificationModel',
    title: '客户规格型号',
    width: '100px',
  },
  { field: 'materiel', title: 'SKU', width: '180px' },
  { field: 'quantity', title: '数量', width: '80px' },
  { field: 'quantityUnitName', title: '单位', width: '80px' },
];

const rightCol: VxeGridPropTypes.Columns = [
  {
    field: 'sketchEventSet',
    title: '草稿单事件名称',
    width: '200px',
    formatter({ cellValue }) {
      return (cellValue || []).join(';\n');
    },
  },
  {
    field: 'unifyApprovalNo',
    title: '审批单号',
    width: '240px',
    slots: {
      default: 'unify_approval_no',
    },
  },
  {
    field: 'unifyApprovalStatus',
    title: '审批状态',
    width: '240px',
    slots: {
      default: 'unify_approval_status',
    },
  },
  {
    field: 'orderCreateStatus',
    title: '整单转单完成',
    width: '100px',
    formatter({ cellValue }) {
      return orderCreateStatusMap[cellValue as 0 | 1] || '';
    },
  },
  { field: 'orderSource', title: '订单来源', width: '120px' },
  { field: 'subOrderSourceToShow', title: '子订单来源', width: '120px' },
  { field: 'customerServiceName', title: '客服', width: '80px' },
  { field: 'sellerName', title: '销售', width: '80px' },
  { field: 'gmtCreate', title: '创建时间', width: '160px' },
  {
    field: 'comment',
    title: '备注',
    width: '120px',
    editRender: {},
    slots: { edit: 'exra_edit' },
  },
  {
    title: '操作',
    width: '160px',
    fixed: 'right',
    slots: {
      default: 'table_row_action',
    },
  },
];

const userInfo = JSON.parse(localStorage.getItem('currentUserInfo') || '{}');

const mode = ref<'order' | 'item'>('order');

const searchProps = reactive<SearchOptions>({
  labelWidth: 140,
  rowFoldNum: 2,
  fields: commonFields as any,
  searchKey: 'draft-list',
  colSpan: 6,
  showSearchFilter: true,
  keyJson: {
    configType: 'draft-list',
    owner: userInfo.username || 'sys',
    type: 'search',
  },
  keyResolveRule: 'configType-type-owner',
});

const tableProps = reactive<TableOptions>({
  gridOptions: {
    id: 'draft-list',
    align: 'center',
    height: 'auto',
  },
  columns: [...leftCol, ...rightCol] as any,
  data: [],
  loading: false,
  current: 1,
  total: 0,
  pageSize: 10,
  tableKey: 'draft-list',
  keyJson: {
    configType: 'draft-list',
    owner: userInfo.username || 'sys',
    type: 'table',
    mode: mode.value,
  },
  keyResolveRule: 'configType-type-owner-mode',
});
const searchRef = ref(null);
const total = ref(0);
const visible = ref(false);
const visibleCreateOrderDlg = ref(false);
const selectedRow = ref<RowVo | null>(null);
const initFormData = {
  attachStatus: '',
  customerNo: '',
  customerServiceNameList: '',
  orderCreateStatus: '0',
  sketchItemCreateStatus: '',
  eventCodeList: [],
  gmtCreate: [
    dayjs().subtract(7, 'd').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ],
  orderSourceList: '',
  sellerNameList: '',
  skuList: '',
  voucherNoList: '',
};
const formFields = ref<Partial<FormData>>({
  ...initFormData,
});

watchEffect(() => {
  if (listStore.dict) {
    searchProps.fields.forEach((item) => {
      if (item.prop === 'eventCodeList') {
        item.options = listStore.dict?.eventName;
      } else if (item.prop === 'attachStatus') {
        item.options = listStore.dict?.attachStatus;
      } else if (item.prop === 'orderCreateStatus') {
        item.options = listStore.dict?.orderCreateStatus;
      } else if (item.prop === 'orderSourceList') {
        item.typeOptions = listStore.dict?.orderSource;
        item.detailOptions = groupBy(
          listStore.dict?.subOrderSourceMap,
          'parent'
        );
      } else if (item.prop === 'sketchItemCreateStatus') {
        item.options = listStore.dict?.sketchItemCreateStatus;
      }
    });
  }
  if (mode.value === 'order') {
    tableProps.columns = [...leftCol, ...optionOrderCol, ...rightCol] as any;
  } else {
    tableProps.columns = [...leftCol, ...optionItemCol, ...rightCol] as any;
    tableProps.columns.splice(
      tableProps.columns.findIndex(
        (column) => column.field === 'orderCreateStatus'
      ),
      0,
      {
        field: 'sketchItemCreateStatus',
        title: '行转单完成',
        width: '100px',
        formatter({ cellValue }) {
          return orderCreateStatusMap[cellValue as 0 | 1] || '';
        },
      }
    );
  }
  console.log('mode', mode.value);

  tableProps.keyJson!.mode = mode.value;
});

const handleBlur = async (row: RowVo) => {
  const { sketchOrderNo, sketchOrderItemNo, comment } = row;
  const dimension = mode.value === 'order' ? 1 : 0;
  tableProps.loading = true;
  try {
    const res = await request({
      url: `/api-opc/v2/sketch/comment/update`,
      method: 'POST',
      data: {
        comment,
        sketchOrderNo,
        dimension,
        ...(mode.value === 'order' ? {} : { sketchOrderItemNo }),
      },
    });
    if (res?.success === true) {
      ElMessage.success('更新备注成功！');
    } else {
      ElMessage.error(res?.msg || '更新备注失败！');
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    tableProps.loading = false;
  }
};

const handlePageChange = (params: PagePartialInfo) => {
  const { current, pageSize } = params;
  tableProps.current = current;
  tableProps.pageSize = pageSize;
  const data: FormData = (searchRef.value as any).formFields;
  const result = convert(data);
  query(current, pageSize, result);
};

const convert = (data: Partial<FormData>) => {
  const {
    attachStatus,
    customerNo,
    orderCreateStatus,
    sketchItemCreateStatus,
    eventCodeList,
  }: Partial<SomeType> = data;
  const result: Partial<QueryData> = {
    customerNo,
    attachStatus,
    orderCreateStatus,
    sketchItemCreateStatus,
    eventCodeList,
  };
  if (data.gmtCreate && data.gmtCreate.length === 2) {
    result.gmtCreateStart = `${data.gmtCreate[0]} 00:00:00`;
    result.gmtCreateEnd = `${data.gmtCreate[1]} 23:59:59`;
  }
  diffArr.forEach((str: DiffType) => {
    if (data[str]) {
      result[str] =
        (data[str] || '')
          .trim()
          .split(/[\s,;，；]/g)
          .filter((s) => s && s.trim()) || [];
    }
  });
  return result;
};

const handleSubmit = (data: Partial<FormData>) => {
  const result = convert(data);
  query(1, tableProps.pageSize, result);
};

const handleReset = () => {};

const handleInputChange = (data: { prop: string; value: string }) => {
  // 订单号改变时，去除【整单转单完成】默认选项
  if (data.prop === 'voucherNoList') {
    formFields.value.orderCreateStatus = '';
  }
};

const query = async (
  current: number,
  size: number,
  data: Partial<QueryData>
) => {
  tableProps.loading = true;
  try {
    const res = await request({
      url: `/api-opc/v2/sketch/list/${mode.value || 'item'}`,
      method: 'POST',
      data: {
        current,
        size,
        ...data,
      },
    });
    if (res?.code !== 200 && res?.msg) {
      ElMessageBox.alert(res?.msg, '提示', {
        confirmButtonText: '取消',
        type: 'warning',
        callback: () => {
          tableProps.data = [];
          tableProps.total = 0;
          total.value = 0;
        },
      });
    } else if (res?.data) {
      tableProps.data = res?.data.records;
      tableProps.total = res?.data.total;
      total.value = res?.data.total;
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  } finally {
    tableProps.loading = false;
  }
};

const handleUpload = (row: RowVo) => {
  if (row.sketchOrderNo) {
    selectedRow.value = row;
  }
  visible.value = true;
};

const handleOpenCreateDlg = () => {
  request({
    url: '/api-opc/v1/so/template/so/authority/permission',
  }).then((res) => {
    if (res && res.code === 200) {
      visibleCreateOrderDlg.value = true;
    } else if (res && res.data) {
      ElMessageBox.alert(res.data, '操作提示', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        type: 'warning',
      });
    }
  });
};

const handleDelete = (row: RowVo) => {
  ElMessageBox.confirm(
    '此操作将删除所选的草稿单，是否继续?<br/>删除草稿单后，当前草稿单的草稿行所生成的待办任务也将同时取消。',
    '操作提示',
    {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await request({
        url: 'api-opc/v2/sketch/doLogicDeleteSketchOrderBatchWithCheck',
        method: 'POST',
        data: [row.sketchOrderNo],
      });
      if (res?.code === 200) {
        ElMessage.success('删除成功！');
        handlePageChange({ current: 1, pageSize: tableProps.pageSize });
      } else {
        ElMessage.error(res?.data?.join() || '删除失败！');
      }
    })
    .catch((error) => console.log(error));
};

const toOrderDetail = (soNo: string) => {
  if (soNo) {
    window.open(`/orderSale/formal/detail/${soNo}?soNo=${soNo}`);
  }
};
const toPriceAudit = (unifyApprovalNo: string) => {
  if (unifyApprovalNo) {
    window.open(`/priceAudit/audit/${unifyApprovalNo}`);
  }
};

// 转换路由带的参数为筛选项条件
const transformRouteQuery = (data: Record<string, string>) => {
  const res: any = {};

  const transformBoolData = (param: string) => {
    if (param === 'true') {
      return true;
    } else if (param === 'false') {
      return false;
    } else {
      return param;
    }
  };

  for (const [key, value] of Object.entries(data)) {
    if (key.endsWith('Start') || key.endsWith('End')) {
      // 把类似arrivalDateStart: '2023-11-01'，arrivalDateEnd: '2023-11-03'这样的参数转成arrivalDate: ['2023-11-01', '2023-11-03']
      const resKey = key.replace(/(Start|End)$/, '');
      if (!res[resKey]) res[resKey] = [];
      if (value) {
        res[resKey][key.endsWith('Start') ? 0 : 1] = value;
      }
    } else {
      const field = searchProps.fields.find((item) => item.prop === key) as any;
      // arrivalDate始终只从arrivalDateStart，arrivalDateEnd转换而来
      if (field?.type !== 'dateRange') {
        res[key] = field?.multiple
          ? value.split(',')
          : transformBoolData(value);
      }
    }
  }
  // 删掉open=true的入参
  delete res.open;
  return res;
};

const formatUnifyApprovalNo = (row: RowVo, type: 'no' | 'status') => {
  if (type === 'no') {
    return row.unifyApprovalNo
      ?.split(',')
      ?.map((item: string) => item.split(':')[0]);
  } else {
    return row.unifyApprovalNo
      ?.split(',')
      ?.map((item: string) => item.split(':')[1]);
  }
};

onMounted(async () => {
  const queryData = route.query;
  const formatQueryData = transformRouteQuery(queryData as any);
  if (queryData.viewType) {
    mode.value = queryData.viewType as any;
  }
  tableProps.current = Number(queryData?.current || 1);
  tableProps.pageSize = Number(queryData?.size || 10);
  if (!listStore.dict) {
    await listStore.query();
  }
  formFields.value = {
    ...formFields.value,
    ...formatQueryData,
  };
  // 只有默认条件时，不查询数据
  if (JSON.stringify(formFields.value) !== JSON.stringify(initFormData)) {
    const result = convert(formFields.value);
    query(tableProps.current, tableProps.pageSize, result);
  }
  // 进页面后使用url参数查询后需要将query清除
  history.replaceState(null, '', window.location.href.split('?')[0]);
});
</script>

<template>
  <div class="bg-#EFF4FF px-27px py-15px">
    <div class="search-container px-15px py-20px">
      <Search
        ref="searchRef"
        v-bind="searchProps"
        :initial-search-options="formFields"
        @submit="handleSubmit"
        @reset="handleReset"
        @input-change="handleInputChange"
      />
    </div>
    <div class="table-container">
      <Table v-bind="tableProps" @page-change="handlePageChange">
        <template #so_item_customer="{ row }">
          <span>{{ `${row.customerNo} ${row.customerName}` }}</span>
        </template>
        <template #table_toolbar_slot>
          <el-switch
            v-model="mode"
            active-text="行维度"
            inactive-text="单维度"
            active-value="item"
            inactive-value="order"
            @change="
              handlePageChange({ current: 1, pageSize: tableProps.pageSize })
            "
          />
        </template>
        <template #so_no="{ row }">
          <div v-for="item in row.soNo?.split(',')" :key="item">
            <span
              v-if="item"
              class="link-to-detail"
              @click="toOrderDetail(item)"
              >{{ item }}
            </span>
          </div>
        </template>
        <template #table_tools_slot_slot>
          <div class="m-r-10px">
            <el-button
              v-if="getButtonAuth('草稿订单列表', '创建订单')"
              type="primary"
              size="small"
              @click="handleOpenCreateDlg"
              >创建订单
            </el-button>
          </div>
        </template>
        <template #unify_approval_no="{ row }">
          <div v-for="item in formatUnifyApprovalNo(row, 'no')" :key="item">
            <span v-if="item" class="link-to-detail" @click="toPriceAudit(item)"
              >{{ item }}
            </span>
          </div>
        </template>
        <template #unify_approval_status="{ row }">
          <div v-for="item in formatUnifyApprovalNo(row, 'status')" :key="item">
            <span>{{ item }}</span>
          </div>
        </template>
        <template #table_row_action="{ row }">
          <div class="flex justify-center items-center">
            <el-button
              v-if="getButtonAuth('草稿订单列表', '上传文档')"
              size="small"
              type="primary"
              text
              :disabled="row.headerEditable === '0'"
              @click="handleUpload(row)"
              >{{
                row.attachStatus === '1' ? '继续上传' : '上传文档'
              }}</el-button
            >
            <router-link
              v-if="getButtonAuth('草稿订单列表', '编辑')"
              class="text-#409eff decoration-none inline-block"
              :to="`/draft/${row.sketchOrderNo}`"
              target="_blank"
              >编辑</router-link
            >
            <el-button
              v-if="mode === 'order' && getButtonAuth('草稿订单列表', '删除')"
              size="small"
              type="primary"
              text
              @click="handleDelete(row)"
              >删除</el-button
            >
          </div>
        </template>
        <template #exra_edit="{ row }">
          <el-input
            v-model="row.comment"
            type="textarea"
            @blur="handleBlur(row)"
          />
        </template>
      </Table>
    </div>
    <Upload
      v-model:upload-dialog-visible="visible"
      :business-id="selectedRow?.sketchOrderNo || ''"
    />
    <CreateOrderDlg v-model:visible="visibleCreateOrderDlg" />
  </div>
</template>

<style scoped>
.search-container {
  background-color: #fff;
  margin-bottom: 15px;
  border-radius: 8px;
}
.table-container {
  background-color: #fff;
  padding-left: 15px;
  padding-right: 15px;
  height: calc(100vh - 160px);
  border-radius: 8px;
}

.link-to-detail {
  color: #597bee;
  cursor: pointer;
}
</style>
