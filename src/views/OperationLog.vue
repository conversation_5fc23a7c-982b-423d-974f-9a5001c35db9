<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import request from '@/utils/request';
import type { FormInstance, TableColumnCtx } from 'element-plus';

interface SoModifyColumnBOS {
  columnName: string;
  afterData: string;
  beforeData: string;
}

interface TableData {
  [key: string]: string | number;
}

type OperationData = TableData & {
  soModifyColumnBOS?: SoModifyColumnBOS[];
};

interface SpanMethodProps {
  row: OperationData;
  column: TableColumnCtx<OperationData>;
  rowIndex: number;
  columnIndex: number;
}

const fixedColumns = [
  {
    prop: 'sapOrderNo',
    label: 'SAP单号',
    width: 120,
  },
  {
    prop: 'omsOrderNo',
    label: 'OMS单号',
    width: 190,
  },
  {
    prop: 'customerName',
    label: '客户名称',
    width: 180,
  },
  {
    prop: 'customerServiceName',
    label: '客服',
    width: 80,
  },
  {
    prop: 'omsItemNo',
    label: 'OMS行号',
    width: 90,
  },
  {
    prop: 'skuNo',
    label: '商品编码',
    width: 105,
  },
  {
    prop: 'skuName',
    label: '商品名称',
    width: 120,
  },
  {
    prop: 'modifyTime',
    label: '变更时间',
    width: 120,
  },
  {
    prop: 'userName',
    label: '变更人',
    width: 90,
  },
  {
    prop: 'operName',
    label: '触发操作',
    width: 120,
  },
];

interface QueryData {
  skuNos?: string;
  orderItemNos?: string;
  keywords?: string;
}
const formData = reactive<QueryData>({
  skuNos: '',
  orderItemNos: '',
  keywords: '',
});

const formRef = ref<FormInstance>();
const rowData = ref<TableData[]>([]);
const total = ref<number>(0);
const current = ref<number>(1);
const loading = ref(false);
const msg = ref('');
const route = useRoute();
const query = async (num: number, data: QueryData = {}) => {
  try {
    const id = route.query.id as string;
    if (id) {
      loading.value = true;
      const res: any = await request({
        url: '/api-boss-opc/so/modify/report',
        method: 'get',
        params: {
          orderType: 0,
          orderNos: id,
          current: num,
          ...data,
        },
      });
      console.log('res', res);
      if (res?.data) {
        const data = res.data as OperationData[];
        rowData.value = [];
        total.value = res.totalCount || 0;
        data.forEach((item: OperationData) => {
          if (
            item &&
            item.soModifyColumnBOS &&
            item.soModifyColumnBOS?.length > 0
          ) {
            const len = (item.soModifyColumnBOS || []).length;
            item.soModifyColumnBOS.forEach((bos, idx) => {
              const newItem: TableData = {
                ...item,
              };
              rowData.value.push({
                ...newItem,
                ...bos,
                span: idx === 0 ? len : 0,
              });
            });
          } else {
            const newItem: TableData = { ...item };
            rowData.value.push({ ...newItem, span: 1 });
          }
        });
      }
      if (res?.msg) {
        msg.value = res?.msg;
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  query(1);
});
const handleSubmit = () => {
  query(1, formData);
};
const handleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  msg.value = '';
  query(1);
};

const span = ({ rowIndex, columnIndex }: SpanMethodProps) => {
  if (columnIndex < fixedColumns.length) {
    const _row = Number(rowData.value[rowIndex]?.span) || 0;
    const _col = _row > 0 ? 1 : 0;
    console.log(rowIndex, _row, _col);
    return { rowspan: _row, colspan: _col };
  }
};

watch(current, (val) => {
  query(val, formData);
});
</script>

<template>
  <el-form ref="formRef" :inline="true" :model="formData">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="商品编码" class="w-full">
          <el-input
            v-model="formData.skuNos"
            placeholder="最多支持30个商品编码，以空格分隔"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="订单行号" class="w-full">
          <el-input
            v-model="formData.orderItemNos"
            placeholder="支持多个，逗号分隔"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="关键字" class="w-full">
          <el-input
            v-model="formData.keywords"
            placeholder="支持多个，逗号分隔"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item>
          <el-button type="primary" class="w-100px" @click="handleSubmit"
            >查询</el-button
          >
          <el-button
            type="primary"
            class="w-100px"
            @click="handleReset(formRef)"
            >重置</el-button
          >
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div class="text-12px text-right px-5px py-5px color-green-500">
    {{ msg }}
  </div>
  <el-table
    v-loading="loading"
    :data="rowData"
    :span-method="span"
    border
    class="w-full"
  >
    <el-table-column
      v-for="column in fixedColumns"
      :key="column.prop"
      :prop="column.prop"
      :label="column.label"
      :width="column.width"
      align="center"
    />
    <el-table-column prop="columnName" label="变更字段" align="center" />
    <el-table-column prop="beforeData" label="变更前" align="center" />
    <el-table-column prop="afterData" label="变更后" align="center" />
  </el-table>
  <div class="flex flex-row-reverse">
    <el-pagination
      v-model:current-page="current"
      :hide-on-single-page="true"
      :total="total"
      layout="prev, pager, next"
    />
  </div>
</template>
