<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { VxeGridProps } from 'vxe-table';
import qs from 'qs';
import { type Customer, SaleOrgList } from '@/stores/customer';
import { searchClients } from '@/api/maintainment';
import request from '@/utils/request';

interface Column {
  originSapOrderNo: string;
  deliveryNo: string;
  deliveryItemNo: string;
  sku: string;
  sapMaterialName: string;
  deliveryAmount: string;
  deliveryBatch: string;
}

// interface OrderData {
//   deliveryNo: string;
//   deliveryOriginCustomer: string;
//   originOrderCustomer: string;
//   items: Column[];
// }

const route = useRoute();
const administrator = ref(false);

const getPermission = async () => {
  const res = await request({
    url: '/api-opc/v1/so/template/so/create/modify/invoice/receiver/permission',
    method: 'get',
  });
  if (res.code === 200 && res.data === 'success') {
    administrator.value = true;
  } else {
    administrator.value = false;
  }
};
onMounted(() => {
  console.log('814创建订单');
  getPermission();
});

const form = reactive<{
  deliveryNos: string;
  deliveryOriginCustomer: string;
  deliveryOriginCustomerName: string;
  originOrderCustomer: string;
  originOrderCustomerName: string;
  distributionChannel: string;
  saleOrganization: string;
  customer: Customer;
}>({
  deliveryNos: '',
  deliveryOriginCustomer: '',
  deliveryOriginCustomerName: '',
  originOrderCustomer: '',
  originOrderCustomerName: '',
  distributionChannel: '',
  saleOrganization: '',
  customer: {},
});

const state = reactive<{
  loadingCustomer: boolean;
  submitLoading: boolean;
  num: number;
  customerList: Customer[];
}>({
  loadingCustomer: false,
  submitLoading: false,
  num: 0,
  customerList: [],
});

const isCustomerAvailable = (customer: Customer) => {
  const { companyValue } = route.query;

  if (customer && companyValue && companyValue.length > 2) {
    const { saleOrgList } = customer;
    return saleOrgList?.find(
      (item: SaleOrgList) =>
        item.salesOrganization &&
        item.salesOrganization.length >= 2 &&
        item.salesOrganization.slice(0, 2) === companyValue.slice(0, 2)
    );
  }
  return false;
};

const queryCustomerList = async (query: string) => {
  state.loadingCustomer = true;
  state.num++;
  const a = state.num;
  try {
    const result = await searchClients(query);
    state.loadingCustomer = false;
    if (a === state.num && result && result.code === 200) {
      state.customerList = [
        {
          customerNumber: '客户编码',
          customerName: '客户名称',
          cityName: '城市',
        },
        ...result.data,
      ];
    }
  } catch (error) {
    console.log(error);
  }
};

const gridOptions = reactive<VxeGridProps<Column>>({
  border: true,
  minHeight: 400,
  maxHeight: 600,
  showHeaderOverflow: true,
  size: 'small',
  align: 'center',
  scrollX: { enabled: true, gt: -1 },
  scrollY: { enabled: true, gt: 30 },
  columnConfig: {
    resizable: true,
  },
  columns: [
    { title: '全选', type: 'checkbox', width: 100, fixed: 'left' },
    {
      title: '原始订单编号',
      field: 'originSapOrderNo',
    },
    { title: 'SAP814单号', field: 'deliveryNo' },
    {
      title: 'SAP行号',
      field: 'deliveryItemNo',
    },
    {
      title: 'SKU',
      field: 'sku',
    },
    {
      title: '商品描述',
      field: 'sapMaterialName',
    },
    {
      title: '数量',
      field: 'deliveryAmount',
    },
    {
      title: '批次号',
      field: 'deliveryBatch',
    },
  ],
  toolbarConfig: {
    slots: {},
  },
  data: [],
});

const selectedItemList = ref<Column[]>([]);
const handleCheckboxChange = (event: any) => {
  const { records } = event;
  selectedItemList.value = records;
};

const deliveryNos = computed(() =>
  form.deliveryNos?.replaceAll(/\s+|,|，|;|；/g, ',')
);
const getOrderInfo = async () => {
  try {
    if (!form.deliveryNos) return ElMessage.error('请输入SAP-814单号！');
    const res = await request({
      url: `/api-opc/v1/so/template/queryOriginOrder/deliveryNo`,
      method: 'post',
      data: {
        deliveryNos: deliveryNos.value,
        orderBasis: route.query?.orderBasis,
        querySkuDetail: '0', // 是否查询sku详情
      },
    });
    if (res.code === 200 && res.data) {
      const {
        deliveryOriginCustomer,
        deliveryOriginCustomerName,
        originOrderCustomer,
        originOrderCustomerName,
        distributionChannel,
        saleOrganization,
      } = res.data;
      form.deliveryOriginCustomer = deliveryOriginCustomer || '';
      form.deliveryOriginCustomerName = deliveryOriginCustomerName || '';
      form.originOrderCustomer = originOrderCustomer || '';
      form.originOrderCustomerName = originOrderCustomerName || '';
      form.distributionChannel = distributionChannel || '';
      form.saleOrganization = saleOrganization || '';
      gridOptions.data = res.data.items;
      console.log(gridOptions.data);
    } else {
      ElMessage.error(res.msg || '查询失败');
    }
  } catch (error) {
    console.log(error);
  }
};

const goToOrderCreate = () => {
  const { companyValue, orderTypeValue, orderBasis } = route.query;
  window.location.replace(
    `/sr/create/${companyValue}/${orderTypeValue}?orderBasis=${orderBasis}&orderSource=SAP814`
  );
};

// 订单依据=更换抬头开票
const isChangeReceiver = computed(
  () => route.query.orderBasis === 'MODIFY_INVOICE_RECEIVER'
);
const submit = async () => {
  try {
    if (isChangeReceiver.value && Object.keys(form.customer).length === 0) {
      return ElMessage.warning('请选择客户');
    }
    if (selectedItemList.value.length === 0) {
      return ElMessage.warning('请选择商品行');
    }
    state.submitLoading = true;
    const sapOrderNos = selectedItemList.value
      ?.map((item) => item.originSapOrderNo)
      ?.join(',');

    const data = {
      isUploadFile: '0',
      newCustomer: form.customer?.customerNumber,
      newCustomerName: form.customer?.customerName,
      oldCustomer: form.originOrderCustomer,
      sapOrderNos,
      distributionChannel: form.distributionChannel,
      saleOrganization: form.saleOrganization,
    };
    const res = await request({
      url: `/api-opc/v1/so/template/updateInvoiceValidate`,
      method: 'POST',
      data,
    });
    if (res.code === 200) {
      // SAP单号+行号，用于跳转订单创建时自动带入商品行
      const deliveryItemNos = selectedItemList.value.map(
        (item: Column) =>
          `${item.deliveryNo || ''}_${item.deliveryItemNo || ''}`
      );
      const SAP814Info = {
        deliveryItemNos,
        customer: isChangeReceiver.value
          ? form.customer
          : { customerNumber: form.deliveryOriginCustomer },
      };
      const sapOrderNos = form.deliveryNos.replaceAll(/\s+|,|，|;|；/g, '_');
      localStorage.setItem(
        `SAP_814_ORDER_${sapOrderNos}`,
        JSON.stringify(SAP814Info)
      );
      const { companyValue, orderTypeValue, orderBasis } = route.query;
      const queryStr = {
        orderBasis,
        deliveryNos: sapOrderNos,
        orderSource: 'SAP814',
      };
      window.location.replace(
        `/sr/create/${companyValue}/${orderTypeValue}?${qs.stringify(queryStr)}`
      );
      // router.push(
      //   `/create/${companyValue}/${orderTypeValue}?${qs.stringify(queryStr)}`
      // );
    } else if (res.code === 301) {
      ElMessageBox.confirm(
        res.msg ||
          '开票抬头客户信用冻结，请确认是否继续？点击继续系统将触发消息给对应销售',
        '操作提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'error',
        }
      )
        .then(async () => {
          // 触发IT消息提醒给客户抬头下对应销售
          const sendRes = await request({
            url: `/api-opc/v1/so/template/sendCustomerCenterMsg`,
            method: 'POST',
            data: {
              ...data,
              msgTemplate: 'ModifyInvoiceReceiverTemplateMsg',
            },
          });
          if (sendRes.code === 200) {
            ElMessage.success('消息发送成功');
          } else {
            ElMessage.error(sendRes.msg || '消息发送失败');
          }
        })
        .catch(() => {
          console.log('已取消操作');
        });
    } else {
      ElMessageBox.alert(res.msg || '操作失败', '换抬头限制及退货行选择校验', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        type: 'error',
      });
    }
  } catch (error) {
    console.log(error);
  } finally {
    state.submitLoading = false;
  }
};
</script>

<template>
  <div class="p-10px">
    <el-form label-width="120px" class="mt-20px">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="SAP-814单号" prop="deliveryNos">
            <el-input
              v-model="form.deliveryNos"
              placeholder="请输入SAP-814单号，多个单号用逗号分隔"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="getOrderInfo">确认</el-button>
          <el-button
            v-if="administrator"
            type="primary"
            @click="goToOrderCreate"
          >
            无814直接创建
          </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="当前814下单客户">
            <el-input
              :value="`${form.deliveryOriginCustomer} ${form.deliveryOriginCustomerName}`"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="isChangeReceiver" :gutter="20">
        <el-col :span="12">
          <el-form-item label="原始下单客户">
            <el-input
              :value="`${form.originOrderCustomer} ${form.originOrderCustomerName}`"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="isChangeReceiver" :gutter="20">
        <el-col :span="12">
          <el-form-item label="新下单客户">
            <el-select
              v-model="form.customer"
              filterable
              clearable
              remote
              reserve-keyword
              placeholder="请输入客户编号/名称"
              class="flex-1"
              value-key="customerNumber"
              :remote-method="queryCustomerList"
              :loading="state.loadingCustomer"
            >
              <el-option
                v-for="(item, index) in state.customerList"
                :key="item.customerId"
                :label="item.customerName"
                :value="item"
                :disabled="index === 0 || !isCustomerAvailable(item)"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :class="index === 0 ? 'font-bold' : 'font-normal'"
                >
                  <div>{{ item.customerNumber }}</div>
                  <div>{{ item.cityName }}</div>
                  <div>{{ item.customerName }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      v-bind="gridOptions"
      @checkbox-change="handleCheckboxChange"
      @checkbox-all="handleCheckboxChange"
    />
    <div class="flex justify-center m-40px">
      <el-button type="primary" :loading="state.submitLoading" @click="submit"
        >确认选择</el-button
      >
    </div>
  </div>
</template>

<style scoped lang="scss">
.selectClientItem {
  display: flex;
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
