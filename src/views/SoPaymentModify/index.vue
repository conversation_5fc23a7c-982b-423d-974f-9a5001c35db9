<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { uniqBy } from 'lodash';
import SelectCustomer from '@/components/SelectCustomer.vue';
import UploadFiles from '@/components/UploadFiles.vue';
import request from '@/utils/request';
import { useCommonStore } from '@/stores/common';
import Header from './components/Header.vue';
import OrderDetail, { TableData } from './components/OrderDetail.vue';
import type { Customer } from '@/components/type';

const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);

const formRef = ref<FormInstance>();

const formData = ref({
  applyType: '销售订单款期修改',
  customerName: {} as Customer,
  customerNumber: '',
  targetPaymentTerm: '',
  applyReason: '',
  attachmentList: [],
});

// 应收总额、逾期金额、逾期超30天金额、逾期超60天金额
const invoicedOverdueAmountList = ref({
  receivableTotalAmount: '',
  invoicedOverdueAmountExceed0days: '',
  invoicedOverdueAmountExceed30days: '',
  invoicedOverdueAmountExceed60days: '',
});

const addOrderDialogVisible = ref(false);

const selectedItems = ref<TableData[]>([]);
const tableData = ref<TableData[]>([]);

const paymentTermsOptions = computed(() => {
  return (dictList.value.paymentTerms || [])
    .filter((item) => !(item.code.startsWith('K') || item.code === 'P001'))
    .map((item) => ({
      label: item.name,
      value: item.code,
    }));
});

const uploadUrl = computed(() => {
  return `/api-opc/change/paymentTerm/sos/batch/import?customerNo=${formData.value.customerNumber}&targetPaymentTerm=${formData.value.targetPaymentTerm}`;
});

const rules = {
  customerName: [{ required: true, message: '请选择客户', trigger: 'change' }],
  targetPaymentTerm: [
    { required: true, message: '请选择付款条件', trigger: 'change' },
  ],
  applyReason: [
    { required: true, message: '请输入申请原因', trigger: 'change' },
  ],
  attachmentList: [
    { required: true, message: '请上传附件', trigger: 'change' },
  ],
};

const handleCustomerChange = (value: Customer) => {
  formData.value.customerNumber = value.customerNumber || '';
  if (value.customerNumber) {
    request({
      url: '/data-center/customer/center/invoice/receivable/oa/summary',
      method: 'POST',
      data: {
        customerCode: value.customerNumber,
      },
    }).then((res) => {
      console.log(res);
      if (res.code === 200 && res.data) {
        invoicedOverdueAmountList.value = {
          receivableTotalAmount: res.data.receivableTotalAmount,
          invoicedOverdueAmountExceed0days:
            res.data.invoicedOverdueAmountExceed0days,
          invoicedOverdueAmountExceed30days:
            res.data.invoicedOverdueAmountExceed30days,
          invoicedOverdueAmountExceed60days:
            res.data.invoicedOverdueAmountExceed60days,
        };
      }
    });
  }
};

const openOrderDetailDialog = () => {
  addOrderDialogVisible.value = true;
};

const handleAddOrder = (val: TableData[]) => {
  console.log('handleAddOrder');
  tableData.value = [...tableData.value, ...val];
};

const downloadTemplate = () => {
  window.open(
    'https://static.zkh360.com/file/2025-01-08/%E9%94%80%E5%94%AE%E8%AE%A2%E5%8D%95%E6%94%B9%E6%AC%BE%E6%9C%9F%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-1736327878849.xlsx',
    '_blank'
  );
};

const handleSelectionChange = (val: TableData[]) => {
  selectedItems.value = val;
};

const handleBatchDelete = () => {
  try {
    tableData.value = tableData.value.filter(
      (item) => !selectedItems.value.includes(item)
    );
    selectedItems.value = [];
    ElMessage.success('删除成功!');
  } catch (error) {
    console.log(error);
    ElMessage.error('删除失败!');
  }
};

const handleDelete = (row: TableData) => {
  try {
    tableData.value = tableData.value.filter((item) => item.soNo !== row.soNo);
    ElMessage.success('删除成功!');
  } catch (error) {
    console.log(error);
    ElMessage.error('删除失败!');
  }
};

const handleUploadSuccess = (res: any) => {
  // ElMessage.success('上传成功!');
  console.log(res);
  if (res.data?.validOrders?.length > 0) {
    tableData.value = [...tableData.value, ...res.data.validOrders];
  }
  if (res.code === 200) {
    ElMessage.success('导入成功!');
  } else if (res.data) {
    const { remindMsg, invalidOrdersExcelUrl } = res.data;
    ElMessageBox.alert(
      `<div>${remindMsg}</div><div><a style="color: #409eff;text-decoration: none;" href="${invalidOrdersExcelUrl}" target="_blank">下载导入结果</a></div>`,
      '批量导入失败！',
      {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        type: 'error',
      }
    );
  } else {
    ElMessage.error(res.msg || '导入失败!');
  }
};

const handleUploadError = (error: any) => {
  console.log('error', error);
  ElMessage.error(error.message || '上传失败');
};

const handleSubmit = () => {
  if (tableData.value.length === 0) {
    ElMessage.error('请添加订单明细!');
    return;
  }
  formRef.value?.validate((valid) => {
    if (valid) {
      try {
        const submitHandler = () => {
          const userInfo = JSON.parse(
            localStorage.getItem('currentUserInfo') || '{}'
          );
          const sellerName = userInfo.nickname;
          const seller = userInfo.username;

          const data = {
            ...formData.value,
            customerName: formData.value.customerName.customerName,
            sellerName,
            seller,
            targetPaymentTermName: paymentTermsOptions.value.find(
              (item: any) => item.value === formData.value.targetPaymentTerm
            )?.label,
            tableData: tableData.value,
            ...invoicedOverdueAmountList.value,
          };
          request({
            url: '/api-opc/change/paymentTerm/sos/add/event',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            data,
          }).then((res: any) => {
            if (res.code === 200) {
              ElMessage.success('提交成功!');
            } else {
              ElMessage.error(res.msg || '提交失败,请重试!');
            }
          });
        };

        const uniqueRows = uniqBy(tableData.value, 'soNo');
        if (uniqueRows.length !== tableData.value.length) {
          ElMessageBox.confirm('订单添加重复,是否自动去重后提交？', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          })
            .then(() => {
              tableData.value = uniqueRows;
              submitHandler();
            })
            .catch(() => {
              return;
            });
        } else {
          submitHandler();
        }
      } catch (error) {
        console.log(error);
      }
    }
  });
};

onMounted(() => {
  if (JSON.stringify(dictList.value) === '{}') {
    commonStore.getDictList();
  }
});
</script>

<template>
  <div class="bg-#fff p-10px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      inline
      label-width="120px"
      label-suffix=":"
    >
      <Header title="申请类型" />
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="申请类型" prop="applyType" style="width: 100%">
            <el-input v-model="formData.applyType" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <Header title="客户信息" />
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="客户名称" prop="customerName">
            <SelectCustomer
              v-model="formData.customerName"
              @change="handleCustomerChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户编码">
            <span>{{ formData.customerNumber }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="修改后客户付款条件"
            prop="targetPaymentTerm"
            label-width="160px"
          >
            <el-select-v2
              v-model="formData.targetPaymentTerm"
              :options="paymentTermsOptions"
              filterable
              clearable
              style="width: 100%"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="4">
          <el-form-item label="应收总额">
            <span>{{ invoicedOverdueAmountList.receivableTotalAmount }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="逾期金额">
            <span>{{
              invoicedOverdueAmountList.invoicedOverdueAmountExceed0days
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="逾期超30天金额">
            <span>{{
              invoicedOverdueAmountList.invoicedOverdueAmountExceed30days
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="逾期超60天金额">
            <span>{{
              invoicedOverdueAmountList.invoicedOverdueAmountExceed60days
            }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <Header title="订单信息">
        <el-button type="primary" size="small" @click="downloadTemplate"
          >下载导入模板</el-button
        >
        <el-upload
          :action="uploadUrl"
          accept=".xlsx,.xls,.csv"
          :show-file-list="false"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
        >
          <el-button type="primary" size="small" class="m-b-2px"
            >批量导入订单</el-button
          >
        </el-upload>
        <el-button type="primary" size="small" @click="openOrderDetailDialog"
          >添加明细</el-button
        >
        <el-popconfirm
          title="确认批量删除吗？"
          icon="warning"
          @confirm="handleBatchDelete"
        >
          <template #reference>
            <el-button
              type="danger"
              size="small"
              style="margin-left: 0"
              :disabled="selectedItems.length === 0"
              >批量删除</el-button
            >
          </template>
        </el-popconfirm>
      </Header>
      <el-table
        :data="tableData"
        max-height="450"
        class="m-y-10px"
        border
        stripe
        highlight-current-row
        table-layout="auto"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column
          align="center"
          prop="soNo"
          label="OMS订单号"
          width="200px"
        >
          <template #default="{ row }">
            <el-link
              type="primary"
              target="_blank"
              :href="`/orderSale/formal/detail/${row.soNo}?soNo=${row.soNo}`"
              >{{ row.soNo }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column align="center" prop="sapOrderNo" label="SAP订单号" />
        <el-table-column align="center" prop="orderType" label="订单类型" />
        <el-table-column
          align="center"
          prop="paymentTermName"
          label="付款条款"
        />
        <el-table-column align="center" prop="autoBatching" label="是否整单">
          <template #default="{ row }">
            {{ row.autoBatching === 'X' ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="deliveryStatus"
          label="发货状态"
        />
        <el-table-column
          align="center"
          prop="customerOrderNo"
          label="客户订单号"
          width="120px"
        />
        <el-table-column align="center" prop="orderSource" label="订单来源" />
        <el-table-column align="center" prop="sellerName" label="销售" />
        <el-table-column
          align="center"
          prop="customerServiceName"
          label="客服"
        />
        <el-table-column align="center" prop="gmtCreate" label="创建时间" />
        <el-table-column align="center" label="操作">
          <template #default="{ row }">
            <el-popconfirm
              title="确认删除该条明细？"
              icon="warning"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <Header title="申请原因" />
      <el-form-item label="申请原因" prop="applyReason">
        <el-input
          v-model="formData.applyReason"
          type="textarea"
          maxlength="500"
          show-word-limit
          style="width: 60%"
          :rows="4"
          placeholder="请输入申请原因"
        />
      </el-form-item>
      <el-form-item label="附件" style="width: 800px" prop="attachmentList">
        <UploadFiles
          v-model="formData.attachmentList"
          text="请上传附件,如合同"
          :limit="10"
        />
      </el-form-item>
    </el-form>
    <div class="m-t-10px flex justify-center">
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>

    <OrderDetail
      v-if="addOrderDialogVisible"
      v-model:show-order-detail="addOrderDialogVisible"
      :customer-no="formData.customerNumber"
      @add-order="handleAddOrder"
    />
  </div>
</template>

<style scoped lang="scss">
.el-form-item {
  width: 100%;
  margin-bottom: 10px;
  margin-top: 10px;
}
</style>
