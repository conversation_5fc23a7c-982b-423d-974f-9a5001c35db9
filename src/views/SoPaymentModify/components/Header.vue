<script setup lang="ts">
defineProps<{
  title: string;
}>();
</script>

<template>
  <div class="flex justify-between bg-#f6f7fc h-32px">
    <div class="flex items-center">
      <div class="title-prefix" />
      <span class="font-500 color-#333 text-14px">
        {{ title }}
      </span>
    </div>
    <div class="inline-flex items-center m-r-12px gap-2">
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.title-prefix {
  width: 4px;
  height: 20px;
  background: #409dff;
  margin: 0 12px;
}

.gap-line {
  width: 1px;
  height: 16px;
  background: #e1eaff;
  margin: 0 30px;
}
</style>
