<script setup lang="ts">
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { computed, reactive, ref } from 'vue';
import request from '@/utils/request';

export interface TableData {
  soNo: string;
  sapOrderNo: string;
  orderType: string;
  paymentTerm: string;
  paymentTermName: string;
  autoBatching: string;
  deliveryStatus: string;
  customerOrderNo: string;
  orderSource: string;
  seller: string;
  sellerName: string;
  customerService: string;
  customerServiceName: string;
  gmtCreate: string;
  canChoose: boolean;
  unChooseReason: string;
}

interface IParam {
  soNos?: string;
  customerNo?: string;
  targetPaymentTerm?: string;
}
interface Table {
  formData: IParam;
  tableList: TableData[];
  selectedItems: TableData[];
  pageLoading: boolean;
}

const props = defineProps({
  showOrderDetail: { type: Boolean, required: true },
  customerNo: { type: String, default: '' },
  targetPaymentTerm: { type: String, default: '' },
});
const emit = defineEmits(['update:showOrderDetail', 'addOrder']);
const showDetail = computed({
  get: () => props.showOrderDetail,
  set: (val) => emit('update:showOrderDetail', val),
});
const state = reactive<Table>({
  formData: {
    soNos: '',
  },
  tableList: [],
  selectedItems: [],
  pageLoading: false,
});
const detailFormRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  soNos: [{ required: true, message: '请输入单号', trigger: 'blur' }],
});

const closeDialog = () => {
  emit('update:showOrderDetail', false);
};

const getList = (formEl: FormInstance | undefined) => {
  // if (!props.customerNo) return ElMessage.error('请输入客户名称或编码！');
  formEl?.validate(async (valid) => {
    if (valid) {
      state.pageLoading = true;
      const data: IParam = {
        soNos: state.formData.soNos
          ?.split(/[\s,;、，；]/g)
          .filter(Boolean)
          .join(','),
        customerNo: props.customerNo,
        targetPaymentTerm: props.targetPaymentTerm,
      };
      try {
        const res: any = await request({
          url: `/api-opc/change/paymentTerm/sos/query`,
          method: 'post',
          data,
        });
        if (res.code === 200 && res?.data) {
          state.tableList = res.data;
        } else {
          ElMessage.error(res.msg);
          // state.tableList = [];
        }
      } finally {
        state.pageLoading = false;
      }
    }
  });
};

const handleSelectionChange = (val: TableData[]) => {
  state.selectedItems = val;
};

const addOrderDetail = () => {
  const data = state.selectedItems;
  if (data.length === 0) return ElMessage.error('请至少勾选一项订单明细！');
  if (data.length > 500)
    return ElMessage.error('单次最多提交500个，请减少勾选后重新提交');
  closeDialog();
  emit('addOrder', data);
};

const rowSelectable = (row: TableData) => {
  return row.canChoose;
};
</script>

<template>
  <el-dialog
    v-model="showDetail"
    title="销售订单明细"
    :before-close="closeDialog"
    width="1200px"
  >
    <div v-loading="state.pageLoading">
      <el-form
        ref="detailFormRef"
        :inline="true"
        :model="state.formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item class="m-b-10px" label="订单号：" prop="soNos">
          <el-input
            v-model="state.formData.soNos"
            clearable
            style="width: 300px"
            type="textarea"
            placeholder="支持通过外围/OMS/SAP/客户订单号检索"
          />
        </el-form-item>
        <el-form-item class="m-b-10px">
          <el-button type="primary" @click="getList(detailFormRef)"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
      <div class="text-14px font-500 m-l-100px m-b-10px">
        支持通过外围/OMS/SAP/客户订单号检索，同时支持10个单号，以英文空格分隔
      </div>
      <div class="result">
        <p>查询结果：</p>
        <el-table
          :data="state.tableList"
          style="width: 100%"
          max-height="450"
          border
          stripe
          highlight-current-row
          table-layout="auto"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            :selectable="rowSelectable"
            align="center"
            width="55"
          />
          <el-table-column
            align="center"
            prop="unChooseReason"
            label="不可选中原因"
          >
            <template #default="{ row }">
              <span v-if="row.unChooseReason" class="text-red-500">{{
                row.unChooseReason
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="soNo"
            label="OMS订单号"
            width="160px"
          />
          <el-table-column align="center" prop="sapOrderNo" label="SAP订单号" />
          <el-table-column align="center" prop="orderType" label="订单类型" />
          <el-table-column
            align="center"
            prop="paymentTermName"
            label="付款条款"
          />
          <el-table-column align="center" prop="autoBatching" label="是否整单">
            <template #default="{ row }">
              {{ row.autoBatching === 'X' ? '否' : '是' }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="deliveryStatus"
            label="发货状态"
          />
          <el-table-column
            align="center"
            prop="customerOrderNo"
            label="客户订单号"
            width="120px"
          />
          <el-table-column align="center" prop="orderSource" label="订单来源" />
          <el-table-column align="center" prop="sellerName" label="销售" />
          <el-table-column
            align="center"
            prop="customerServiceName"
            label="客服"
          />
          <el-table-column align="center" prop="gmtCreate" label="创建时间" />
        </el-table>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" @click="addOrderDetail">确认添加</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
