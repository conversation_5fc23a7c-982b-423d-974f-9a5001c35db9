<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import request from '@/utils/request';
import NoLogisticsWarning from './NoLogisticsWarning.vue';

type LogisticsType =
  | 'KH_LOGISTICS_PATH'
  | 'SELF_LOGISTICS_PATH'
  | 'THIRD_PARTY_SERVICE_LOGISTICS'
  | 'THIRD_SELF_LOGISTICS_PATH';

export interface LogisticsOrderInfo {
  deliveryNo: string;
  deliveryType: string;
  deliveryTypeName: string;
  logisticsCode: string;
  logisticsNo: string;
  logisticsType: LogisticsType;
}

interface LogisticsStepInfo {
  carrierName: string;
  logisticsCode: string;
  clerk?: string;
  clerkPhone?: string;
  pri?: string;
  logisticsContent: {
    address: string;
    remark: string;
    status: string;
    trackTime: string;
  }[];
}

interface QueryParams {
  deliveryNumber?: string;
  logisticsCode?: string;
  trackingNumber?: string;
  company?: string;
  type?: LogisticsType;
  phone?: string;
}

interface OrderParams {
  soNo: any;
}

const route = useRoute();
let { soNo, deliveryNo, logisticsCode, logisticsCompanyNo } = route.query; // deliveryNo: DN(ZKH发货)/VC(供应商直发)送货单号
const logisticsInfos = ref<LogisticsOrderInfo[]>([]);
const name = ref('');
const phone = ref('');
const logisticsStepInfo = ref<LogisticsStepInfo[]>([]);
const currentLogistics = ref<string>('');
const loading = ref(false);
const logisticsTabs = ref<LogisticsOrderInfo[]>([]);

// 如果传了物流单号和物流公司，直接用第三方物流查物流轨迹；
// 否则，用soNo查出所有SO履约进度物流信息，用deliveryNo匹配；
const queryOrderInfo = async (params: OrderParams) => {
  loading.value = true;
  try {
    const res = await request({
      url: '/api-opc/v1/so/purchase/progress/list',
      method: 'POST',
      params: { soNo: params.soNo, mergeAtp: true },
    });
    if (res && res.success) {
      logisticsInfos.value = res.data?.logisticsInfos;
      phone.value = res.data?.receiverPhoneNumber;
      if (logisticsCode) {
        await getLogisticsByLogisticsCode();
      } else if (res.data?.logisticsInfos?.length > 0) {
        const curList = res.data?.logisticsInfos?.filter(
          (item: LogisticsOrderInfo) => item.deliveryNo === deliveryNo
        ) as LogisticsOrderInfo[];
        logisticsTabs.value = curList || [];
        currentLogistics.value = curList[0]?.logisticsNo;
        await getLogisticsByDeliveryNo(curList[0]);
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const handleClick = async (tab: any) => {
  const { paneName } = tab;
  currentLogistics.value = paneName;
  const cur = logisticsTabs.value?.find(
    (item: LogisticsOrderInfo) => item.logisticsNo === paneName
  ) as LogisticsOrderInfo;
  await getLogisticsByDeliveryNo(cur);
};
const queryNew = async (params: QueryParams) => {
  try {
    loading.value = true;
    const res = await request({
      url: '/api-logistics/logistics/pathForTC',
      method: 'get',
      params,
    });
    if (res?.success === false) {
      logisticsStepInfo.value = [
        {
          carrierName: '',
          logisticsCode: '',
          clerk: '',
          clerkPhone: '',
          logisticsContent: [],
        },
      ];
      name.value = '';
      phone.value = '';
      ElMessageBox.alert(res?.message || '请求错误', '错误', {
        confirmButtonText: '确定',
        type: 'error',
      });
    } else if (res?.result) {
      const arr: LogisticsStepInfo[] = [];
      const logisticsContent = res.result?.logisticsContent || [];
      logisticsContent.forEach((a: any) => {
        if (
          a.carrierName !== arr[0]?.carrierName ||
          a.logisticsCode !== arr[0]?.logisticsCode
        ) {
          arr.unshift({
            carrierName: a.carrierName,
            logisticsCode: a.logisticsCode,
            pri: a.pri,
            logisticsContent: [a],
          });
        } else {
          arr[0].logisticsContent.push(a);
        }
      });
      const result = await request({
        url: '/api-logistics/transportInfo/queryMulti',
        method: 'post',
        data: [params.deliveryNumber],
      });
      const tskInfos =
        (result && result?.result[0] && result?.result[0]?.tskInfos) || [];
      const obj = tskInfos && tskInfos.length > 0 ? tskInfos.at(-1) : {};
      name.value = obj.clerk || '';
      phone.value = obj.clerkPhone || '';
      logisticsStepInfo.value = arr.reverse();
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const query = async (params: QueryParams) => {
  try {
    loading.value = true;
    const res = await request({
      url: '/api-logistics/logistics/path',
      method: 'get',
      params,
    });
    if (res?.success === false) {
      logisticsStepInfo.value = [
        {
          carrierName: '',
          logisticsCode: '',
          clerk: '',
          clerkPhone: '',
          logisticsContent: [],
        },
      ];
      ElMessageBox.alert(res?.message || '请求错误', '错误', {
        confirmButtonText: '确定',
        type: 'error',
      });
    } else if (res?.result) {
      const { logisticsInfo } = res.result;
      logisticsInfo.logisticsCode = logisticsInfo?.transportCode || '';
      logisticsStepInfo.value = [logisticsInfo];
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (!!soNo && soNo !== 'undefined') {
    queryOrderInfo({ soNo: soNo as string });
  }
});

const getLogisticsByDeliveryNo = (found: LogisticsOrderInfo) => {
  const { deliveryNo, logisticsNo } = found;
  const _params: QueryParams = {
    deliveryNumber: (deliveryNo as string) || '',
    logisticsCode: (logisticsNo as string) || '',
  };
  return queryNew(_params);
};

const getLogisticsByLogisticsCode = async () => {
  if (!logisticsCompanyNo) {
    const res = await request({
      url: `api-sales/standard/customer/getLogisticsCompanyName?number=${logisticsCode}`,
      method: 'get',
    });
    if (res?.code === 200) {
      logisticsCompanyNo = res.data.comCode;
    }
  }
  if (logisticsCode && logisticsCompanyNo) {
    const _params: QueryParams = {
      deliveryNumber: '',
      trackingNumber: (logisticsCode as string) || '',
      company: logisticsCompanyNo as string,
      type: 'THIRD_PARTY_SERVICE_LOGISTICS',
      phone: phone.value,
    };
    return query(_params);
  }
};
</script>

<template>
  <div class="px-27px">
    <div v-loading="loading" class="m-10px text-12px" style="padding: 10px">
      <el-tabs
        v-if="logisticsTabs.length > 1"
        v-model="currentLogistics"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="item in logisticsTabs"
          :key="item.logisticsNo"
          :label="item.logisticsNo"
          :name="item.logisticsNo"
        />
      </el-tabs>
      <div
        v-if="phone || name"
        class="bg-#f4f4f4 text-#666 h-40px m-0 flex items-center px-10px"
        style="margin-bottom: 10px"
      >
        <span v-if="name" style="margin-right: 10px"
          >联系人: <em class="em">{{ name }}</em></span
        >
        <span v-if="phone"
          >联系电话: <em class="em">{{ phone }}</em></span
        >
      </div>
      <div v-for="(a, i) in logisticsStepInfo" :key="i">
        <div class="bg-#f4f4f4 text-#666 h-40px m-0 flex items-center px-10px">
          <div v-if="a?.pri === '1'">
            <em class="em">【正在发往商家转运中心】</em>
          </div>
          <div v-else>
            <span v-if="a?.carrierName" style="margin-right: 10px"
              >该订单被<em class="em">【{{ a?.carrierName }}】</em>承运</span
            >
            <span v-if="a?.logisticsCode"
              >运单号：<em class="em">【{{ a?.logisticsCode }}】</em></span
            >
          </div>
        </div>
        <div style="margin-top: 10px">
          <el-timeline class="ml-120px mt-30px">
            <el-timeline-item
              v-for="item in a?.logisticsContent"
              :key="item.trackTime"
              color="#1890ff"
            >
              <div class="absolute -left-180px">
                {{ item.trackTime }}
              </div>
              <div class="flex-1 pl-50px pr-10px overflow-y-auto">
                <div class="font-bold">{{ item.status }}</div>
                <div>{{ item.remark }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div
            v-if="!a?.logisticsContent?.length"
            class="flex items-center justify-center h-min-500px"
          >
            <NoLogisticsWarning
              :logistics-tabs="logisticsTabs"
              :current-logistics="currentLogistics"
            />
          </div>
        </div>
      </div>
      <div
        v-if="!logisticsStepInfo?.length"
        class="flex items-center justify-center h-min-500px"
      >
        <NoLogisticsWarning
          :logistics-tabs="logisticsTabs"
          :current-logistics="currentLogistics"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.em {
  color: #333;
  font-style: normal;
  font-weight: 700;
}
.time {
  position: relative;
}
.time::before {
  content: '';
  position: absolute;
  top: 16px;
  left: 185px;
  width: 9px;
  height: 9px;
  background-color: #cecece;
  border-radius: 100%;
  background-color: #1890ff;
}
</style>
