<script setup lang="ts">
import { computed } from 'vue';
import { type LogisticsOrderInfo } from './LogisticsStep.vue';
const props = defineProps<{
  logisticsTabs: LogisticsOrderInfo[];
  currentLogistics: string;
}>();

const tableData = computed(() => {
  return props.logisticsTabs.filter(
    (item) => item.logisticsNo === props.currentLogistics
  );
});
</script>

<template>
  <div>
    <el-result icon="warning" title="抱歉! 暂无物流信息" sub-title="" />
    <el-table :data="tableData" border>
      <el-table-column prop="deliveryNo" label="送货单号" width="200" />
      <el-table-column prop="logisticsNo" label="物流单号" width="200">
        <template #default="{ row }"> {{ row.logisticsNo || '暂无' }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>
