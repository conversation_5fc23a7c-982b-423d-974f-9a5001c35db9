<!--
 * @Author: luozhikai
 * @Date: 2023-07-14 15:18:45
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-08-16 14:26:19
 * @Description: file content
-->
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { debounce } from 'lodash';
import { searchClients } from '@/api/maintainment';

interface Option {
  customerNumber: string;
  cityName: string;
  customerName: string;
}

const props = defineProps({
  value: { type: String, default: '' },
  propsName: { type: String, default: '' },
  clearable: { type: Boolean, default: true },
});
const emits = defineEmits(['input', 'select']);

const loading = ref<boolean>(false);
const options = ref<Option[]>([]);
const name = ref<string>('');

onMounted(() => {
  name.value = props.value;
  if (props.propsName) {
    options.value = [
      {
        customerNumber: '客户编码',
        cityName: '城市',
        customerName: '客户名称',
      },
      {
        customerNumber: props.value,
        cityName: '',
        customerName: props.propsName,
      },
    ];
  }
});

const afterChangeCustomer = (val: any) => {
  const target = options.value.find((item) => item.customerNumber === val);
  emits('select', target);
};
const afterClear = () => {
  options.value = [];
};

const getCustomerList = debounce((query: string) => {
  if (query) {
    loading.value = true;
    searchClients(query.trim())
      .then((res: any) => {
        console.log('customer list', res);
        if (res.code === 200 && res.data) {
          options.value = [
            {
              customerNumber: '客户编码',
              cityName: '城市',
              customerName: '客户名称',
            },
            ...res.data,
          ];
        }
        loading.value = false;
      })
      .catch((error: any) => {
        console.log(error);
        loading.value = false;
      });
  }
}, 1000);
</script>

<template>
  <el-select
    popper-class="module-select-customer"
    class="w-full"
    filterable
    :clearable="clearable"
    remote
    placeholder="单个客户编码搜索"
    :model-value="props.value"
    :remote-method="getCustomerList"
    :loading="loading"
    @change="afterChangeCustomer"
    @clear="afterClear"
  >
    <el-option
      v-for="(item, index) in options"
      :key="item.customerNumber"
      :label="item.customerName"
      :value="item.customerNumber"
      :disabled="index === 0"
    >
      <p class="option" :class="{ bold: index === 0 }">
        <span>{{ item.customerNumber }}</span>
        <span>{{ item.cityName }}</span>
        <span>{{ item.customerName }}</span>
      </p>
    </el-option>
  </el-select>
</template>

<style lang="scss">
.module-select-customer {
  width: 500px;

  .option {
    display: flex;

    span {
      width: 20%;

      &:last-child {
        width: auto;
      }
    }
  }
}
</style>
