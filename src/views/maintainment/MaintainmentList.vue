<script setup lang="ts">
import { computed, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import {
  addCustomerProduct,
  batchOperation,
  editCustomerProduct,
  getEnumData,
  getImportTemplate,
  getList,
} from '@/api/maintainment';
import { getQueryObject } from '@/utils/index';
import { useCommonStore } from '@/stores/common';
import CustomerSelect from './CustomerSelect.vue';
import EditModal from './EditModal.vue';
import { listTableCols } from './option';
import type { FormData, ListItem, Option } from '@/constants/index';
const initFormData = {
  customerCode: '',
  groupCommonStatus: '0,1',
  status: '0,1',
  pageNo: 1,
  pageSize: 20,
};

const commonStore = useCommonStore();
const acceptFileType = computed(() => commonStore.acceptFileType || {});

const router = useRouter();

const show = ref<boolean>(false);
const current = ref<Option>();
const loading = ref<boolean>(false);
const modalLoading = ref<boolean>(false);
const exportLoading = ref<boolean>(false);
const batchLoading = ref<boolean>(false);
const rules = ref<any>({
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'change' },
  ],
});
const formData = ref<FormData>({ ...initFormData });
const form = ref<any>({ customerName: '' });
const tableData = ref<ListItem[]>([]);
const total = ref<number>(0);
const enumData = ref<any>({
  groupCommon: [],
  dataStatus: [],
  dataSource: [],
});
const modalType = ref<string>('edit');
const modalVisible = ref<boolean>(false);
const modalDetail = ref<ListItem>({} as ListItem);
const selectedRows = ref<ListItem[]>([]);

const lastSearchCustomerName = ref<string>('');

const selectCustomer = (val: any) => {
  formData.value.customerCode = val?.customerNumber;
  formData.value.customerName = val?.customerName;
};

const openEditModal = (val: any, row: ListItem) => {
  modalVisible.value = true;
  modalType.value = val;
  modalDetail.value = row;
};

const handleSelect = (sr: any) => {
  console.log(sr);
  selectedRows.value = [...sr];
};

// 单行的操作
const handleAction = (type: string, row: ListItem) => {
  console.log(type, row);
  let commitType = '';
  if (type === 'changeStatus') {
    commitType = row.status === '1' ? '3' : '2';
  }
  if (type === 'changeCommonStatus') {
    commitType = row.groupCommonStatus === '1' ? '6' : '5';
  }
  if (type === 'reuse') {
    if (!formData.value.customerCode)
      return ElMessage.error('请选择客户编号后再复用');
    const reuseRow: ListItem = {
      ...row,
      showCustomerCode: formData.value.customerCode,
      showCustomerName: formData.value.customerName,
      dataSource: 'BOSS',
    };
    openEditModal('add', reuseRow);
  }
  if (!commitType) return;
  commitAction(commitType, [row]);
};

const handleBatchAciton = (type: string) => {
  console.log(type, selectedRows.value);
  commitAction(type, selectedRows.value);
};

const commitAction = (type: string, data: any[]) => {
  console.log(type, data);
  let params: any = {};
  params.operateType = type;
  params.idList = data.map((d) => d.id);
  if (type === '1') {
    // 复用
    if (!formData.value.customerCode)
      return ElMessage.error('请选择客户编号后再复用');
    params.targetCustomerCode = formData.value.customerCode;
  }
  if (type === '2') {
    // 生效
    params.changedStatus = '1';
  }
  if (type === '3') {
    // 失效
    params.changedStatus = '0';
  }
  if (type === '4') {
    // 删除
  }
  if (type === '5') {
    // 集团共用
    params.changedGroupCommon = '1';
  }
  if (type === '6') {
    // 取消集团共用
    params.changedGroupCommon = '0';
  }
  if (type === '7') {
    // 导出,使用当前查询条件作为参数
    exportLoading.value = true;
    const { pageNo, pageSize, ...others } = formData.value;
    params = {
      operateType: type,
      ...others,
    };
    params.skuList = params.skuList ? params.skuList.split(' ') : [];
    params.customerMaterialNoList = params.customerMaterialNoList
      ? params.customerMaterialNoList.trim().split(' ')
      : [];
    // 如果勾选了列表中的条目就使用勾选的，没有勾选则使用搜索栏的值
    params.idList =
      data.length > 0
        ? data.map((d) => d.id)
        : params.idList
          ? params.idList.split(' ')
          : [];
  } else {
    batchLoading.value = true;
  }

  batchOperation(params)
    .then((res: any) => {
      if (res.code === 200) {
        search();
        if (type === '7') {
          ElMessage.success(res.data);
          if (window.self !== window.top) {
            window?.top?.postMessage({ source: 'sr', type: 'download' }, '*');
          }
        } else {
          ElMessage.success(res.msg);
        }
      } else {
        ElMessage.error({
          message: formatMsg(res),
          duration: 10000,
          showClose: true,
        });
      }
    })
    .finally(() => {
      exportLoading.value = false;
      batchLoading.value = false;
    });
};

const getTemplate = () => {
  getImportTemplate().then((res: any) => {
    if (res.code === 200) {
      window.open(res.data);
    } else {
      ElMessage.error(formatMsg(res));
    }
  });
};

const handleOk = (data: ListItem) => {
  console.log(data);
  const d: any = { ...data };
  d.customerCode = data.showCustomerCode;
  d.customerName = data.showCustomerName;
  if (modalType.value === 'edit') {
    // 编辑
    modalLoading.value = true;
    editCustomerProduct(d)
      .then((res: any) => {
        if (res.code === 200) {
          ElMessage.success('编辑成功');
          modalVisible.value = false;
          search();
        } else {
          ElMessage.error(formatMsg(res));
        }
      })
      .finally(() => {
        modalLoading.value = false;
      });
  } else if (modalType.value === 'add') {
    modalLoading.value = true;
    addCustomerProduct(d)
      .then((res: any) => {
        if (res.code === 200) {
          ElMessage.success('新增成功');
          modalVisible.value = false;
          search();
        } else {
          ElMessage.error(formatMsg(res));
        }
      })
      .finally(() => {
        modalLoading.value = false;
      });
  } else {
    // 详情
    modalVisible.value = false;
  }
};

const updateModalVisible = (val: any) => {
  modalVisible.value = val;
};

const routeToDetail = (id: string) => {
  if (window.self === window.top) {
    router.push(`maintainment/detail/${id}`);
  } else {
    window?.top?.postMessage({ source: 'sr', id }, '*');
  }
};
const handleSearch = () => {
  formData.value.pageNo = 1;
  search();
};
const search = (data?: FormData) => {
  // formData.value.customerCode = formData.value.customerCode || null;
  loading.value = true;
  const params = data || { ...formData.value };
  params.skuList = params.skuList ? params.skuList.trim().split(' ') : [];
  params.idList = params.idList ? params.idList.trim().split(' ') : [];
  params.customerMaterialNoList = params.customerMaterialNoList
    ? params.customerMaterialNoList.trim().split(' ')
    : [];
  Object.keys(params).forEach((key: string) => {
    if (typeof params[key as keyof FormData] === 'string') {
      params[key as keyof FormData] = params[key as keyof FormData].trim();
    }
  });
  getList(params)
    .then((res: any) => {
      console.log(res);

      if (res.code === 200 && res.data) {
        tableData.value = res.data;

        total.value = res.total;
      }
    })
    .catch((error: any) => {
      console.log(error);
    })
    .finally(() => {
      loading.value = false;
    });
};

const resetSearch = () => {
  console.log('reset');
  formData.value = { ...initFormData };
};

const getEnum = () => {
  getEnumData().then((res: any) => {
    console.log(res);

    if (res.code === 200 && res.data) {
      enumData.value = res.data;
    }
  });
};

const onUploadSuccess = (res: any) => {
  if (res.code === 200) {
    ElMessage.success('上传完成');
    // Attention: 数据插入有延迟
    setTimeout(() => {
      search();
    }, 1500);
  } else if (res.data?.resultExcelUrl) {
    // ElNotification({
    //   title: '上传失败',
    //   message: '点击下载失败表格',
    //   type: 'error',
    //   duration: 5000,
    //   showClose: false,
    //   onClick: () => {
    //     window.open(res.data.resultExcelUrl);
    //   },
    // });
    ElMessageBox.confirm('点击下载失败表格', '上传失败', {
      distinguishCancelAndClose: true,
      confirmButtonText: '下载',
      cancelButtonText: '关闭',
      closeOnClickModal: false,
      type: 'error',
    }).then(() => {
      window.open(res.data.resultExcelUrl);
    });
  } else {
    ElMessage.error(res.msg);
  }
};
const afterChangeCustomer = (target: Option) => {
  // let target = this.option.customer.find(item => item.customerNumber === val)
  current.value = target;
  console.log(target);
};
const add = () => {
  current.value = {} as Option;
  show.value = true;
};
const createCustomer = () => {
  if (!(current.value && current.value.customerName)) {
    ElMessage.error('请选择客户');
    return;
  }
  const { customerNumber } = current.value;
  routeToDetail(customerNumber);
};
const handleSizeChange = (res: number) => {
  formData.value.pageSize = res;
  search();
};
const handleCurrentChange = (res: number) => {
  formData.value.pageNo = res;
  search();
};
onBeforeMount(() => {
  const options = localStorage.getItem('sr-maintainment-search-options');
  console.log('localStorage', options);
  if (options) {
    formData.value = JSON.parse(options);
    if (formData.value.customerName) {
      // 用于回显
      lastSearchCustomerName.value = formData.value.customerName;
    }
  }
  initQuery();
  if (JSON.stringify(formData.value) !== JSON.stringify(initFormData)) {
    // 无搜索条件时不自动查询，避免查询太慢
    search();
  }
  getEnum();
});

const initQuery = () => {
  const queryObj = getQueryObject(window.location.href);
  if (queryObj.id) {
    formData.value.idList = queryObj.id;
  }
};

watch(
  formData,
  (cur) => {
    console.log('formdata.change', cur);
    localStorage.setItem('sr-maintainment-search-options', JSON.stringify(cur));
  },
  { deep: true }
);

onBeforeUnmount(() => {
  console.log('unmount', formData.value);
});

const formatMsg = (res: any): string => {
  if (res.data) return `${res.msg}:${res.data}`;
  return res.msg;
};
</script>

<template>
  <div class="app-container page-customer-maintainment">
    <div class="module-formData">
      <el-form label-position="right" label-width="130px" label-suffix=":">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户编号">
              <customer-select
                v-model:value="formData.customerCode"
                v-model:name="lastSearchCustomerName"
                @select="selectCustomer"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="震坤行物料号">
              <el-input
                v-model="formData.skuList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料号">
              <el-input
                v-model="formData.customerMaterialNoList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料关系代码">
              <el-input
                v-model="formData.idList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户物料名称">
              <el-input
                v-model="formData.customerMaterialName"
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料规格型号">
              <el-input
                v-model="formData.customerMaterialSpecification"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="集团共用">
              <el-select
                v-model="formData.groupCommonStatus"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in enumData.groupCommon"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.dataSourceList"
                multiple
                clearable
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in enumData.dataSource"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select
                v-model="formData.status"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in enumData.dataStatus"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :offset="12" :span="6">
            <span class="float-right">
              <el-button :loading="loading" type="primary" @click="handleSearch"
                >查询</el-button
              >
              <el-button @click="resetSearch">重置</el-button>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="module-list">
      <span class="module-btns">
        <el-tooltip
          placement="top-start"
          content="模板只限于批量新增,批量修改模板请直接使用导出的模板进行修政"
        >
          <el-button type="primary" @click="getTemplate">获取模板</el-button>
        </el-tooltip>
        <el-dropdown>
          <el-button type="primary"
            >导入<el-icon class="el-icon--right"><arrow-down /></el-icon
          ></el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="insert">
                <el-upload
                  action="/api-customer-product/customerProduct/relation/batchInsert/customerMaterialRelation"
                  :on-success="onUploadSuccess"
                  :accept="acceptFileType.soCommonType"
                  :show-file-list="false"
                >
                  批量新增
                </el-upload>
              </el-dropdown-item>
              <el-dropdown-item command="insert">
                <el-upload
                  action="/api-customer-product/customerProduct/relation/batchUpdate/customerMaterialRelation"
                  :on-success="onUploadSuccess"
                  :accept="acceptFileType.soCommonType"
                  :show-file-list="false"
                >
                  批量修改
                </el-upload>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          :loading="exportLoading"
          type="primary"
          @click="() => handleBatchAciton('7')"
          >导出</el-button
        >
        <el-dropdown @command="handleBatchAciton">
          <el-button
            :loading="batchLoading"
            :disabled="selectedRows.length === 0"
            type="primary"
          >
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="1">复用</el-dropdown-item>
              <el-dropdown-item command="2">生效</el-dropdown-item>
              <el-dropdown-item command="3">失效</el-dropdown-item>
              <!-- <el-dropdown-item command="4">删除</el-dropdown-item> -->
              <el-dropdown-item command="5">集团共用</el-dropdown-item>
              <el-dropdown-item command="6">取消集团共用</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="add">新增</el-button>
      </span>

      <el-table
        v-loading="loading"
        show-overflow-tooltip
        :data="tableData"
        class="el-table-customer"
        @selection-change="handleSelect"
        @select-all="handleSelect"
      >
        <el-table-column type="selection" width="55" fixed />
        <el-table-column
          v-for="col in listTableCols"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :align="col.align"
          :width="col.width"
          header-align="center"
        >
          <template #default="scope">
            <!-- <router-link
              v-if="col.prop === 'customerName'"
              :to="{ path: `maintainment/${scope.row.id}` }"
              :title="scope.row.customerName"
            >
              {{ scope.row.customerName }}
            </router-link> -->
            <template v-if="col.prop === 'showCustomerName'">
              <el-button
                type="primary"
                link
                @click="() => routeToDetail(scope.row.showCustomerCode)"
                >{{ scope.row.showCustomerName }}</el-button
              >
            </template>
            <template v-if="col.prop === 'zkhSkuNo'">
              <span
                >{{ scope.row.zkhSkuNo }}
                <span v-if="scope.row.ifProductOil"
                  >&nbsp;<el-tag type="danger" size="small"
                    >成品油</el-tag
                  ></span
                ></span
              >
            </template>
            <template v-if="col.prop === 'groupCommonStatus'">
              {{
                scope.row.groupCommonStatus === '1'
                  ? '是'
                  : scope.row.groupCommonStatus === '0'
                    ? '否'
                    : ''
              }}
            </template>
            <template v-if="col.prop === 'status'">
              <i
                v-if="scope.row.status === '1'"
                class="status-icon status-on"
              />
              <i
                v-else-if="scope.row.status === '0'"
                class="status-icon status-off"
              />
              <span v-else />
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          header-align="center"
          min-width="250"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.status === '1' || scope.row.status === '0'"
              type="primary"
              link
              size="small"
              @click="() => handleAction('changeStatus', scope.row)"
            >
              {{
                scope.row.status === '1'
                  ? '失效'
                  : scope.row.status === '0'
                    ? '生效'
                    : ''
              }}
            </el-button>
            <el-button
              v-if="scope.row.editAuth"
              type="primary"
              link
              size="small"
              @click="() => openEditModal('edit', scope.row)"
              >编辑</el-button
            >
            <el-button
              v-else
              type="primary"
              link
              size="small"
              @click="() => openEditModal('detail', scope.row)"
              >详情</el-button
            >
            <el-button
              v-if="
                scope.row.groupCommonStatus === '1' ||
                scope.row.groupCommonStatus === '0'
              "
              type="primary"
              link
              size="small"
              @click="() => handleAction('changeCommonStatus', scope.row)"
            >
              {{
                scope.row.groupCommonStatus === '1'
                  ? '取消集团共用'
                  : scope.row.groupCommonStatus === '0'
                    ? '集团共用'
                    : ''
              }}
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click="() => handleAction('reuse', scope.row)"
              >复用</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="float-right"
        background
        :current-page="formData.pageNo"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="formData.pageSize"
        :default-page-size="20"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog v-model="show" width="600px" title="选择客户添加">
      <el-form :inline="true" :rules="rules" :model="form">
        <el-form-item label="客户名称" prop="customerName">
          <customer-select
            v-model:value="form.customerName"
            @select="afterChangeCustomer"
          />
        </el-form-item>
      </el-form>
      <p v-if="current && current.customerName" class="info">
        <span>
          {{ current.customerNumber }}
        </span>
        <span>
          {{ current.customerName }}
        </span>
      </p>
      <template #footer>
        <p class="dialog-footer">
          <el-button
            :disabled="!current?.customerNumber"
            type="primary"
            @click="createCustomer"
            >添加</el-button
          >
        </p>
      </template>
    </el-dialog>
    <EditModal
      v-if="modalVisible"
      :type="modalType"
      :visible="modalVisible"
      :detail="modalDetail"
      :loading="modalLoading"
      @handle-ok="handleOk"
      @update:visible="updateModalVisible"
    />
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<style lang="scss" scoped>
.page-customer-maintainment {
  padding: 20px;

  .info {
    font-size: 16px;
    margin: 20px 0 0;
    padding: 10px;
    background-color: #eee;
    border-radius: 5px;

    span {
      margin-right: 10px;
    }
  }
  .el-table-customer {
    width: 100%;
    height: calc(100vh - 300px);
  }
  .el-form--inline {
    .el-form-item {
      // width: 100%;
    }
  }
  .el-dropdown {
    margin: 0 12px;
  }
}
</style>
