/* eslint-disable prettier/prettier */
import type { ListItem } from '@/constants/index';

export const status = [
  { label: '全部', value: '' },
  { label: '生效', value: '1' },
  { label: '失效', value: '0' },
];

export interface ColType {
  label: string;
  width?: number;
  align?: string;
  placeholder?: string;
  type?: string;
  rules?: {
    required: boolean;
    message: string;
  }[];
  enums?: {
    label: string;
    value: string;
  }[];
  disabled?: boolean;
}

export const listTableCols = [
  { prop: 'id', label: '客户物料关系代码', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'showCustomerName', label: '客户名称', width: 200, align: 'center', 'header-align': 'center' },
  { prop: 'zkhSkuNo', label: '震坤行物料号', width: 180, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialNo', label: '客户物料号', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialName', label: '客户物料名称', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialSpecification', label: '客户物料规格型号', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialUnit', label: '客户物料单位', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialStandardQuantity', label: '客户标准单位数量', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'zkhSkuStandardQuantity', label: '震坤行标准单位数量', width: 200, align: 'center', 'header-align': 'center' },
  { prop: 'dataSource', label: '来源', width: 100, align: 'center', 'header-align': 'center' },
  { prop: 'groupCommonStatus', label: '集团共用', width: 100, align: 'center', 'header-align': 'center' },
  { prop: 'status', label: '状态', width: 100, align: 'center', 'header-align': 'center' },
  // { prop: 'operations', label: '操作', width: 200, align: 'center', 'header-align': 'center', fixed: 'right' },
]

export const detailListTableCols = [
  { prop: 'id', label: '客户物料关系代码', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'showCustomerName', label: '客户名称', width: 200, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialNo', label: '客户物料号', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialName', label: '客户物料名称', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialSpecification', label: '客户物料规格型号', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialUnit', label: '客户物料单位', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'zkhSkuNo', label: '震坤行物料号', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'zkhSkuName', label: '震坤行物料名称', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'zkhSkuUnit', label: '震坤行物料单位', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'quantityRate', label: '客户标准单位数量/震坤行标准单位数量', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'dataSource', label: '来源', width: 100, align: 'center', 'header-align': 'center' },
  { prop: 'groupCommonStatus', label: '集团共用', width: 100, align: 'center', 'header-align': 'center' },
  { prop: 'status', label: '状态', width: 100, align: 'center', 'header-align': 'center' },
]

export const logColumns = [
  { prop: 'gmtCreate', label: '操作时间', width: 150, align: 'center', 'header-align': 'center' },
  { prop: 'operator', label: '操作人', width: 100, align: 'center', 'header-align': 'center' },
  { prop: 'relationId', label: '客户物料关系代码', width: 100, align: 'center', 'header-align': 'center' },
  { prop: 'zkhSkuNo', label: '震坤行物料号', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'customerMaterialNo', label: '客户物料号', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'updateColumnName', label: '变更字段', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'updateColumnChnName', label: '变更字段对应中文', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'originalValue', label: '变更前', width: 120, align: 'center', 'header-align': 'center' },
  { prop: 'updatedValue', label: '变更后', width: 120, align: 'center', 'header-align': 'center' },
]

export type CustomerProductItemPropKey = Exclude<keyof ListItem, 'status' | 'id' | 'editAuth' | 'ifProductOil'>;
export const customerProductItem: (ColType & {
  prop: CustomerProductItemPropKey;
})[] = [
  { label: '震坤行物料号', prop: 'zkhSkuNo', type: 'input', rules: [{ required: true, message: '震坤行物料号必填' }], placeholder: '请输入震坤行物料号，比如：AE3513325' },
  { label: '客户物料号', prop: 'customerMaterialNo', type: 'input', placeholder: '请输入客户物料号，比如：MT23-01' },
  { label: '客户物料名称', prop: 'customerMaterialName', type: 'input', placeholder: '请输入客户物料名称，比如：气源处理单元' },
  { label: '客户物料规格型号', prop: 'customerMaterialSpecification', type: 'input', placeholder: '请输入客户物料规格型号，比如：24V' },
  { label: '客户物料单位', prop: 'customerMaterialUnit', type: 'input', placeholder: '请输入客户物料单位，比如：个' },
  { label: '客户标准单位数量', prop: 'customerMaterialStandardQuantity', type: 'input', placeholder: '请输入客户标准单位数量，比如：1' },
  { label: '震坤行标准单位数量', prop: 'zkhSkuStandardQuantity', type: 'input', placeholder: '请输入震坤行标准单位数量，比如：3' },
  { label: '集团共用', prop: 'groupCommonStatus', type: 'select' , enums: [{label: '是', value: '1'}, {label: '否', value: '0'}] },
  { label: '来源', prop: 'dataSource', type: 'input', disabled: true },
];