<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue';
// import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { customerProductItem } from './option';
import type { ListItem } from '@/constants/index';

interface Props {
  type?: string; //'edit' | 'detail' | 'add';
  visible: boolean;
  detail?: ListItem;
  loading: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits(['handleOk', 'update:visible']);

const itemDetail = ref<ListItem>({} as ListItem);
const dialogVisible = ref<boolean>(false);

// watch(
//   () => props.visible,
//   (val) => {
//     dialogVisible.value = val;
//   }
// );

const title = computed(() => {
  let t = '';
  switch (props.type) {
    case 'edit':
      t = '编辑物料关系';
      break;
    case 'detail':
      t = '物料关系详情';
      break;
    case 'add':
      t = '新增物料关系';
      break;
    default:
      break;
  }
  return t;
});

const editDisabled = computed(() => (item: any) => {
  if (item.disabled) return true;
  if (props.type === 'detail') return true;
  if (
    props.detail?.ifProductOil &&
    (item.prop === 'customerMaterialUnit' ||
      item.prop === 'customerMaterialStandardQuantity' ||
      item.prop === 'zkhSkuStandardQuantity')
  ) {
    return true;
  }
  return false;
});

onMounted(() => {
  // itemDetail.value =
  //   JSON.stringify(props.detail) === '{}'
  //     ? ({ dataSource: 'BOSS', groupCommonStatus: '0' } as ListItem)
  //     : JSON.parse(JSON.stringify(props.detail!));
  if (JSON.stringify(props.detail) === '{}') {
    itemDetail.value = {
      dataSource: 'BOSS',
      groupCommonStatus: '0',
    } as ListItem;
  } else if (props.detail?.ifProductOil) {
    itemDetail.value = {
      ...JSON.parse(JSON.stringify(props.detail!)),
      customerMaterialUnit: null,
      customerMaterialStandardQuantity: null,
      zkhSkuStandardQuantity: null,
    };
  } else {
    itemDetail.value = JSON.parse(JSON.stringify(props.detail!));
  }
  nextTick(() => {
    dialogVisible.value = true;
  });
});

const handleSubmit = () => {
  if (props.type === 'edit' || props.type === 'add') {
    const {
      zkhSkuNo,
      customerMaterialUnit,
      customerMaterialStandardQuantity,
      zkhSkuStandardQuantity,
    } = itemDetail.value;
    if (!zkhSkuNo) return ElMessage.error('震坤行物料号必填');
    if (
      !(
        (customerMaterialUnit &&
          customerMaterialStandardQuantity &&
          zkhSkuStandardQuantity) ||
        (!customerMaterialUnit &&
          !customerMaterialStandardQuantity &&
          !zkhSkuStandardQuantity)
      )
    )
      return ElMessage.error(
        '客户物料单位、客户标准单位数量、震坤行标准单位数量必须同时填写或同时不填'
      );
  }
  emits('handleOk', itemDetail.value);
};

const handleCancel = () => {
  emits('update:visible', false);
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :destroy-on-close="true"
    :before-close="handleCancel"
    top="120px"
    width="600px"
    class="edit-modal"
  >
    <el-form :label-width="150" label-suffix=":" :model="itemDetail">
      <el-form-item
        v-for="item in customerProductItem"
        :key="item.prop"
        :label="item.label"
        :rules="item.rules"
      >
        <el-input
          v-if="item.type === 'input'"
          v-model="itemDetail[item.prop]"
          class="w-300px"
          :disabled="editDisabled(item)"
          :placeholder="item.placeholder || '请选择'"
        />
        <el-select
          v-if="item.type === 'select'"
          v-model="itemDetail[item.prop]"
          :disabled="props.type === 'detail'"
          class="w-300px"
          :placeholder="item.placeholder || '请选择'"
        >
          <el-option
            v-for="option in item.enums"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联单号">
        <el-input
          v-model="itemDetail.referenceNo"
          class="w-300px"
          disabled
          placeholder=""
        />
      </el-form-item>
      <el-form-item label="关联行号">
        <el-input
          v-model="itemDetail.referenceItemNo"
          class="w-300px"
          disabled
          placeholder=""
        />
      </el-form-item>
    </el-form>
    <span v-if="!itemDetail.ifProductOil" class="tips"
      >客户物料单位、客户标准单位数量、震坤行标准单位数量三个字段同时填写才有意义</span
    >
    <span v-else class="tips"
      >成品油不允许修改客户物料单位、客户标准单位数量、震坤行标准单位数量</span
    >
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          :loading="props.loading"
          type="primary"
          @click="handleSubmit"
        >
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
// .edit-modal {
//   :deep(.el-input) {
//     width: 300px;
//   }

//   :deep(.el-select) {
//     width: 300px;
//   }
// }
.tips {
  font-weight: bold;
  color: red;
}
</style>
