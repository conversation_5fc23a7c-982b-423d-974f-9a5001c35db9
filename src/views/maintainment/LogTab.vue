<script setup lang="ts">
import { useRoute } from 'vue-router';
import { onMounted, ref, watch } from 'vue';
import { ElMessage, type TableColumnCtx } from 'element-plus';
// import type { TableColumnCtx } from 'element-plus';

import { getLog, searchClients } from '@/api/maintainment';
import { logColumns } from './option';

interface SearchForm {
  customerCode: string;
  customerMaterialNo: string;
  idList?: any;
  skuList?: any;
  pageNo: number;
  pageSize: number;
}

interface ListItem {
  gmtCreate: string;
  operator: string;
  relationId: number;
  zkhSkuNo: string;
  customerMaterialNo: string;
  updateColumnName: string;
  updateColumnChnName: string;
  originalValue: string;
  updatedValue: string;
}

interface SpanMethodProps {
  row: ListItem;
  column: TableColumnCtx<ListItem>;
  rowIndex: number;
  columnIndex: number;
}

const route = useRoute();
const formData = ref<SearchForm>({
  customerCode: '',
  customerMaterialNo: '',
  // idList: '',
  // skuList: '',
  pageNo: 1,
  pageSize: 20,
});
const customerDetail = ref<any>({});
const loading = ref<boolean>(false);
const tableData = ref<ListItem[]>([]);
const total = ref<number>(0);

watch(
  formData,
  (cur) => {
    console.log('formdata.change', cur);
    localStorage.setItem(
      `${cur.customerCode}-log-search-options`,
      JSON.stringify(cur)
    );
  },
  { deep: true }
);

onMounted(() => {
  const id = route.params.id as string;
  formData.value.customerCode = id;
  const options = localStorage.getItem(`${id}-log-search-options`);
  console.log('localStorage-log', options);
  if (options) {
    formData.value = JSON.parse(options);
  }
  search();
  getCustomerDetail(id);
});

const handleSearch = () => {
  // 点击查询按钮时将页面重置到第一页
  formData.value.pageNo = 1;
  search();
};
const search = () => {
  const id = route.params.id as string;
  loading.value = true;
  const params = { ...formData.value, customerCode: id };
  params.skuList = params.skuList ? params.skuList.split(' ') : [];
  params.idList = params.idList ? params.idList.split(' ') : [];
  getLog(params)
    .then((res: any) => {
      console.log(res);
      if (res.code === 200 && res.data) {
        tableData.value = res.data;
        total.value = res.total;
      }
    })
    .catch((error: any) => {
      console.log(error);
    })
    .finally(() => {
      loading.value = false;
    });
};

const resetSearch = () => {
  formData.value = {
    customerCode: route.params.id as string,
    customerMaterialNo: '',
    // idList: '',
    // skuList: '',
    pageNo: 1,
    pageSize: 20,
  };
};

const handleSizeChange = (res: number) => {
  formData.value.pageSize = res;
  search();
};
const handleCurrentChange = (res: number) => {
  formData.value.pageNo = res;
  search();
};

const getCustomerDetail = (query: string) => {
  if (query) {
    searchClients(query)
      .then((res: any) => {
        if (res.code === 200 && res.data) {
          customerDetail.value = res.data[0];
        }
      })
      .catch((error: any) => {
        ElMessage.error(error);
      });
  }
};

const spanMethod = () => {
  const mergingRows = (tableData: ListItem[]) => {
    // 表格数据列合并预处理,生成一个与行数相同的数组记录每一行设置的合并数
    // 如果是第一条记录（索引为0），向数组中加入1，并设置索引位置；
    // 如果不是第一条记录，则判断它与前一条记录是否相等，如果相等，
    // 则向mergingRows中添入元素0，并将前一位元素+1，表示合并行数+1，
    // 以此往复，得到所有行的合并数，0即表示该行不显示。
    const mergingRows = [];
    let mergingPos = 0;
    for (let i = 0; i < tableData.length; i++) {
      // tabledata 表格数据源
      if (i === 0) {
        mergingRows.push(1);
        mergingPos = 0;
      } else {
        const {
          gmtCreate,
          operator,
          relationId,
          zkhSkuNo,
          customerMaterialNo,
        } = tableData[i];
        const {
          gmtCreate: gc,
          operator: op,
          relationId: re,
          zkhSkuNo: zkh,
          customerMaterialNo: cn,
        } = tableData[i - 1];
        if (
          gmtCreate === gc &&
          operator === op &&
          relationId === re &&
          zkhSkuNo === zkh &&
          customerMaterialNo === cn
        ) {
          mergingRows[mergingPos] += 1;
          mergingRows.push(0);
        } else {
          mergingRows.push(1);
          mergingPos = i;
        }
      }
    }
    return mergingRows;
  };
  const data = mergingRows(tableData.value);
  return ({ rowIndex, columnIndex }: SpanMethodProps) => {
    if (columnIndex < 5) {
      // 第一列
      const _row = data[rowIndex];
      const _col = _row > 0 ? 1 : 0;
      return {
        rowspan: _row,
        colspan: _col,
      };
    }
  };
};
</script>

<template>
  <div class="page-customer-maintainment">
    <div>
      <el-form label-position="right" label-width="130px" label-suffix=":">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户名称">
              {{ customerDetail.customerName || '' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编号">
              {{ route.params.id }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="震坤行物料号">
              <el-input
                v-model="formData.skuList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料号">
              <el-input
                v-model="formData.customerMaterialNo"
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="客户物料关系代码">
              <el-input
                v-model="formData.idList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
          <el-col :offset="12" :span="6">
            <span class="float-right">
              <el-button :loading="loading" type="primary" @click="handleSearch"
                >查询</el-button
              >
              <el-button @click="resetSearch">重置</el-button>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="module-list">
      <el-table
        v-loading="loading"
        :span-method="spanMethod()"
        show-overflow-tooltip
        :data="tableData"
        class="log-table"
        border
      >
        <el-table-column
          v-for="col in logColumns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :align="col.align"
          header-align="center"
        />
      </el-table>
      <el-pagination
        class="float-right"
        background
        :current-page="formData.pageNo"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="formData.pageSize"
        :default-page-size="20"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<style lang="scss" scoped>
.page-customer-maintainment {
  padding: 20px;
}

.log-table {
  width: 100%;
  height: calc(100vh - 250px);
}
</style>
