<!--
 * @Author: luozhikai
 * @Date: 2023-07-14 10:43:13
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-08-15 20:02:26
 * @Description: file content
-->
<script setup lang="ts">
import { onBeforeMount, ref } from 'vue';
import { useRoute } from 'vue-router';
import DetailTab from './DetailTab.vue';
import LogTab from './LogTab.vue';
import type { TabPaneName } from 'element-plus';

const activeTab = ref<string>('detail');
const route = useRoute();

const handleChange = (name: TabPaneName) => {
  activeTab.value = name as string;
  const id = route.query.id;
  localStorage.setItem(`${id}-detail-page-active-tab`, name as string);
};

onBeforeMount(() => {
  const id = route.query.id;
  const tab = localStorage.getItem(`${id}-detail-page-active-tab`);
  if (tab) {
    activeTab.value = tab;
  }
});
</script>

<template>
  <el-tabs v-model="activeTab" type="border-card" @tab-change="handleChange">
    <el-tab-pane name="detail" label="客户物料关系详情"
      ><DetailTab
    /></el-tab-pane>
    <el-tab-pane name="log" label="日志"><LogTab /></el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped>
.el-tabs--border-card {
  border-bottom: 0;
}
</style>
