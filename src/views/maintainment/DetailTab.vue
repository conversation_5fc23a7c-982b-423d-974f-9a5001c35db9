<!--
 * @Author: luozhikai
 * @Date: 2023-07-21 16:01:07
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-08-22 16:37:06
 * @Description: file content
-->
<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import {
  addCustomerProduct,
  batchOperation,
  editCustomerProduct,
  getEnumData,
  getImportTemplate,
  getList,
  searchClients,
} from '@/api/maintainment';
import { useCommonStore } from '@/stores/common';
import EditModal from './EditModal.vue';
import { detailListTableCols } from './option';
import type { FormData, ListItem } from '@/constants/index';

const commonStore = useCommonStore();
const acceptFileType = computed(() => commonStore.acceptFileType || {});

const router = useRouter();
const route = useRoute();

const loading = ref<boolean>(false);
const exportLoading = ref<boolean>(false);
const batchLoading = ref<boolean>(false);
const customerDetail = ref<any>({});
const formData = ref<FormData>({
  customerCode: route.params.id,
  groupCommonStatus: '0,1',
  status: '0,1',
  pageNo: 1,
  pageSize: 20,
});
const tableData = ref<ListItem[]>([]);
const total = ref<number>(0);
const enumData = ref<any>({
  groupCommon: [],
  dataStatus: [],
  dataSource: [],
});
const modalType = ref<string>('edit');
const modalVisible = ref<boolean>(false);
const modalLoading = ref<boolean>(false);
const modalDetail = ref<ListItem>({} as ListItem);
const selectedRows = ref<ListItem[]>([]);

const getRate = computed(() => (val: ListItem) => {
  if (!val.customerMaterialStandardQuantity && !val.zkhSkuStandardQuantity)
    return '';
  return `${val.customerMaterialStandardQuantity}:${val.zkhSkuStandardQuantity}`;
  // if (!val.customerMaterialStandardQuantity || !val.zkhSkuStandardQuantity) return '';
  // const n = max_gy(val.customerMaterialStandardQuantity, val.zkhSkuStandardQuantity);
  // return `${val.customerMaterialStandardQuantity / n}:${val.zkhSkuStandardQuantity / n}`;
});

watch(
  formData,
  (cur) => {
    console.log('formdata.change', cur);
    localStorage.setItem(
      `${cur.customerCode}-detail-search-options`,
      JSON.stringify(cur)
    );
  },
  { deep: true }
);
const openEditModal = (val: any, row: ListItem) => {
  modalVisible.value = true;
  modalType.value = val;
  modalDetail.value = row;
};

const handleSelect = (sr: any) => {
  console.log(sr);
  selectedRows.value = [...sr];
};

const handleAction = (type: string, row: ListItem) => {
  console.log(type, row);
  let commitType = '';
  if (type === 'changeStatus') {
    commitType = row.status === '1' ? '3' : '2';
  }
  if (type === 'changeCommonStatus') {
    commitType = row.groupCommonStatus === '1' ? '6' : '5';
  }
  if (type === 'reuse') {
    if (!formData.value.customerCode)
      return ElMessage.error('请选择客户编号后再复用');
    const reuseRow: ListItem = {
      ...row,
      dataSource: 'BOSS',
    };
    openEditModal('add', reuseRow);
  }
  if (!commitType) return;
  commitAction(commitType, [row]);
};

const handleBatchAciton = (type: string) => {
  console.log(type, selectedRows.value);
  commitAction(type, selectedRows.value);
};

const commitAction = (type: string, data: any[]) => {
  console.log(type, data);
  let params: any = {};
  params.operateType = type;
  params.idList = data.map((d) => d.id);
  if (type === '1') {
    // 复用
    params.targetCustomerCode = route.params.id;
  }
  if (type === '2') {
    // 生效
    params.changedStatus = '1';
  }
  if (type === '3') {
    // 失效
    params.changedStatus = '0';
  }
  if (type === '4') {
    // 删除
  }
  if (type === '5') {
    // 集团共用
    params.changedGroupCommon = '1';
  }
  if (type === '6') {
    // 取消集团共用
    params.changedGroupCommon = '0';
  }
  if (type === '7') {
    // 导出,使用当前查询条件作为参数
    exportLoading.value = true;
    const { pageNo, pageSize, ...others } = formData.value;
    params = {
      operateType: type,
      ...others,
    };
    params.skuList = params.skuList ? params.skuList.split(' ') : [];
    params.customerMaterialNoList = params.customerMaterialNoList
      ? params.customerMaterialNoList.trim().split(' ')
      : [];
    params.idList =
      data.length > 0
        ? data.map((d) => d.id)
        : params.idList
          ? params.idList.split(' ')
          : [];
  } else {
    batchLoading.value = true;
  }
  batchOperation(params)
    .then((res: any) => {
      if (res.code === 200) {
        search();
        if (type === '7') {
          ElMessage.success(res.data);
          if (window.self !== window.top) {
            window?.top?.postMessage(
              { source: 'sr', type: 'detail-download' },
              '*'
            );
          }
        } else {
          ElMessage.success(res.msg);
        }
      } else {
        ElMessage.error({
          message: formatMsg(res),
          duration: 10000,
          showClose: true,
        });
      }
    })
    .finally(() => {
      exportLoading.value = false;
      batchLoading.value = false;
    });
};

const getTemplate = () => {
  getImportTemplate().then((res: any) => {
    if (res.code === 200) {
      window.open(res.data);
    } else {
      ElMessage.error(formatMsg(res));
    }
  });
};

const handleOk = (data: any) => {
  console.log(data);
  const d: any = { ...data };
  d.customerCode = route.params.id;
  d.customerName = customerDetail.value.customerName;
  if (modalType.value === 'edit') {
    // 编辑
    modalLoading.value = true;
    editCustomerProduct(d)
      .then((res: any) => {
        if (res.code === 200) {
          ElMessage.success('编辑成功');
          search();
          modalVisible.value = false;
        } else {
          ElMessage.error(formatMsg(res));
        }
      })
      .finally(() => {
        modalLoading.value = false;
      });
  } else if (modalType.value === 'add') {
    modalLoading.value = true;
    addCustomerProduct(d)
      .then((res: any) => {
        if (res.code === 200) {
          ElMessage.success('新增成功');
          modalVisible.value = false;
          search();
        } else {
          ElMessage.error(formatMsg(res));
        }
      })
      .finally(() => {
        modalLoading.value = false;
      });
  } else {
    // 详情
    modalVisible.value = false;
  }
};

const formatMsg = (res: any): string => {
  if (res.data) return `${res.msg}:${res.data}`;
  return res.msg;
};

const updateModalVisible = (val: any) => {
  modalVisible.value = val;
};

const routeToDetail = (id: string) => {
  if (window.self === window.top) {
    router.push(`maintainment/detail/${id}`);
  } else {
    window?.top?.postMessage({ source: 'sr', id }, '*');
  }
};

const handleSearch = () => {
  formData.value.pageNo = 1;
  search();
};
const search = () => {
  // formData.value.customerCode = formData.value.customerCode || null;
  const id = route.params.id as string;
  loading.value = true;
  const params = { ...formData.value, customerCode: id };
  params.skuList = params.skuList ? params.skuList.trim().split(' ') : [];
  params.idList = params.idList ? params.idList.trim().split(' ') : [];
  params.customerMaterialNoList = params.customerMaterialNoList
    ? params.customerMaterialNoList.trim().split(' ')
    : [];
  Object.keys(params).forEach((key: string) => {
    if (typeof params[key as keyof FormData] === 'string') {
      params[key as keyof FormData] = params[key as keyof FormData].trim();
    }
  });
  getList(params)
    .then((res: any) => {
      console.log(res);

      if (res.code === 200 && res.data) {
        tableData.value = res.data;
        total.value = res.total;
      }
    })
    .catch((error: any) => {
      console.log(error);
    })
    .finally(() => {
      loading.value = false;
    });
};

const resetSearch = () => {
  formData.value = {
    customerCode: route.params.id,
    groupCommonStatus: '0,1',
    status: '0,1',
    pageNo: 1,
    pageSize: 20,
  };
};

const getEnum = () => {
  getEnumData().then((res: any) => {
    console.log(res);

    if (res.code === 200 && res.data) {
      enumData.value = res.data;
    }
  });
};

const onUploadSuccess = (res: any) => {
  if (res.code === 200) {
    ElMessage.success('上传完成');
    // Attention: 数据插入有延迟
    setTimeout(() => {
      search();
    }, 1500);
  } else if (res.data?.resultExcelUrl) {
    ElMessageBox.confirm('点击下载失败表格', '上传失败', {
      distinguishCancelAndClose: true,
      confirmButtonText: '下载',
      cancelButtonText: '关闭',
      closeOnClickModal: false,
      type: 'error',
    }).then(() => {
      window.open(res.data.resultExcelUrl);
    });
  } else {
    ElMessage.error(res.msg);
  }

  // ElMessage.error(res.msg);
  // setTimeout(() => {
  //   window.open(res.data.resultExcelUrl);
  // }, 1000);
};

const handleSizeChange = (res: number) => {
  formData.value.pageSize = res;
  search();
};
const handleCurrentChange = (res: number) => {
  formData.value.pageNo = res;
  search();
};

const getCustomerDetail = (query: string) => {
  if (query) {
    searchClients(query)
      .then((res: any) => {
        if (res.code === 200 && res.data) {
          customerDetail.value = res.data[0];
        }
      })
      .catch((error: any) => {
        ElMessage.error(error);
      });
  }
};
onBeforeMount(() => {
  const id = route.params.id as string;
  formData.value.customerCode = id;
  const options = localStorage.getItem(`${id}-detail-search-options`);
  console.log('localStorage-detail', options);
  if (options) {
    formData.value = JSON.parse(options);
  }
  search();
  getEnum();
  getCustomerDetail(id);
});
</script>

<template>
  <div class="app-container page-customer-maintainment">
    <div class="module-formData">
      <el-form label-position="right" label-width="130px" label-suffix=":">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户名称">
              {{ customerDetail.customerName || '' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编号">
              {{ route.params.id }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="震坤行物料号">
              <el-input
                v-model="formData.skuList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料关系代码">
              <el-input
                v-model="formData.idList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户物料号">
              <el-input
                v-model="formData.customerMaterialNoList"
                clearable
                placeholder="多条以空格间隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料名称">
              <el-input
                v-model="formData.customerMaterialName"
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户物料规格型号">
              <el-input
                v-model="formData.customerMaterialSpecification"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="集团共用">
              <el-select
                v-model="formData.groupCommonStatus"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in enumData.groupCommon"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.dataSourceList"
                multiple
                clearable
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in enumData.dataSource"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select
                v-model="formData.status"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in enumData.dataStatus"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :offset="6" :span="6">
            <span class="float-right">
              <el-button :loading="loading" type="primary" @click="handleSearch"
                >查询</el-button
              >
              <el-button @click="resetSearch">重置</el-button>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="module-list">
      <span class="module-btns">
        <el-tooltip
          placement="top-start"
          content="模板只限于批量新增,批量修改模板请直接使用导出的模板进行修政"
        >
          <el-button type="primary" @click="getTemplate">获取模板</el-button>
        </el-tooltip>
        <el-dropdown>
          <el-button type="primary"
            >导入<el-icon class="el-icon--right"><arrow-down /></el-icon
          ></el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="insert">
                <el-upload
                  action="/api-customer-product/customerProduct/relation/batchInsert/customerMaterialRelation"
                  :on-success="onUploadSuccess"
                  :accept="acceptFileType.soCommonType"
                  :show-file-list="false"
                >
                  批量新增
                </el-upload>
              </el-dropdown-item>
              <el-dropdown-item command="insert">
                <el-upload
                  action="/api-customer-product/customerProduct/relation/batchUpdate/customerMaterialRelation"
                  :on-success="onUploadSuccess"
                  :accept="acceptFileType.soCommonType"
                  :show-file-list="false"
                >
                  批量修改
                </el-upload>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          :loading="exportLoading"
          type="primary"
          @click="() => handleBatchAciton('7')"
          >导出</el-button
        >
        <el-dropdown @command="handleBatchAciton">
          <el-button
            :loading="batchLoading"
            :disabled="selectedRows.length === 0"
            type="primary"
          >
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="1">复用</el-dropdown-item>
              <el-dropdown-item command="2">生效</el-dropdown-item>
              <el-dropdown-item command="3">失效</el-dropdown-item>
              <!-- <el-dropdown-item command="4">删除</el-dropdown-item> -->
              <el-dropdown-item command="5">集团共用</el-dropdown-item>
              <el-dropdown-item command="6">取消集团共用</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          @click="() => openEditModal('add', {} as ListItem)"
          >新增</el-button
        >
      </span>

      <el-table
        v-loading="loading"
        show-overflow-tooltip
        :data="tableData"
        class="detail-el-table"
        @selection-change="handleSelect"
        @select-all="handleSelect"
      >
        <el-table-column type="selection" width="55" fixed />
        <el-table-column
          v-for="col in detailListTableCols"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :align="col.align"
          :width="col.width"
          header-align="center"
        >
          <template #default="scope">
            <!-- <router-link
                v-if="col.prop === 'customerName'"
                :to="{ path: `maintainment/${scope.row.id}` }"
                :title="scope.row.customerName"
              >
                {{ scope.row.customerName }}
              </router-link> -->
            <template v-if="col.prop === 'showCustomerName'">
              <el-button
                type="primary"
                link
                @click="() => routeToDetail(scope.row.showCustomerCode)"
                >{{ scope.row.showCustomerName }}</el-button
              >
            </template>
            <template v-if="col.prop === 'quantityRate'">
              {{ getRate(scope.row) }}
            </template>
            <template v-if="col.prop === 'zkhSkuNo'">
              <span
                >{{ scope.row.zkhSkuNo }}
                <span v-if="scope.row.ifProductOil"
                  >&nbsp;<el-tag type="danger" size="small"
                    >成品油</el-tag
                  ></span
                ></span
              >
            </template>
            <template v-if="col.prop === 'groupCommonStatus'">
              {{
                scope.row.groupCommonStatus === '1'
                  ? '是'
                  : scope.row.groupCommonStatus === '0'
                    ? '否'
                    : ''
              }}
            </template>
            <template v-if="col.prop === 'status'">
              <i
                v-if="scope.row.status === '1'"
                class="status-icon status-on"
              />
              <i
                v-else-if="scope.row.status === '0'"
                class="status-icon status-off"
              />
              <span v-else />
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          header-align="center"
          min-width="250"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.status === '1' || scope.row.status === '0'"
              type="primary"
              link
              size="small"
              @click="() => handleAction('changeStatus', scope.row)"
            >
              {{
                scope.row.status === '1'
                  ? '失效'
                  : scope.row.status === '0'
                    ? '生效'
                    : ''
              }}
            </el-button>
            <el-button
              v-if="scope.row.editAuth"
              type="primary"
              link
              size="small"
              @click="() => openEditModal('edit', scope.row)"
              >编辑</el-button
            >
            <el-button
              v-else
              type="primary"
              link
              size="small"
              @click="() => openEditModal('detail', scope.row)"
              >详情</el-button
            >
            <el-button
              v-if="
                scope.row.groupCommonStatus === '1' ||
                scope.row.groupCommonStatus === '0'
              "
              type="primary"
              link
              size="small"
              @click="() => handleAction('changeCommonStatus', scope.row)"
            >
              {{
                scope.row.groupCommonStatus === '1'
                  ? '取消集团共用'
                  : scope.row.groupCommonStatus === '0'
                    ? '集团共用'
                    : ''
              }}
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click="() => handleAction('reuse', scope.row)"
              >复用</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="float-right"
        background
        :current-page="formData.pageNo"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="formData.pageSize"
        :default-page-size="20"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <EditModal
      v-if="modalVisible"
      :type="modalType"
      :visible="modalVisible"
      :detail="modalDetail"
      :loading="modalLoading"
      @handle-ok="handleOk"
      @update:visible="updateModalVisible"
    />
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<style lang="scss" scoped>
.page-customer-maintainment {
  padding: 20px;
  .detail-el-table {
    width: 100%;
    height: calc(100vh - 350px);
  }
  .info {
    font-size: 16px;
    margin: 20px 0 0;
    padding: 10px;
    background-color: #eee;
    border-radius: 5px;

    span {
      margin-right: 10px;
    }
  }
  .el-form--inline {
    .el-form-item {
      // width: 100%;
    }
  }
  .el-dropdown {
    margin: 0 12px;
  }
}
</style>
