<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue';
import { fetchEventSource } from '@microsoft/fetch-event-source';

const API_KEY = 'app-K8xH5fEAao5LRjM7LF3ApIaT';

const inputText = ref('');
const messages = ref<{ sender: string; type: string; content: string }[]>([]);
const chatBox = ref<HTMLDivElement | null>(null);

const displayTypingEffect = (fullText: string, messageIndex: number) => {
  let index = 0;
  const typingInterval = setInterval(() => {
    if (index < fullText.length) {
      // 逐字更新内容
      messages.value[messageIndex].content = fullText.slice(0, index + 1);
      index++;
      scrollToBottom();
    } else {
      clearInterval(typingInterval); // 完成后清除定时器
    }
  }, 100);
};

const getRealtimeMessage = () => {
  try {
    let aiResponse = ''; // 用于累积 AI 的回答
    let aiMessageIndex = -1; // 用于记录 AI 消息的索引
    const ctrlAbout = new AbortController();
    fetchEventSource('/api-agent/v1/chat-messages', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: {},
        query: inputText.value,
        response_mode: 'streaming',
        conversation_id: '',
        user: 'user',
      }),
      signal: ctrlAbout.signal,
      openWhenHidden: true, // 在浏览器标签页隐藏时保持与服务器的EventSource连接
      onmessage: (event) => {
        // 处理流式数据
        const data = JSON.parse(event.data);
        console.log('data', data.answer, Date.now());
        if (data.answer) {
          aiResponse += data.answer;

          // 如果还没有 AI 消息，添加一条新消息
          if (aiMessageIndex === -1) {
            aiMessageIndex = messages.value.length;
            messages.value.push({
              sender: 'AI',
              type: 'text',
              content: '',
            });
          }

          // 逐字更新显示内容
          displayTypingEffect(aiResponse, aiMessageIndex);

          scrollToBottom();
        }
      },
      onerror: (error) => {
        console.error('Error in fetchEventSource:', error);
        messages.value.push({
          sender: 'AI',
          type: 'text',
          content: 'Error occurred while fetching data.',
        });
        scrollToBottom();
      },
      onclose: () => {
        // 关闭流
        console.log('onclose');
      },
    });
  } catch (error) {
    console.error('Failed to send message:', error);
  } finally {
    // 清空输入框
    inputText.value = '';
  }
};

const sendMessage = () => {
  if (inputText.value.trim()) {
    messages.value.push({
      sender: 'You',
      type: 'text',
      content: inputText.value,
    });
    getRealtimeMessage();
  }
};

const scrollToBottom = () => {
  nextTick(() => {
    if (chatBox.value) {
      chatBox.value.scrollTop = chatBox.value.scrollHeight;
    }
  });
};

onMounted(() => {
  if (inputText.value.trim()) {
    getRealtimeMessage();
  }
});
</script>

<template>
  <div class="chat-container">
    <div ref="chatBox" class="chat-box">
      <div v-for="(message, index) in messages" :key="index" class="message">
        <strong>{{ message.sender }}:</strong>
        <span v-if="message.type === 'text'">{{ message.content }}</span>
      </div>
    </div>
    <div class="input-area">
      <input
        v-model="inputText"
        placeholder="Type your message..."
        @keyup.enter="sendMessage"
      />
      <button @click="sendMessage">Send</button>
    </div>
  </div>
</template>

<style scoped>
.chat-container {
  width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

.chat-box {
  height: 500px;
  overflow-y: scroll;
  border: 1px solid #eee;
  padding: 10px;
  margin-bottom: 10px;
}

.message {
  margin-bottom: 10px;
}

.chat-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.input-area {
  display: flex;
  gap: 10px;
}

input[type='text'] {
  flex: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}
</style>
