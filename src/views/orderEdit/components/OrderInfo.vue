<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { computed } from 'vue';
import { useCommonStore } from '@/stores/common';
import type { EditOrder } from '@/types/edit';

const commonStore = useCommonStore();

const { dictList } = storeToRefs(commonStore);

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const orderSourceOptions = computed(() => {
  return (
    dictList.value.orderSource?.filter(
      (item) => item.status === 'normal' && item.supportQuery === '1'
    ) || []
  );
});
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-form-item label="外围订单号" prop="orderNo">
        <el-input
          v-model="data.orderNo"
          maxlength="50"
          placeholder="请输入外围订单号"
          clearable
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="客户订单号" prop="customerReferenceNo">
        <el-input
          v-model="data.customerReferenceNo"
          placeholder="请输入客户订单号"
          maxlength="35"
          clearable
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-form-item label="订单依据" prop="orderBasis">
        <el-input v-model="data.orderBasis" disabled />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="订单依据关联单号" prop="orderBasis">
        <el-input v-model="data.orderBasis" disabled />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-form-item label="订单来源" prop="orderSource">
        <el-select
          v-model="data.orderSource"
          placeholder="请选择订单来源"
          style="width: 100%"
          :disabled="data.orderSource === 'HQZY'"
          clearable
        >
          <el-option
            v-for="item in orderSourceOptions"
            :key="item.code"
            :disabled="item.code === 'HQZY'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="草稿订单号" prop="sketchOrderNo">
        <el-input v-model="data.sketchOrderNo" disabled />
      </el-form-item>
    </el-col>
  </el-row>
</template>
