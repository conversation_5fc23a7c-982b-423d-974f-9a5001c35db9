<script setup lang="ts">
import { Ref, computed, inject, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import { CustomerData } from '@/types/customer';
import { queryPaymentTerms } from '@/api/order';
import { DetailOrder } from '@/types/detail';
import { SalesDictType } from '@/types/common';
import { parseDict } from '../utils';
import InvoiceReceiverDialog, {
  type ReceiverForm,
} from './InvoiceReceiverDialog.vue';
import type { EditOrder } from '@/types/edit';

const commonStore = useCommonStore();
const { dictList, rolesInfoList } = storeToRefs(commonStore);

const emit = defineEmits(['update', 'close']);

const props = defineProps<{
  data: EditOrder;
  customerData: CustomerData | null;
}>();

const tempData = props.data ? { ...props.data } : null;
const orderData = ref<EditOrder | null>(tempData);

const detailData = inject<Ref<DetailOrder>>('orderData');

const paymentTermsOptions = ref<SalesDictType[]>([]);

const salesGroupName = computed(() => {
  if (orderData.value && dictList.value) {
    const { salesGroup } = detailData?.value || {};
    if (dictList.value.salesGroup) {
      const group = dictList.value.salesGroup.find(
        (item: SalesDictType) => salesGroup === item.parentCode
      );
      if (group) {
        return group.name;
      }
    }
  }
  return '';
});
const salesOfficeName = computed(() => {
  if (orderData.value && dictList.value) {
    const { salesOffice } = detailData?.value || {};
    if (dictList.value.salesOffice) {
      const office = dictList.value.salesOffice.find(
        (item: SalesDictType) => salesOffice === item.parentCode
      );
      if (office) {
        return office.name;
      }
    }
  }
  return '';
});

const queryPaymentTermsList = () => {
  const { salesOrganization, distributionChannel } = detailData?.value || {};
  const productGroupArr = orderData.value?.items
    ?.map((item) => item.productGroup)
    ?.filter((item) => !!item);
  const productGroups = [...new Set(productGroupArr)]?.join(',') || '';
  const params = {
    customerCode: orderData.value?.invoiceReceiver,
    salesOrganization,
    distributionChannel,
    productGroups,
  };
  queryPaymentTerms(params)
    .then((res) => {
      if (res && res.code === 200) {
        paymentTermsOptions.value = res.data;
      } else {
        paymentTermsOptions.value = [];
      }
    })
    .catch((error) => console.log(error));
};

const formRef = ref();
const rules = {
  paymentTerm: [{ required: true, message: '请选择付款条件', trigger: 'blur' }],
};
const disabledInvoiceReceiver = computed(
  () =>
    detailData?.value?.hasDelivered === 'B' ||
    detailData?.value?.hasDelivered === 'C'
);

const showInvoiceReceiverDialog = ref(false);

const sumitInvoiceReceiver = (receiverData: ReceiverForm) => {
  if (orderData.value) {
    orderData.value.receivingInvoice = receiverData.receivingInvoice;
    orderData.value.invoiceReceiver = receiverData.invoiceReceiver;
    orderData.value.invoiceReceiverName = receiverData.invoiceReceiverName;
    orderData.value.receiverDocMetaData = receiverData.docMetaData;
  }
};

const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      emit('update', orderData.value);
      emit('close');
    }
  });
};
const handleCancel = () => {
  emit('close');
};

onMounted(() => {
  queryPaymentTermsList();
});
</script>

<template>
  <el-form
    v-if="orderData"
    ref="formRef"
    :model="orderData"
    :rules="rules"
    label-width="120px"
    label-position="left"
    class="p-10px"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="售达方">
          <el-input
            :value="detailData?.customerName || ''"
            placeholder="售达方"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="送达方">
          <el-input
            :value="detailData?.customerName || ''"
            placeholder="请输入送达方"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收票方">
          <div class="flex justify-between w-100%">
            <el-input
              v-if="orderData"
              v-model="orderData.invoiceReceiverName"
              disabled
            />
            <el-button
              type="primary"
              class="ml-10px"
              :disabled="disabledInvoiceReceiver"
              @click="showInvoiceReceiverDialog = true"
              >修改</el-button
            >
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="付款方">
          <el-input
            :value="orderData?.invoiceReceiverName"
            placeholder="请输入付款方"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户来源">
          <el-input
            :value="customerData?.customerSourceName"
            placeholder="请输入客户来源"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="付款条件" prop="paymentTerm">
          <el-select
            v-if="orderData"
            v-model="orderData.paymentTerm"
            placeholder="请选择付款条件"
            :disabled="!rolesInfoList?.includes('boss-订单信用角色')"
          >
            <el-option
              v-for="item in paymentTermsOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="自营配送">
          <el-select
            v-if="orderData"
            v-model="orderData.serviceCenterSelfTransport"
            placeholder="是否自营配送"
          >
            <el-option
              v-for="item in dictList['serviceCenterSelfTransport']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="含税/未税">
          <el-input
            :value="
              detailData ? { '1': '含税', '0': '未税' }[detailData.isTax] : ''
            "
            disabled
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="销售办事处">
          <el-input
            v-model="salesOfficeName"
            :disabled="true"
            placeholder="请输入销售办事处"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="销售组">
          <el-input
            v-model="salesGroupName"
            :disabled="true"
            placeholder="销售办事处"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="接受供应商直发">
          <el-select
            v-if="orderData"
            v-model="orderData.acceptSupplierDelivery"
            placeholder="是否接受供应商直发"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="客户订单号">
          <el-input
            v-if="orderData"
            v-model="orderData.customerReferenceNo"
            placeholder="客户订单号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="销售范围" prop="salesRange">
          <el-input
            v-if="detailData"
            :disabled="true"
            :value="`${detailData.salesOrganization}/${detailData.distributionChannel}/${detailData.productGroup}&nbsp;&nbsp;${parseDict(dictList, 'salesOrganization', detailData.salesOrganization)}，${parseDict(dictList, 'distributionChannel', detailData.distributionChannel)}，${parseDict(dictList, 'productGroup', detailData.productGroup)}`"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <div class="flex justify-center">
      <el-button type="primary" @click="handleSave">确认保存</el-button>
      <el-button @click="handleCancel">关闭</el-button>
    </div>
  </el-form>
  <InvoiceReceiverDialog
    v-if="orderData"
    v-model:show-dialog="showInvoiceReceiverDialog"
    :data="orderData"
    @submit="sumitInvoiceReceiver"
  />
</template>
