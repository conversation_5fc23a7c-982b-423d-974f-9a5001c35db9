<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { ref } from 'vue';
import { useCommonStore } from '@/stores/common';
import { setValueIfIncludesZero } from '@/utils/index';
import type { EditOrder } from '@/types/edit';

const commonStore = useCommonStore();

const { dictList, orderServiceDict } = storeToRefs(commonStore);

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const calOptionDisabled = (field: keyof EditOrder, item: any) => {
  const val = (data.value[field] as string)?.split(',');
  if (val?.includes('0')) {
    return item.code !== '0';
  }
  // 包装要求03，04互斥
  if (field === 'packagingReq') {
    if (val?.includes('03')) {
      return item.code === '04';
    } else if (val?.includes('04')) {
      return item.code === '03';
    }
  }
  return false;
};

const packagingReq = ref<string[]>(
  (data.value.packagingReq || '').split(',').filter((item) => !!item)
);
const deliveryWarehouseInfo = ref<string[]>(
  (data.value.deliveryWarehouseInfo || '').split(',').filter((item) => !!item)
);

const changePackagingReq = (val: string[]) => {
  packagingReq.value = setValueIfIncludesZero(val);
  data.value.packagingReq = setValueIfIncludesZero(val).join(',');
};
const changeDeliveryWarehouseInfo = (val: string[]) => {
  deliveryWarehouseInfo.value = setValueIfIncludesZero(val);
  data.value.deliveryWarehouseInfo = setValueIfIncludesZero(val).join(',');
};
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label-width="0" prop="hideLogo" style="padding-left: 20px">
        <el-checkbox
          v-model="data.hideLogo"
          label="隐藏logo"
          true-label="X"
          false-label="Z"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="包装要求" prop="packagingReq">
        <el-select
          v-model="packagingReq"
          placeholder="请选择包装要求"
          style="width: 100%"
          multiple
          @change="changePackagingReq"
        >
          <el-option
            v-for="item in dictList['packagingReq']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('packagingReq', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="紧固件检测" prop="fastenerDetect">
        <el-select
          v-model="data.fastenerDetect"
          placeholder="请选择紧固件检测"
          style="width: 100%"
        >
          <el-option
            v-for="item in orderServiceDict['fastenerDetect']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="送货资料需要仓配信息" prop="deliveryWarehouseInfo">
        <el-select
          v-model="deliveryWarehouseInfo"
          placeholder="请选择送货资料需要仓配信息"
          style="width: 100%"
          multiple
          @change="changeDeliveryWarehouseInfo"
        >
          <el-option
            v-for="item in dictList['deliveryWarehouseInfo']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('deliveryWarehouseInfo', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="紧固件特殊包装要求" prop="fastenerSpecialPackageReq">
        <el-select
          v-model="data.fastenerSpecialPackageReq"
          placeholder="请选择紧固件特殊包装要求"
          style="width: 100%"
        >
          <el-option
            v-for="item in orderServiceDict['fastenerSpecialPackageReq']
              ?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="交货其他备注">
        <el-input v-model="data.deliveryOtherNote" maxlength="50" />
      </el-form-item>
    </el-col>
  </el-row>
</template>
