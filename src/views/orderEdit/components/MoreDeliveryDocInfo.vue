<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { ref } from 'vue';
import { useCommonStore } from '@/stores/common';
import { setValueIfIncludesZero } from '@/utils/index';
import type { EditOrder } from '@/types/edit';

const commonStore = useCommonStore();

const { dictList, orderServiceDict } = storeToRefs(commonStore);

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const dnSignatureReq = ref<string[]>(
  (data.value.dnSignatureReq || '').split(',').filter((item) => !!item)
);
const otherLabelReq = ref<string[]>(
  (data.value.otherLabelReq || '').split(',').filter((item) => !!item)
);
const fastenerLabelReq = ref<string[]>(
  (data.value.fastenerLabelReq || '').split(',').filter((item) => !!item)
);
const labelPasteWay = ref<string[]>(
  (data.value.labelPasteWay || '').split(',').filter((item) => !!item)
);

const changeDnSignatureReq = (val: string[]) => {
  dnSignatureReq.value = setValueIfIncludesZero(val);
  data.value.dnSignatureReq = setValueIfIncludesZero(val).join(',');
};
const changeOtherLabelReq = (val: string[]) => {
  otherLabelReq.value = setValueIfIncludesZero(val);
  data.value.otherLabelReq = setValueIfIncludesZero(val).join(',');
};
const changeFastenerLabelReq = (val: string[]) => {
  fastenerLabelReq.value = setValueIfIncludesZero(val);
  data.value.fastenerLabelReq = setValueIfIncludesZero(val).join(',');
};
const changeLabelPasteWay = (val: string[]) => {
  labelPasteWay.value = setValueIfIncludesZero(val);
  data.value.labelPasteWay = setValueIfIncludesZero(val).join(',');
};

const calOptionDisabled = (field: keyof EditOrder, item: any) => {
  if (
    ((data.value[field] as string) || '').split(',').includes('0') &&
    item.code !== '0'
  ) {
    return true;
  }
  return false;
};
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox v-model="data.attachOrder" true-label="X" false-label="Z">
          附订单
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox v-model="data.attachCoa" true-label="X" false-label="Z">
          附COA
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox v-model="data.attachMsds" true-label="X" false-label="Z">
          附MSDS
        </el-checkbox>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox v-model="data.attachTds" true-label="X" false-label="Z">
          附TDS
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox
          v-model="data.specifiedDocument"
          true-label="X"
          false-label="Z"
        >
          其他随货资料
        </el-checkbox>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="送货单模板">
        <el-select
          v-model="data.deliverySlipTemplate"
          placeholder="请选择送货单模板"
        >
          <el-option
            v-for="item in dictList['deliverySlipTemplate']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="打印份数">
        <el-input-number
          v-model="data.printNum"
          :min="0"
          :max="99"
          :step="1"
          :precision="0"
          placeholder="请输入打印份数"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="送货单纸张要求">
        <el-select v-model="data.dnPaperReq" placeholder="请选择送货单纸张要求">
          <el-option
            v-for="item in orderServiceDict['dnPaperReq']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.disabled || item.persistDisabled"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="送货单附带方式">
        <el-select
          v-model="data.dnIncidentalWay"
          placeholder="请选择送货单纸张要求"
          clearable
        >
          <el-option
            v-for="item in orderServiceDict['dnIncidentalWay']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.disabled || item.persistDisabled"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="送货单签章要求">
        <el-select
          v-model="dnSignatureReq"
          placeholder="请选择送货单纸张要求"
          multiple
          @change="changeDnSignatureReq"
        >
          <el-option
            v-for="item in orderServiceDict['dnSignatureReq']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.disabled || item.persistDisabled"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="标签模板">
        <el-select v-model="data.labelTemplate" placeholder="请选择标签模板">
          <el-option
            v-for="item in dictList['labelTemplate']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="其他标签要求">
        <el-select
          v-model="otherLabelReq"
          placeholder="请选择其他标签要求"
          multiple
          @change="changeOtherLabelReq"
        >
          <el-option
            v-for="item in orderServiceDict['otherLabelReq']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('otherLabelReq', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="紧固件标签要求">
        <el-select
          v-model="fastenerLabelReq"
          placeholder="请选择紧固件标签要求"
          multiple
          @change="changeFastenerLabelReq"
        >
          <el-option
            v-for="item in orderServiceDict['fastenerLabelReq']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('fastenerLabelReq', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item>
        <template #label>
          <div class="flex items-center">
            <el-tooltip
              effect="dark"
              content="仅适用于非OEM紧固件"
              placement="top"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
            <span class="ml-1">标签张贴方式</span>
          </div>
        </template>
        <el-select
          v-model="labelPasteWay"
          placeholder="请选择标签张贴方式"
          multiple
          @change="changeLabelPasteWay"
        >
          <el-option
            v-for="item in orderServiceDict['labelPasteWay']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('labelPasteWay', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="合格证标识">
        <el-select
          v-model="data.certificateIdentification"
          placeholder="请选合格证标识"
        >
          <el-option
            v-for="item in dictList['certificateIdentification']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="签单返回">
        <el-select v-model="data.signingBack" placeholder="请选择签单返回">
          <el-option
            v-for="item in dictList['signingBack']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.status === 'stop'"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
</template>
