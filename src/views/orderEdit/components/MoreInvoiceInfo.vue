<script setup lang="ts">
import { Ref, inject, onMounted, reactive, ref } from 'vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { useCommonStore } from '@/stores/common';
import { findInvoiceType, searchContactListByGroup } from '@/api/order';
import SelectContact from '@/components/SelectContact.vue';
import { CustomerData } from '@/types/customer';
import type { EditOrder } from '@/types/edit';
import type { ContactType, SalesDictType } from '@/types/common';

const commonStore = useCommonStore();

const invoiceFormRef = ref<FormInstance>();
const { dictList } = storeToRefs(commonStore);
const cusDetail = inject<Ref<CustomerData>>('customerData');

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const rules = reactive<FormRules<EditOrder>>({
  receivingInvoiceName: [
    { required: true, message: '请选择收票联系人', trigger: 'blur' },
  ],
  receivingInvoicePhone: [
    { required: true, message: '请输入收票联系人电话', trigger: 'blur' },
  ],
  receivingInvoiceAddress: [
    { required: true, message: '请输入收票联系人地址', trigger: 'blur' },
  ],
});

const loadingInvoiceContact = ref(false);
const receivingInvoice = ref<Partial<ContactType>>({});
const receivingInvoiceList = ref<ContactType[]>([]);

const queryReceivingInvoiceContactList = (contactName: string) => {
  contactListByGroupHandle(contactName, data?.value?.invoiceReceiver || '');
};
// 收票联系人
const receivingInvoiceChange = (val: ContactType) => {
  if (val) {
    receivingInvoice.value = val;
    data.value.receivingInvoiceContact = Number(val.contactId); // id
    data.value.receivingInvoiceName = val.contactName || ''; // 姓名
    data.value.receivingInvoicePhone = val.contactPhone || '--'; // 电话
    data.value.receivingInvoiceAddress = val.address || '--'; // 地址
  } else {
    receivingInvoice.value = {};
    data.value.receivingInvoiceName = ''; // 姓名
    data.value.receivingInvoicePhone = ''; // 电话
    data.value.receivingInvoiceAddress = ''; // 地址
  }
};
const initContact = () => {
  receivingInvoice.value = {
    address: data.value.receivingInvoiceAddress,
    contactId: data.value.receivingInvoiceContact
      ? data.value.receivingInvoiceContact.toString()
      : '',
    contactName: data.value.receivingInvoiceName,
    contactPhone: data.value.receivingInvoicePhone,
  };
  contactListByGroupHandle('', data?.value.invoiceReceiver || '').then(() => {
    receivingInvoiceList.value = [
      ...receivingInvoiceList.value.filter(
        (item) =>
          item.contactId !== data.value.receivingInvoiceContact?.toString() &&
          Number(item.contactId) !== data.value.receivingInvoiceContact
      ),
    ];
    if (receivingInvoice.value?.contactId) {
      receivingInvoiceList.value.unshift(receivingInvoice.value as ContactType);
    }
  });
};
const contactListByGroupHandle = async (
  contactName: string,
  customerCode: string
) => {
  if (cusDetail && cusDetail.value.saleOrgVO) {
    const { salesOrganization, productGroup, distributionChannel } =
      cusDetail.value.saleOrgVO;
    loadingInvoiceContact.value = true;
    await searchContactListByGroup({
      customerCode,
      contactName,
      distributionChannel,
      productGroup,
      salesOrganization,
    })
      .then((res) => {
        if (res?.data?.records?.length) {
          receivingInvoiceList.value = res?.data?.records;
        }
      })
      .finally(() => {
        loadingInvoiceContact.value = false;
      });
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};

const invoiceTypeOptions = ref<SalesDictType[]>([]);
const getInvoiceType = async () => {
  const { salesOrganization } = cusDetail?.value?.saleOrgVO || {};
  const res = await findInvoiceType({ salesOrg: salesOrganization });
  if (res.code === 200 && res.data) {
    invoiceTypeOptions.value = res.data;
  } else {
    invoiceTypeOptions.value = [];
  }
};

onMounted(() => {
  initContact();
  getInvoiceType();
});
defineExpose({
  invoiceFormRef,
});
</script>

<template>
  <el-form
    ref="invoiceFormRef"
    :model="data"
    label-width="165px"
    :rules="rules"
  >
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="客户税号">
          <el-input
            :value="cusDetail?.corporationTaxNum || ''"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开票地址和电话">
          <el-input :value="data.billingAddressAndPhone" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开户行">
          <el-input :value="cusDetail?.bankName" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="账号">
          <el-input :value="cusDetail?.bankNumber" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户付款账号">
          <el-input
            :value="cusDetail?.customerPayAccountTypeName"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="收票联系人" prop="receivingInvoiceName">
          <SelectContact
            v-model="receivingInvoice"
            title="收票联系人"
            :contact-list="receivingInvoiceList"
            :loading="loadingInvoiceContact"
            :remote-method="queryReceivingInvoiceContactList"
            @change="receivingInvoiceChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收票人电话" prop="receivingInvoicePhone">
          <el-input
            v-model="data.receivingInvoicePhone"
            placeholder="请输入收票人电话"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="收票地址" prop="receivingInvoiceAddress">
          <el-input v-model="data.receivingInvoiceAddress" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="寄票备注" prop="shippingInfo">
          <el-input v-model="data.shippingInfo" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxRow">
      <el-col :span="6">
        <el-form-item label-width="0" prop="invoicingByMail">
          <el-checkbox
            v-model="data.invoicingByMail"
            label="凭邮件开票"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="returnOffset">
          <el-checkbox
            v-model="data.returnOffset"
            label="退换货抵消"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="mergeBilling">
          <el-checkbox
            v-model="data.mergeBilling"
            label="合并开票"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="autoBilling">
          <el-checkbox
            v-model="data.autoBilling"
            label="自动开票"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxRow">
      <el-col :span="6">
        <el-form-item label-width="0" prop="billingRobot">
          <el-checkbox
            v-model="data.billingRobot"
            label="开票机器人"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="showDiscount">
          <el-checkbox
            v-model="data.showDiscount"
            label="显示折扣"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="ifDocMailed">
          <el-checkbox
            v-model="data.ifDocMailed"
            label="附资料邮寄"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="发票类型" prop="invoiceType">
          <el-select v-model="data.invoiceType" placeholder="请选择发票类型">
            <el-option
              v-for="item in invoiceTypeOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合并开票要求">
          <el-select
            v-model="data.mergeBillingDemand"
            placeholder="请选择合并开票要求"
          >
            <el-option
              v-for="item in dictList['mergeBillingDemand']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="快递公司">
          <el-select
            v-model="data.expressCompany"
            clearable
            placeholder="请选择快递公司"
          >
            <el-option
              v-for="item in dictList['expressCompany']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="发票备注">
          <el-input v-model="data.financialNote" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped>
.checkBoxRow {
  padding-left: 100px;
}
</style>
