<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import DividerHeader from '@/components/DividerHeader.vue';
import { PriceData } from '@/types/price';
import { calculatePrice as calculatePriceApi } from '@/api/order';
import { isNull } from '@/utils';
import ItemDetailOrder from './ItemDetailOrder.vue';
import ItemDetailWarehouse from './ItemDetailWarehouse.vue';
import ItemDetailPrice from './ItemDetailPrice.vue';
import ItemDetailOther from './ItemDetailOther.vue';
import type { EditItem, EditOrder } from '@/types/edit';
import type { DetailOrder } from '@/types/detail';

const emit = defineEmits(['cancel', 'save']);

const props = defineProps<{
  data: EditItem;
  orderData: DetailOrder;
  editData: EditOrder;
}>();

const tempData = { ...props.data };
const itemData = ref<EditItem>(tempData);
const itemFormRef = ref<FormInstance>();

const defaultRules = {
  quantity: [{ required: true, message: '请填写数量', trigger: 'blur' }],
  factory: [{ required: true, message: '请选择工厂', trigger: 'blur' }],
  directDeliverySupplier: [
    { required: true, message: '请选择直发', trigger: 'blur' },
  ],
  position: [{ required: true, message: '请选择发货仓', trigger: 'blur' }],
};

const nullSkuRules = {
  customerMaterialName: [
    { required: true, message: '请输入客户物料名称', trigger: 'blur' },
  ],
};

const rules = ref<FormRules<EditItem>>({});

const handleSubmit = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      const {
        positionModifyReason,
        positionModifyDetail,
        largeReduceReasonDesc,
        materiel,
        quantity,
        customerMaterialQuantity,
      } = itemData.value;
      if (
        !materiel &&
        (!quantity || quantity === 0) &&
        (!customerMaterialQuantity || customerMaterialQuantity === 0)
      ) {
        ElMessage.error('数量和客户物料数量不能同时为0！');
        return;
      }
      if (largeReduceReasonDesc && largeReduceReasonDesc?.length > 50) {
        ElMessage.error('减少大类详情最多50字符！');
        return;
      }
      if (positionModifyReason && !positionModifyDetail) {
        ElMessage.error('仓库修改详情必须填写');
        return;
      }
      emit('save', itemData.value);
      emit('cancel');
    }
  });
};

const handleCancel = () => {
  emit('cancel');
};

watchEffect(() => {
  if (!itemData.value?.materiel) {
    rules.value = { ...nullSkuRules };
  } else {
    rules.value = { ...defaultRules };
  }
  if (!props.orderData) return;
  if (props.orderData.isTax === '1') {
    rules.value.taxPrice = [
      { required: true, message: '请填写含税单价', trigger: 'blur' },
    ];
  } else {
    rules.value.freeTaxPrice = [
      { required: true, message: '请填写未税单价', trigger: 'blur' },
    ];
  }
  if (props.orderData.orderType === 'Z014') {
    rules.value.customerDate = [
      { required: true, message: '请填写客户期望送达日期', trigger: 'blur' },
    ];
  }
  if (props.orderData.orderType === 'Z018') {
    rules.value.rentalDueDate = [
      { required: true, message: '请填写租赁截止日期', trigger: 'blur' },
    ];
    rules.value.fixedAssetsId = [
      { required: true, message: '请输入固定资产编号', trigger: 'blur' },
    ];
  }
  const oldItemData = props.orderData?.items.find((_row) => {
    return _row.soItemNo === itemData.value?.soItemNo;
  });
  if (
    (itemData.value?.addType === '1' &&
      itemData.value?.largeReduceReason === '17') ||
    (oldItemData?.largeReduceReason !== '17' &&
      itemData.value?.largeReduceReason === '17')
  ) {
    rules.value.materialReplaceProcessNo = [
      { required: true, message: '请输入替换OA流程', trigger: 'blur' },
    ];
  } else {
    rules.value.materialReplaceProcessNo = [];
  }
  itemFormRef.value?.clearValidate();
});

const calculatePrice = async (): Promise<PriceData | undefined> => {
  if (props.orderData && props.orderData.isTax != null) {
    const { orderBasis, isTax } = props.orderData;
    const item = {
      discountAmount: Number(itemData.value.discountAmount),
      itemNo: itemData.value.soItemNo || itemData.value.idx,
      promotionalDiscountRate: isNull(itemData.value.promotionalDiscountRate)
        ? 100
        : itemData.value.promotionalDiscountRate,
      quantity: itemData.value.quantity || 0,
      sku: itemData.value.materiel,
      taxRate: itemData.value.taxRate || 0,
      unitPrice:
        isTax === '1' ? itemData.value.taxPrice : itemData.value.freeTaxPrice,
    };
    const queryData = {
      orderBasis,
      isTax,
      items: [item],
    };
    const res = await calculatePriceApi(queryData);
    if (res.code === 200) {
      return res.data;
    }
  }
};

const handlePriceChange = async () => {
  const priceData = await calculatePrice();
  if (priceData?.priceItems) {
    const foundItem = priceData.priceItems.find(
      (item) =>
        item.itemNo === itemData.value.idx ||
        item.itemNo === itemData.value.soItemNo
    );
    itemData.value = {
      ...itemData.value,
      ...foundItem,
      freeTaxPrice: foundItem?.untaxedUnitPrice || 0,
    };
  }
};
</script>

<template>
  <el-form
    ref="itemFormRef"
    :model="itemData"
    label-width="160px"
    :rules="rules"
  >
    <DividerHeader>商品信息</DividerHeader>
    <ItemDetailOrder v-model="itemData" @change-price="handlePriceChange" />
    <DividerHeader>仓位信息</DividerHeader>
    <ItemDetailWarehouse v-model="itemData" />
    <DividerHeader>价格信息</DividerHeader>
    <ItemDetailPrice v-model="itemData" @change-price="handlePriceChange" />
    <DividerHeader>其他信息</DividerHeader>
    <ItemDetailOther v-model="itemData" :edit-data="editData" />
    <div class="flex items-center justify-center h-60px">
      <el-button @click="handleCancel">关闭</el-button>
      <el-button type="primary" @click="handleSubmit(itemFormRef)"
        >确认</el-button
      >
    </div>
  </el-form>
</template>
