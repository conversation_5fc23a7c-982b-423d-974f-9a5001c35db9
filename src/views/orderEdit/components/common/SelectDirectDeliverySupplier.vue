<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import {
  isEnableDirectDeliverySupplier as isEnableDirectDeliverySupplierFn,
  isServiceOrder,
} from '@/utils/orderType';
import { from814OrderBasis } from '@/constants/order';
import type { EditItem } from '@/types/edit';
import type { DetailOrder } from '@/types/detail';

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  orderData: DetailOrder | undefined;
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  disabled?: boolean;
}>();
const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);

const row = useVModel(props, 'modelValue', emit);

const allowSelectWhsAuth = inject<Ref<boolean>>('allowSelectWhsAuth');

const isEnableDirectDeliverySupplier = computed(() => {
  return props.orderData
    ? isEnableDirectDeliverySupplierFn(props.orderData.orderType)
    : true;
});
const isService = computed(() => {
  return props.orderData ? isServiceOrder(props.orderData.orderType) : false;
});
const directDeliverySupplierList = computed(() => {
  return dictList.value.directDeliverySupplier.filter((item) =>
    row.value.addType === '1' ? true : Number.parseInt(item.code, 10) < 2
  );
});
const changeDirectDeliverySupplier = () => {
  row.value.position = '';
};

const isSAP814 = computed(() =>
  from814OrderBasis.includes(props.orderData?.orderBasis || '')
);

// 不在名单内客户建单时，选择直发=系统自动判断，库位=自动挑仓，且两个字段置灰不可选择
const disabledDirectAndPosition = computed(
  () =>
    !allowSelectWhsAuth?.value &&
    row.value.directDeliverySupplier === '2' &&
    row.value.position === '-1'
);
</script>

<template>
  <el-select
    v-model="row.directDeliverySupplier"
    :disabled="
      !!props.disabled ||
      isService ||
      isEnableDirectDeliverySupplier ||
      isSAP814 ||
      disabledDirectAndPosition
    "
    :size="props.size"
    placeholder="请选择"
    @change="changeDirectDeliverySupplier"
  >
    <el-option
      v-for="item in directDeliverySupplierList"
      :key="item.code"
      :label="item.name"
      :value="item.code"
      :disabled="item.disabled"
    />
  </el-select>
</template>
