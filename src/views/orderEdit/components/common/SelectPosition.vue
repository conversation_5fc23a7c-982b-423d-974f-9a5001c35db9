<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject, ref, watchEffect } from 'vue';
import endsWith from 'lodash/endsWith';
import { queryFinalLT } from '@/utils/edit';
import { from814OrderBasis } from '@/constants/order';
import type { SupplyPosition } from '@/types/position';
import type { DetailOrder } from '@/types/detail';
import type { EditItem } from '@/types/edit';

type Options = {
  value: string;
  label: string;
};
const emit = defineEmits(['update:modelValue', 'change']);

const morePositionDisabled = inject('morePositionDisabled');
const { simPositionList } =
  inject<{
    simPositionList: Ref<SupplyPosition[]>;
  }>('sim') || {};

const allowSelectWhsAuth = inject<Ref<boolean>>('allowSelectWhsAuth');

const props = defineProps<{
  orderData: DetailOrder | undefined;
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  disabled?: boolean;
}>();

const row = useVModel(props, 'modelValue', emit);
const options = ref<Options[]>([]);
watchEffect(() => {
  const { directDeliverySupplier, materiel, factory, addType } = row.value;
  let _simPosList: SupplyPosition[] = [];
  if (materiel && factory && simPositionList) {
    _simPosList = simPositionList.value.filter(
      (item) => item.sku === materiel && item.factory === factory
    );
  }
  if (
    directDeliverySupplier === '1' ||
    ['Z005', 'Z013', 'Z018'].includes(props.orderData?.orderType || '')
  ) {
    _simPosList = _simPosList.filter((item) => endsWith(item.code, '04'));
  }
  if (addType === '1' && directDeliverySupplier === '2') {
    _simPosList.unshift({
      code: '-1',
      name: '自动挑仓',
    });
  }
  options.value = _simPosList.map((pos) => ({
    label: pos.code === '-1' ? pos.name : `${pos.code} ${pos.name}`,
    value: pos.code,
  }));
  console.log(directDeliverySupplier, options.value.length, row.value.soItemNo);
});

const changePosition = async () => {
  delete row.value.lastConfirmedPosition;
  if (props.orderData) {
    await queryFinalLT(row.value, props.orderData);
  }
  emit('change', row.value);
};

const isSAP814 = computed(() =>
  from814OrderBasis.includes(props.orderData?.orderBasis || '')
);

// 不在名单内客户建单时，选择直发=系统自动判断，库位=自动挑仓，且两个字段置灰不可选择
const disabledDirectAndPosition = computed(
  () =>
    !allowSelectWhsAuth?.value &&
    row.value.directDeliverySupplier === '2' &&
    row.value.position === '-1'
);
</script>

<template>
  <el-select-v2
    v-model="row.position"
    filterable
    clearable
    :size="props.size"
    :disabled="props.disabled || isSAP814 || disabledDirectAndPosition"
    placeholder="请选择"
    :options="options"
    popper-class="custom-select-v2"
    :class="{ tableCellError: !row.position }"
    @change="changePosition"
  >
    <template #empty>
      <div v-if="row.directDeliverySupplier === '0'" style="text-align: center">
        <p style="margin-top: 10px; color: grey">无匹配数据</p>
        <p
          v-if="morePositionDisabled"
          style="margin: 10px 10px 0 10px; color: grey"
        >
          所选仓不在仓网内，如有疑问请联系IT
        </p>
      </div>
    </template>
  </el-select-v2>
</template>

<style lang="scss" scoped>
.tableCellError .el-select__wrapper {
  border: 1px solid #ff4949;
  border-radius: 4px;
}
</style>

<style lang="scss">
.custom-select-v2 {
  .el-select-dropdown__list {
    width: 280px !important;
  }
}
</style>
