<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { DetailOrder } from '@/types/detail';
import type { EditItem } from '@/types/edit';

const emit = defineEmits(['update:modelValue', 'changeAmount']);

const props = defineProps<{
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  orderData: DetailOrder | undefined;
}>();

const row = useVModel(props, 'modelValue', emit);

const computedMax = (row: EditItem) => {
  if (row.referType === '03') {
    return row.replenishNum;
  } else if (props.orderData?.orderBasis === 'STOCK_CLEARANCE') {
    const foundItem = props.orderData?.items?.find(
      (item) => item.soItemNo === row.soItemNo
    );
    if (foundItem) {
      return foundItem.quantity;
    }
  }
  return Number.POSITIVE_INFINITY;
};

const handleChangeAmount = () => {
  emit('changeAmount');
};
</script>

<template>
  <el-input-number
    v-model="row.quantity"
    :size="props.size"
    :class="{ tabelCellError: row.quantity === 0 }"
    :min="0"
    :max="computedMax(row)"
    :step="1"
    style="width: 100%"
    @change="handleChangeAmount"
  />
</template>

<style setup lang="scss">
.tabelCellError {
  .el-input {
    border: 1px solid #ff4949;
  }
}
</style>
