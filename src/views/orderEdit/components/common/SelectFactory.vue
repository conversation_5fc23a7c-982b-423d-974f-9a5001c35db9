<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { getFactoryList } from '@/utils/item';
import { useCommonStore } from '@/stores/common';
import { getSimPosition } from '@/utils/edit';
import { Z003Transform } from '@/utils';
import type { SimPositionType, SupplyPosition } from '@/types/position';
import type { EditItem } from '@/types/edit';
import type { DetailOrder } from '@/types/detail';

const emit = defineEmits(['update:modelValue']);

const { simPositionList, simDeliveryList, updatePositionList } =
  inject<{
    simPositionList: Ref<SupplyPosition[]>;
    simDeliveryList: Ref<SupplyPosition[]>;
    updatePositionList: (
      type: SimPositionType,
      posList: SupplyPosition[]
    ) => void;
  }>('sim') || {};

const props = defineProps<{
  orderData: DetailOrder | undefined;
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  disabled?: boolean;
}>();
const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);

const row = useVModel(props, 'modelValue', emit);
const factoryList = computed(() => {
  const { distributionChannel, salesOrganization } = props.orderData || {};
  if (distributionChannel && salesOrganization) {
    return getFactoryList(
      {
        distributionChannel,
        salesOrganization,
      },
      dictList.value
    );
  }
  return [];
});

const isFactoryDisable = (row: EditItem, code: string) => {
  if (row && code) {
    const { skuFactoryPriceMap } = row;
    if (skuFactoryPriceMap) {
      const c = Number.parseInt(code, 10);
      return !(skuFactoryPriceMap[c] && skuFactoryPriceMap[c].taxRate != null);
    }
  }
  return true;
};

const changeFactory = async (value: string) => {
  row.value.factory = value;
  row.value.position = '';
  row.value.deliveryPosition = '';
  const materiel = row.value.materiel;
  const factory = value;
  if (row.value.skuFactoryPriceMap) {
    const { salesOrganization = '' } = props.orderData || {};
    const factoryProductPriceVOMap = row.value.skuFactoryPriceMap;
    const c = row.value.ifSalePriceChannelLimit
      ? Number(Z003Transform(salesOrganization))
      : Number.parseInt(factory, 10);
    const priceMap = factoryProductPriceVOMap[c];
    if (priceMap && row.value.addType === '1') {
      row.value.suggestPrice = Number(priceMap.suggestPrice) || 0;
    }
  }
  const factorySet: string[] = [];
  const skuSet: string[] = [];
  if (materiel && factory) {
    const found1 = (simPositionList?.value || []).some(
      (pos) => pos.sku === materiel && pos.factory === factory
    );
    const found2 = (simDeliveryList?.value || []).some(
      (pos) => pos.sku === materiel && pos.factory === factory
    );
    if (!found1 && !found2) {
      factorySet.push(factory);
      skuSet.push(materiel);
    }
  }
  if (factorySet.length > 0 && skuSet.length > 0 && updatePositionList) {
    const [list1, list2] = await Promise.all([
      getSimPosition(factorySet, skuSet, 1),
      getSimPosition(factorySet, skuSet, 3),
    ]);
    updatePositionList('position', list1);
    updatePositionList('delivery', list2);
  }
};
</script>

<template>
  <el-select
    v-if="row"
    v-model="row.factory"
    :size="props.size"
    placeholder="请选择"
    style="width: 100%"
    :disabled="props.disabled"
    @change="changeFactory"
  >
    <el-option
      v-for="item in factoryList"
      :key="item.code"
      :label="`${item.code || ''}-${item.name}`"
      :value="item.code"
      :disabled="isFactoryDisable(row, item.code)"
    />
  </el-select>
</template>
