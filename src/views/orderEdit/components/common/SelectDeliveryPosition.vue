<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject, ref } from 'vue';
import { queryFinalLT } from '@/utils/edit';
import type { SupplyPosition } from '@/types/position';
import type { EditItem } from '@/types/edit';
import type { DetailOrder } from '@/types/detail';

const emit = defineEmits(['update:modelValue', 'change']);

const props = defineProps<{
  orderData: DetailOrder | undefined;
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  disabled?: boolean;
}>();

const row = useVModel(props, 'modelValue', emit);

const loading = ref<boolean>(false);

const { simDeliveryList } =
  inject<{
    simDeliveryList: Ref<SupplyPosition[]>;
  }>('sim') || {};

const options = computed(() => {
  const { materiel, factory } = row.value;
  if (materiel && factory && simDeliveryList) {
    return simDeliveryList.value
      .filter((item) => item.sku === materiel && item.factory === factory)
      .map((pos) => ({
        label: `${pos.code} ${pos.name}`,
        value: pos.code,
      }));
  }
  return [];
});

const handleChange = (val: string) => {
  row.value.deliveryPosition = val || '';
  row.value.deliveryWarehouseCode =
    (
      (simDeliveryList?.value || []).find(
        (item) => item.code === row.value.deliveryPosition
      ) || {}
    ).warehouseCode || '';
  if (props.orderData) {
    queryFinalLT(row.value, props.orderData);
  }
};
</script>

<template>
  <el-select-v2
    v-model="row.deliveryPosition"
    filterable
    clearable
    :size="props.size"
    placeholder="请选择"
    popper-class="custom-select-v2"
    :loading="loading"
    :disabled="
      !!props.disabled ||
      row.clearedQty > 0 ||
      !props.orderData?.isCollectWhiteList ||
      row.referType === '03' ||
      row.directDeliverySupplier === '1'
    "
    :options="options || []"
    @change="handleChange"
  />
</template>

<style lang="scss">
.custom-select-v2 {
  .el-select-dropdown__list {
    width: 280px !important;
  }
}
</style>
