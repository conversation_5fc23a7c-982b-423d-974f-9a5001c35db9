<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import { Ref, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { getDisabledDate } from '@/utils/item';
import { useCommonStore } from '@/stores/common';
import type { EditItem, EditOrder } from '@/types/edit';
import type { DetailOrder } from '@/types/detail';

const emit = defineEmits(['update:modelValue', 'change']);

const props = defineProps<{
  orderData: EditOrder | undefined;
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  disabled?: boolean;
}>();

const row = useVModel(props, 'modelValue', emit);
const detailData = inject<Ref<DetailOrder>>('orderData');

const commonStore = useCommonStore();
const { holidaysList } = storeToRefs(commonStore);

const disabledDate = (time: Date) => {
  if (props.orderData) {
    const specifiedReceiptDayOfWeek =
      props.orderData?.specifiedReceiptDayOfWeek
        ?.split(',')
        ?.filter((item) => !!item) || [];
    const receiptTimeCategory = props.orderData?.receiptTimeCategory || '';
    const check =
      !['Z002', 'Z014'].includes(detailData?.value.orderType || '') &&
      !['8', 'X'].includes(props.orderData.bidCustomer || '');
    return getDisabledDate(
      time,
      specifiedReceiptDayOfWeek,
      receiptTimeCategory,
      holidaysList.value,
      check
    );
  }
};

const handleFocusDatePicker = () => {
  const specifiedReceiptDayOfWeek =
    props.orderData?.specifiedReceiptDayOfWeek
      ?.split(',')
      ?.filter((day) => !!day) || [];

  const receiptTimeCategory = props.orderData?.receiptTimeCategory;
  if (
    (receiptTimeCategory === 'Z' || !receiptTimeCategory) &&
    specifiedReceiptDayOfWeek?.length &&
    specifiedReceiptDayOfWeek?.every(
      (day: string) => !['0', '01', '02', '03', '04', '05'].includes(day)
    )
  ) {
    ElMessage.warning(
      '无可选日期，请修改客户指定日收货或工作日与周末均可收货!'
    );
  }
};

const changeCustomerDate = () => {
  row.value.isChangeCustomerDate = true;
};
</script>

<template>
  <el-date-picker
    v-model="row.customerDate"
    :disabled-date="disabledDate"
    :disabled="props.disabled"
    :size="props.size"
    type="date"
    style="width: 100%"
    placeholder="选择日期"
    value-format="YYYY-MM-DD"
    @focus="handleFocusDatePicker"
    @change="changeCustomerDate"
  />
</template>
