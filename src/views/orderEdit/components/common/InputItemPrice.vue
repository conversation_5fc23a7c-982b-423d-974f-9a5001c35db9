<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { TaxEnum } from '@/types/common';
import type { EditItem } from '@/types/edit';
import type { DetailOrder } from '@/types/detail';

const emit = defineEmits(['update:modelValue', 'change']);

const props = defineProps<{
  modelValue: EditItem;
  size: '' | 'large' | 'default' | 'small';
  disabled: boolean;
  tax: TaxEnum;
  orderData: DetailOrder;
}>();
const row = useVModel(props, 'modelValue', emit);

const handleChange = () => {
  emit('change');
};
// const handleChange = (val: number | undefined, tax: TaxEnum) => {
//   if (val != null && row.value.taxRate) {
//     if (tax === '1') {
//       row.value.freeTaxPrice = val / (1 + row.value.taxRate);
//     } else {
//       row.value.taxPrice = val * (1 + row.value.taxRate);
//     }
//   }
//   emit('change');
// };
</script>

<template>
  <el-input-number
    v-if="tax === '1'"
    v-model="row.taxPrice"
    :size="size"
    style="width: 100%"
    :min="0"
    :precision="6"
    :step="1"
    :disabled="disabled"
    @change="handleChange"
  />
  <el-input-number
    v-else
    v-model="row.freeTaxPrice"
    :size="size"
    style="width: 100%"
    :min="0"
    :precision="6"
    :step="1"
    :disabled="disabled"
    @change="handleChange"
  />
</template>
