<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { Ref, computed, inject } from 'vue';
import { useCommonStore } from '@/stores/common';
import { SalesDictType } from '@/types/common';
import { DetailOrder } from '@/types/detail';
import SelectFactory from './common/SelectFactory.vue';
import SelectDirectDeliverySupplier from './common/SelectDirectDeliverySupplier.vue';
import SelectPosition from './common/SelectPosition.vue';
import SelectDeliveryPosition from './common/SelectDeliveryPosition.vue';
import type { EditItem } from '@/types/edit';

const orderData = inject<Ref<DetailOrder>>('orderData');

type Item = EditItem;

const emit = defineEmits(['update:modelValue']);
const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);
const props = defineProps<{
  modelValue: Item;
}>();

const data = useVModel(props, 'modelValue', emit);

const isStockClearance = inject<Ref<boolean>>('isStockClearance');

const positionModifyReasonOptions = computed(() => {
  const originOptions = dictList.value.positionModifyReason;
  let options = originOptions;
  const typeOptions = options.filter(
    (option) => orderData && option.parentCode === orderData.value.orderType
  );
  if (typeOptions.length > 0) {
    options = typeOptions;
  } else {
    options = options.filter((option) => option.parentCode === '');
  }
  // Z007 兼容历史数据
  if (originOptions && orderData && orderData.value?.orderType === 'Z007') {
    const finalList: SalesDictType[] = [];
    for (const item of originOptions) {
      if (!finalList.some((fItem) => fItem.code === item.code)) {
        finalList.push(item);
      } else {
        const findIndex = finalList.findIndex(
          (fItem) => fItem.code === item.code
        );
        if (item.parentCode === 'Z007') {
          finalList.splice(findIndex, 1);
          finalList.push(item);
        }
      }
    }
    return [
      ...finalList.filter((e) => e.status !== 'stop'),
      ...finalList.filter((e) => e.status === 'stop'),
    ];
  }
  const a = options.filter((option) => option.status !== 'stop');
  const b = options.filter((option) => option.status === 'stop');
  return [...a, ...b];
});
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="工厂" prop="factory">
        <SelectFactory
          v-model="data"
          :order-data="orderData"
          :disabled="isStockClearance"
          size="default"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="选择直发" prop="directDeliverySupplier">
        <SelectDirectDeliverySupplier
          v-model="data"
          size="default"
          :order-data="orderData"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="发货仓" prop="position">
        <SelectPosition
          v-model="data"
          :order-data="orderData"
          :disabled="isStockClearance"
          size="default"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="集货仓" prop="deliveryPosition">
        <SelectDeliveryPosition
          v-model="data"
          :order-data="orderData"
          :disabled="isStockClearance"
          size="default"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="仓库修改原因" prop="positionModifyReason">
        <el-select
          v-model="data.positionModifyReason"
          placeholder="请选择修改原因"
          style="width: 100%"
          clearable
          :disabled="isStockClearance"
        >
          <el-option
            v-for="item in positionModifyReasonOptions"
            :key="item.code"
            :disabled="item.status === 'stop'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="仓库修改详情" prop="positionModifyDetail">
        <el-input
          v-model="data.positionModifyDetail"
          placeholder="仓库修改详情"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>
