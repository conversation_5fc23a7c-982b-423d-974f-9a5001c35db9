<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { Ref, computed, inject } from 'vue';
import { useCommonStore } from '@/stores/common';
import { DetailOrder } from '@/types/detail';
import { findNormalItemInDictList } from '@/utils/item';
import SelectCustomerDate from './common/SelectCustomerDate.vue';
import type { EditItem, EditOrder } from '@/types/edit';

type Item = EditItem;

const orderData = inject<Ref<DetailOrder>>('orderData');

const emit = defineEmits(['update:modelValue']);
const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);
const props = defineProps<{
  modelValue: Item;
  editData: EditOrder | null;
}>();

const data = useVModel(props, 'modelValue', emit);

const isStockClearance = inject<Ref<boolean>>('isStockClearance');

const largeReduceReasonOptions = computed(() => {
  let options = dictList.value.largeReduceReason;
  const typeOptions = options.filter(
    (option) => orderData && option.parentCode === orderData.value?.orderType
  );
  if (typeOptions.length > 0) {
    options = typeOptions;
  } else {
    options = options.filter((option) => option.parentCode === '');
  }
  const a = options.filter((option) => option.status !== 'stop');
  const b = options.filter((option) => option.status === 'stop');
  return [...a, ...b];
});
const showCustom = computed(() => {
  return (
    /z001/gim.test(orderData ? orderData.value?.orderType : '') &&
    data.value?.customPropertyList &&
    data.value?.customPropertyList?.length > 0
  );
});

const notAcceptDemandReasons = computed(() =>
  data.value.notAcceptDemandReasons
    ?.split(',')
    ?.filter((code) =>
      findNormalItemInDictList(dictList.value.notAcceptDemandReason, code)
    )
);
const dnOrderPendingReasons = computed(() =>
  data.value.dnOrderPendingReasons
    ?.split(',')
    ?.filter((code) =>
      findNormalItemInDictList(dictList.value.dnOrderPendingReason, code)
    )
);
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="4" style="text-align: center">
      <el-form-item label="" prop="needScrapingCode" label-width="0">
        <el-checkbox
          v-model="data.needScrapingCode"
          true-label="X"
          false-label="Z"
          :disabled="isStockClearance"
          >需要刮码</el-checkbox
        >
      </el-form-item>
    </el-col>
    <el-col :span="4">
      <el-form-item label="" prop="customerDateSensitive" label-width="0">
        <el-checkbox
          v-model="data.customerDateSensitive"
          true-label="X"
          false-label="Z"
          :disabled="isStockClearance"
        >
          客户交期敏感
        </el-checkbox>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="客户期望送达日期" prop="customerDate">
        <SelectCustomerDate
          v-if="editData"
          v-model="data"
          :order-data="editData"
          :disabled="isStockClearance"
          size="default"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="减少大类原因" prop="largeReduceReason">
        <el-select
          v-model="data.largeReduceReason"
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in largeReduceReasonOptions"
            :key="item.code"
            :disabled="item.status === 'stop'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="减少大类详情" prop="largeReduceReasonDesc">
        <el-input
          v-model="data.largeReduceReasonDesc"
          placeholder="大类减少原因说明"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="替换OA流程" prop="materialReplaceProcessNo">
        <el-input v-model="data.materialReplaceProcessNo" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="领用人" prop="demandUser">
        <el-input v-model="data.demandUser" :disabled="isStockClearance" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="需求部门" prop="demandDepartment">
        <el-input
          v-model="data.demandDepartment"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="备注" prop="remark">
        <el-input v-model="data.remark" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="采购备注" prop="purchaseNote">
        <el-input v-model="data.purchaseNote" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="不纳入需求挂起原因" prop="notAcceptDemandReasons">
        <el-select
          v-model="notAcceptDemandReasons"
          disabled
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="item in dictList['notAcceptDemandReason']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="交货挂起原因" prop="dnOrderPendingReasons">
        <el-select
          v-model="dnOrderPendingReasons"
          disabled
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="item in dictList['dnOrderPendingReason']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <template v-if="showCustom">
    <el-row
      v-for="(custom, index) in (data && data.customPropertyList) || []"
      :key="index"
      :gutter="10"
    >
      <el-col :span="12">
        <el-form-item :label="`定制属性${index + 1}`">
          <el-input readonly disabled :value="custom.customProperty" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="`定制属性备注${index + 1}`">
          <el-input
            v-model="custom.customPropertyRemark"
            :placeholder="custom.placeholder"
            maxlength="50"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </template>
  <el-row v-if="orderData?.orderType === 'Z018'" :gutter="10">
    <el-col :span="12">
      <el-form-item label="租赁截止日期" prop="rentalDueDate">
        <el-date-picker
          v-model="data.rentalDueDate"
          value-format="YYYY-MM-DD"
          type="date"
          placeholder="选择日期"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="固定资产编号" prop="fixedAssetsId">
        <el-input
          v-model="data.fixedAssetsId"
          placeholder="请输入固定资产编号"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>
