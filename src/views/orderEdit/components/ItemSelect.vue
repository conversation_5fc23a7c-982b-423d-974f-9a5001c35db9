<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { Ref, inject, ref } from 'vue';
import AddSkuItem from '@/components/AddSkuItem.vue';
// import { sensors } from '@/utils/index';
import { getSkuDetail } from '@/api/order';
import { formatSKU, generateItemNo, getSimPosition } from '@/utils/edit';
import { useCommonStore } from '@/stores/common';
import { DetailOrder } from '@/types/detail';
import { SimPositionType, SupplyPosition } from '@/types/position';
import { getDefaultDirectDeliverySupplier, sensorsSku } from '@/utils/item';
import { CustomerData } from '@/types/customer';
import type { EditItem, EditOrder } from '@/types/edit';
import type { SearchItemType } from '@/types/item';

const props = defineProps<{
  data: EditOrder;
}>();
const emit = defineEmits(['update:modelValue', 'showDetailDlg']);
const data = useVModel(props, 'data', emit);

const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);

const num = ref(1);

const { simPositionList, simDeliveryList, updatePositionList } =
  inject<{
    simPositionList: Ref<SupplyPosition[]>;
    simDeliveryList: Ref<SupplyPosition[]>;
    updatePositionList: (
      type: SimPositionType,
      posList: SupplyPosition[]
    ) => void;
  }>('sim') || {};

const orderData = inject<Ref<DetailOrder>>('orderData');
const loading = inject<Ref<boolean>>('loading');
const cusDetail = inject<Ref<CustomerData>>('customerData');
const addSkuToTable = async (
  currentSelectSku: SearchItemType,
  searchKeyWord: string
) => {
  console.log(data.value);
  // const {
  //   skuNo,
  //   materialDescribe,
  //   customerSkuNo,
  //   customerSkuName,
  //   customerSkuUnitCount,
  //   customerSkuUnit,
  //   customerSkuSpecification,
  //   dataSource,
  //   matchField,
  // } = currentSelectSku;
  // const sensorsData = {
  //   key_word: searchKeyWord,
  //   sku_no: skuNo,
  //   product_description: materialDescribe,
  //   customer_materiel_no: customerSkuNo,
  //   customer_materiel_name: customerSkuName,
  //   customer_materiel_quantity: customerSkuUnitCount,
  //   customer_materiel_quantity_unit: customerSkuUnit,
  //   customer_materiel_specifications_no: customerSkuSpecification,
  //   data_source: dataSource,
  //   match_route: matchField,
  // };
  // sensors('SoCreateSelectProductConfirmButtonClick', sensorsData); // 点击确认添加埋点
  sensorsSku('SoCreateSelectProductConfirmButtonClick', {
    ...currentSelectSku,
    searchKeyWord,
  });
  const {
    salesOrganization,
    distributionChannel,
    productGroup,
    orderType,
    customerNo,
  } = orderData?.value || {};
  const params = {
    customerNo,
    salesOrganization,
    distributionChannel,
    productGroup,
    orderType,
  };
  if (loading) {
    loading.value = true;
  }
  try {
    const res = await getSkuDetail(currentSelectSku.skuNo, params);
    console.log(res);
    const newList: EditItem[] = [];
    if (res.code === 200 && orderData) {
      const detail = res.data;
      const formatedSku = formatSKU(
        currentSelectSku,
        detail,
        orderData.value,
        dictList.value,
        cusDetail?.value
      ) as unknown as EditItem;
      console.log(formatedSku);
      formatedSku.idx = `new-${num.value}`;
      num.value++;
      data.value.items = [formatedSku, ...data.value.items];
      newList.push(formatedSku);
      const factorySet: string[] = [];
      const skuSet: string[] = [];
      for (const newItem of newList) {
        const { materiel, factory } = newItem;
        if (materiel && factory) {
          const found1 = (simPositionList?.value || []).some(
            (pos) => pos.sku === materiel && pos.factory === factory
          );
          const found2 = (simDeliveryList?.value || []).some(
            (pos) => pos.sku === materiel && pos.factory === factory
          );
          if (!found1 && !found2) {
            factorySet.push(factory);
            skuSet.push(materiel);
          }
        }
      }
      if (factorySet.length > 0 && skuSet.length > 0 && updatePositionList) {
        const [list1, list2] = await Promise.all([
          getSimPosition(factorySet, skuSet, 1),
          getSimPosition(factorySet, skuSet, 3),
        ]);
        updatePositionList('position', list1);
        updatePositionList('delivery', list2);
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    if (loading) {
      loading.value = false;
    }
  }
};

const addNullSkuToTable = () => {
  const { autoBatching, salesOrganization, orderType } = orderData?.value || {};
  const { currency, refuseSystemDeliveryDate, customerDate } =
    data.value.items.at(-1) || {};
  const sku: EditItem = {
    soItemNo: '',
    quantity: 0,
    sapItemNo: '',
    taxPrice: 0,
    freeTaxPrice: 0,
    currency,
    factory: '1000',
    addType: '1',
    validateItemNo: generateItemNo(),
    clearedQty: 0,
  };
  sku.idx = `new-${num.value}`;
  num.value++;
  if (salesOrganization && orderType) {
    sku.directDeliverySupplier = getDefaultDirectDeliverySupplier(
      salesOrganization,
      orderType,
      dictList.value
    );
    sku.position = sku.directDeliverySupplier === '2' ? '-1' : '';
  }
  // 整单时新增行 默认为 原整单是否接受标期标识
  if (autoBatching !== 'X') {
    sku.refuseSystemDeliveryDate = refuseSystemDeliveryDate;
    // sku.originRefuseSystemDeliveryDate = sku.refuseSystemDeliveryDate
    if (customerDate) {
      sku.customerDate = customerDate;
    }
  }
  // data.value.items.unshift(sku);
  data.value.items = [sku, ...data.value.items];
  emit('showDetailDlg', sku);
};
</script>

<template>
  <AddSkuItem
    :customer-code="orderData?.customerNo"
    source="edit"
    @add-sku-to-table="addSkuToTable"
    @add-null-sku-to-table="addNullSkuToTable"
  />
</template>
