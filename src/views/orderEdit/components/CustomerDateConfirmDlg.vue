<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  showDialog: { type: Boolean, default: false },
  title: { type: String, default: '操作提示' },
  failContent: { type: String, default: '' },
});

const emit = defineEmits(['update:showDialog', 'submit']);

const dlgVisible = computed({
  get() {
    return props.showDialog;
  },
  set(val: boolean) {
    emit('update:showDialog', val);
  },
});

const submit = (type: string) => {
  dlgVisible.value = false;
  emit('submit', type);
};
</script>

<template>
  <el-dialog v-model="dlgVisible" :title="title" append-to-body width="750px">
    <div class="tips-container" v-html="failContent" />
    <template #footer>
      <el-button @click="$emit('update:showDialog', false)">取消</el-button>
      <el-button type="primary" @click="submit('receipt')"
        >接受标期并提交</el-button
      >
      <el-button type="primary" @click="submit('refuse')"
        >不接受标期并提交</el-button
      >
    </template>
  </el-dialog>
</template>
