<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useCommonStore } from '@/stores/common';
import { getDisabledDate } from '@/utils/item';
import { setValueIfIncludesZero } from '@/utils/index';
import { DetailOrder } from '@/types/detail';
import type { EditOrder } from '@/types/edit';

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const commonStore = useCommonStore();

const { dictList, orderServiceDict, holidaysList } = storeToRefs(commonStore);

const orderData = inject<Ref<DetailOrder>>('orderData');

const specifiedReceiptDayOfWeek = ref<string[]>(
  (data.value.specifiedReceiptDayOfWeek || '')
    .split(',')
    .filter((item) => !!item)
);

const disabledCustomerReferenceDate = computed(
  () => data.value.autoBatching === 'X'
);
const disabledDate = (time: Date) => {
  const check =
    !['Z002', 'Z014'].includes(orderData?.value.orderType || '') &&
    !['8', 'X'].includes(data.value?.bidCustomer || '');
  return getDisabledDate(
    time,
    specifiedReceiptDayOfWeek.value,
    data?.value?.receiptTimeCategory || '',
    holidaysList.value,
    check
  );
};

const focusDatePicker = () => {
  if (
    data.value.receiptTimeCategory !== 'X' &&
    data.value.specifiedReceiptDayOfWeek?.length &&
    specifiedReceiptDayOfWeek.value?.every(
      (item: string) => !['0', '01', '02', '03', '04', '05'].includes(item)
    )
  ) {
    ElMessage.warning(
      '无可选日期，请修改客户指定日收货或工作日与周末均可收货!'
    );
  }
};

const calOptionDisabled = (field: keyof EditOrder, item: any) => {
  if (
    ((data.value[field] as string) || '').split(',').includes('0') &&
    item.code !== '0'
  ) {
    return true;
  }
  return false;
};

const handleRefDate = (val: string) => {
  data.value?.items?.forEach((item) => {
    item.customerDate = val;
  });
};

const handleChangeSpecifiedReceiptDayOfWeek = (val: string[]) => {
  specifiedReceiptDayOfWeek.value = setValueIfIncludesZero(val);
  data.value.specifiedReceiptDayOfWeek = setValueIfIncludesZero(val).join(',');
};

const changeBidCustomer = (val: string) => {
  if (val !== '13') {
    handleChangeSpecifiedReceiptDayOfWeek([]);
  }
};
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-form-item label="交期条件" prop="bidCustomer">
        <el-select
          v-model="data.bidCustomer"
          placeholder="请选择交期条件"
          @change="changeBidCustomer"
        >
          <el-option
            v-for="item in dictList['noDeliveryReason']?.filter(
              (item) => item.supportQuery !== '0'
            )"
            :key="item.code"
            :disabled="item.status === 'stop'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item>
        <el-checkbox
          v-model="data.receiptTimeCategory"
          true-label="X"
          false-label="Z"
        >
          周末与节假日均可收货
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="客户期望送达日期">
        <el-date-picker
          v-model="data.wholeCustomerReferenceDate"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择日期"
          class="w-full"
          :disabled="disabledCustomerReferenceDate"
          :disabled-date="disabledDate"
          @focus="focusDatePicker"
          @change="handleRefDate"
        />
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item prop="customerAcceptSysDate">
        <el-checkbox
          v-model="data.customerAcceptSysDate"
          true-label="X"
          false-label="Z"
        >
          客户接受标期
        </el-checkbox>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-form-item label="固定送货周期">
        <el-select
          v-model="specifiedReceiptDayOfWeek"
          placeholder="请选择固定送货周期"
          multiple
          :disabled="data.bidCustomer !== '13'"
          @change="handleChangeSpecifiedReceiptDayOfWeek"
        >
          <el-option
            v-for="item in orderServiceDict['specifiedReceiptDayOfWeek']
              ?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('specifiedReceiptDayOfWeek', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item>
        <el-checkbox
          v-model="data.deliverySensitivity"
          true-label="X"
          false-label="Z"
        >
          客户交期敏感
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="交期敏感区间" prop="deliverySensitivityInterval">
        <template #label>
          <div class="flex justify-center items-center">
            <span>交期敏感区间</span>
            <el-tooltip
              effect="dark"
              content="闭区间，左、右区间可分别输入 -14～0、0～14 之间的整数，建议英文逗号分隔"
              placement="top"
            >
              <el-icon class="v-text-bottom"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input
          v-model="data.deliverySensitivityInterval"
          placeholder="请输入交期敏感区间"
        />
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="交期回复时效">
        <el-select
          v-model="data.deliveryReplyTimeliness"
          placeholder="请选择交期回复时效"
          clearable
        >
          <el-option
            v-for="item in dictList['deliveryReplyTimeliness']"
            :key="item.code"
            :disabled="item.status === 'stop'"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
</template>
