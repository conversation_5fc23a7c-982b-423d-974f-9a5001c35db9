<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { FormInstance, FormRules } from 'element-plus';
import { reactive, ref } from 'vue';
import DividerHeader from '@/components/DividerHeader.vue';
import MoreDeliveryOrderInfo from './MoreDeliveryOrderInfo.vue';
import MoreDeliveryDocInfo from './MoreDeliveryDocInfo.vue';
import MoreDeliveryWarehouseInfo from './MoreDeliveryWarehouseInfo.vue';
import MoreDeliveryTransportInfo from './MoreDeliveryTransportInfo.vue';
import type { EditOrder } from '@/types/edit';

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const deliveryFormRef = ref<FormInstance>();
const rules = reactive<FormRules<EditOrder>>({
  deliveryWarehouseInfo: [
    { required: true, message: '请选择送货资料需要仓配信息', trigger: 'blur' },
  ],
  collectionAmount: [
    {
      pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/,
      message: '请输入有效的预收款金额',
      trigger: 'blur',
    },
  ],
});

defineExpose({
  deliveryFormRef,
});
</script>

<template>
  <el-form
    ref="deliveryFormRef"
    :model="data"
    label-width="165px"
    :rules="rules"
  >
    <DividerHeader>订单信息</DividerHeader>
    <MoreDeliveryOrderInfo v-model="data" />
    <DividerHeader>随货文件要求</DividerHeader>
    <MoreDeliveryDocInfo v-model="data" />
    <DividerHeader>仓储要求</DividerHeader>
    <MoreDeliveryWarehouseInfo v-model="data" />
    <DividerHeader>运输要求</DividerHeader>
    <MoreDeliveryTransportInfo v-model="data" />
  </el-form>
</template>
