<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { CheckboxValueType } from 'element-plus';
import type { EditOrder } from '@/types/edit';

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const handlePaidChange = (val: CheckboxValueType) => {
  if (val !== 'X') {
    data.value.tradeNo = '';
  }
};
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox v-model="data.backupOrder" true-label="X" false-label="Z">
          后补订单
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox
          v-model="data.paid"
          true-label="X"
          false-label="Z"
          @change="handlePaidChange"
        >
          已付款
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox
          v-model="data.clearSlackStock"
          true-label="X"
          false-label="Z"
        >
          清呆滞库存
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label-width="0">
        <el-checkbox
          v-model="data.virtualReturn"
          true-label="X"
          false-label="Z"
        >
          不向客户展示
        </el-checkbox>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="付款流水号" prop="tradeNo">
        <el-input v-model="data.tradeNo" placeholder="付款流水号" clearable />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="预收款金额" prop="collectionAmount">
        <el-input v-model="data.collectionAmount" placeholder="预收款金额" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="项目号" prop="projectNo">
        <el-input
          v-model="data.projectNo"
          placeholder="请输入项目号"
          maxlength="40"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="供应商代码">
        <el-input
          v-model="data.supplierAccount"
          placeholder="请输入供应商代码"
          maxlength="10"
          style="width: 100%"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="付款备注" prop="paymentNote">
        <el-input v-model="data.paymentNote" maxlength="50" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="合同备注" prop="agreementNote">
        <el-input v-model="data.agreementNote" maxlength="50" />
      </el-form-item>
    </el-col>
  </el-row>
</template>
