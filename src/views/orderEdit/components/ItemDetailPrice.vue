<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { Ref, computed, inject, ref } from 'vue';
import { useCommonStore } from '@/stores/common';
import { TaxEnum } from '@/types/common';

import { DetailOrder } from '@/types/detail';
import { formatPrice } from '@/utils/index';
import { isFreeOrder } from '@/utils/orderType';
import InputItemPrice from './common/InputItemPrice.vue';
import type { EditItem } from '@/types/edit';

const orderData = inject<Ref<DetailOrder>>('orderData');

type Item = EditItem;

const emit = defineEmits(['update:modelValue', 'changePrice']);
const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);
const props = defineProps<{
  modelValue: Item;
}>();

const data = useVModel(props, 'modelValue', emit);

const isStockClearance = inject<Ref<boolean>>('isStockClearance');

const discountAmount = ref(
  props.modelValue.discountAmount
    ? Number.parseFloat(props.modelValue.discountAmount)
    : 0
);

const isFree = computed(() => {
  return orderData ? isFreeOrder(orderData?.value.orderType) : false;
});

const isTax = computed(() => {
  return orderData?.value?.isTax;
});

const unTaxedTable = computed(() => {
  const { skuFactoryPriceMap, factory } = data.value || {};
  if (skuFactoryPriceMap && factory) {
    const priceMap = skuFactoryPriceMap[factory];
    if (priceMap) {
      const { dealerMinPrice, suggestPrice, taxRate } = priceMap;
      const dealerMinPriceNumber = Number(dealerMinPrice);
      const suggestPriceNumber = Number(suggestPrice);
      const res = [
        {
          name: '建议销售价',
          price: suggestPrice ? formatPrice(suggestPriceNumber) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        },
      ];
      if (orderData?.value?.distributionChannel === '02') {
        res.push({
          name: '最低折扣价',
          price: dealerMinPrice ? formatPrice(dealerMinPriceNumber) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        });
      }
      return res;
    }
  }
  return [];
});

const taxedTable = computed(() => {
  const { skuFactoryPriceMap, factory } = data.value || {};
  if (factory && skuFactoryPriceMap) {
    const priceMap = skuFactoryPriceMap[factory];
    if (priceMap) {
      const { dealerMinPrice, suggestPrice, taxRate } = priceMap;
      const dealerMinPriceNumber = Number(dealerMinPrice);
      const suggestPriceNumber = Number(suggestPrice);
      const res = [
        {
          name: '建议销售价',
          price: suggestPrice ? formatPrice(suggestPriceNumber) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        },
      ];
      if (orderData?.value?.distributionChannel === '02') {
        res.push({
          name: '最低折扣价',
          price: dealerMinPrice ? formatPrice(dealerMinPriceNumber) : '--',
          rate: taxRate ? `${taxRate}%` : '--',
        });
      }
      return res;
    }
  }
  return [];
});

const handlePriceChange = () => {
  emit('changePrice');
};
const handleChangeDiscountAmount = (val: number | undefined) => {
  data.value.discountAmount = val != null ? val.toString() : '';
  handlePriceChange();
};
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="未税单价" prop="freeTaxPrice">
        <InputItemPrice
          v-if="orderData"
          v-model="data"
          :order-data="orderData"
          :tax="TaxEnum.No"
          size="default"
          :disabled="orderData?.isTax === '1' || isFree || !!isStockClearance"
          @change="handlePriceChange"
        />
        <el-dropdown
          v-if="isTax !== '1'"
          trigger="click"
          placement="bottom"
          class="mt-3px"
        >
          <el-link class="link" size="small" :underline="false" type="primary">
            建议价格
          </el-link>
          <template #dropdown>
            <el-dropdown-menu :show-timeout="50">
              <el-dropdown-item style="padding: 0">
                <el-table
                  :data="unTaxedTable"
                  border
                  style="width: 100%"
                  size="small"
                >
                  <el-table-column
                    prop="name"
                    label="价格项"
                    width="150"
                    align="center"
                  />
                  <el-table-column
                    prop="price"
                    label="含税单价"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    prop="rate"
                    label="税率"
                    width="100"
                    align="center"
                  >
                    <!-- <template #header>
                      <RequiredColTitle>税率</RequiredColTitle>
                    </template> -->
                    <template #default="scope">{{ scope.row.rate }}</template>
                  </el-table-column>
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="含税单价" prop="taxPrice">
        <InputItemPrice
          v-if="orderData"
          v-model="data"
          :order-data="orderData"
          :tax="TaxEnum.Yes"
          size="default"
          :disabled="orderData?.isTax !== '1' || isFree || !!isStockClearance"
          @change="handlePriceChange"
        />
        <el-dropdown
          v-if="isTax === '1'"
          trigger="click"
          placement="bottom"
          class="mt-3px"
        >
          <el-link class="link" size="small" :underline="false" type="primary">
            建议价格
          </el-link>
          <template #dropdown>
            <el-dropdown-menu :show-timeout="50">
              <el-dropdown-item style="padding: 0">
                <el-table
                  :data="taxedTable"
                  border
                  style="width: 100%"
                  size="small"
                >
                  <el-table-column
                    prop="name"
                    label="价格项"
                    width="150"
                    align="center"
                  />
                  <el-table-column
                    prop="price"
                    label="含税单价"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    prop="rate"
                    label="税率"
                    width="100"
                    align="center"
                  >
                    <!-- <template #header>
                      <RequiredColTitle>税率</RequiredColTitle>
                    </template> -->
                    <template #default="scope">{{ scope.row.rate }}</template>
                  </el-table-column>
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="商品未税总金额" prop="freeTotalPrice">
        <el-input
          :value="(data.freeTotalPrice || 0).toFixed(6)"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="商品含税总金额" prop="taxTotalPrice">
        <el-input
          :value="(data.taxTotalPrice || 0).toFixed(6)"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="折扣类型">
        <el-select
          :value="data.discountConditionType || ''"
          placeholder="请选择"
          disabled
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="item in dictList['discountConditionType']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="折扣金额">
        <el-input-number
          v-model="discountAmount"
          :precision="6"
          :step="1"
          :min="0"
          style="width: 100%"
          placeholder="请输入折扣金额"
          :disabled="isStockClearance"
          @change="handleChangeDiscountAmount"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>
