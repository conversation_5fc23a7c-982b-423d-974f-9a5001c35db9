<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, ref, watch } from 'vue';
import { useCommonStore } from '@/stores/common';
import { OrderStatus } from '@/constants/order';
import { DetailOrder } from '@/types/detail';
import type { CustomerData } from '@/types/customer';

const commonStore = useCommonStore();

const { dictList } = storeToRefs(commonStore);

const props = defineProps<{
  orderData: DetailOrder | null;
  customerData: CustomerData | null;
}>();

const orderDetail = ref<DetailOrder | null>(props.orderData);
const customerDetail = ref<CustomerData | null>(props.customerData);

const baseInfo: {
  label: string;
  prop: keyof DetailOrder | Extract<keyof CustomerData, 'saleOrgVO'>;
  span: number;
}[][] = [
  [
    {
      label: 'OMS订单号',
      prop: 'soNo',
      span: 6,
    },
    {
      label: 'SAP订单号',
      prop: 'sapOrderNo',
      span: 6,
    },
    {
      label: '订单时间',
      prop: 'orderCreateTime',
      span: 6,
    },
  ],
  [
    {
      label: '订单状态',
      prop: 'orderStatus',
      span: 6,
    },
    {
      label: '直/分销渠道',
      prop: 'saleOrgVO',
      span: 6,
    },
    {
      label: '创建者',
      prop: 'creator',
      span: 6,
    },
  ],
  [
    {
      label: '客服',
      prop: 'customerServiceName',
      span: 6,
    },
    {
      label: '客服主管',
      prop: 'customerServiceSupervisorName',
      span: 6,
    },
    {
      label: '销售',
      prop: 'sellerName',
      span: 6,
    },
    {
      label: '销售主管',
      prop: 'salesManagerName',
      span: 6,
    },
  ],
];

const freezeBillReasonList = computed(() => {
  return orderDetail.value?.billingFreeze
    ? orderDetail.value.billingFreeze.split(',')
    : [];
});

const freezeReasonList = computed(() => {
  return orderDetail.value?.deliveryFreeze
    ? orderDetail.value.deliveryFreeze.split(',')
    : [];
});

const orderStatus = computed(() => {
  if (orderDetail.value && orderDetail.value.orderStatus) {
    const _orderStatus = orderDetail.value.orderStatus;
    const found = OrderStatus.find((s) => s.code === _orderStatus);
    return found ? found.text : '';
  }
  return '';
});

const parseDict = (val: string | undefined, code: string) => {
  const valList = dictList.value?.[code] || [];
  if (valList && Array.isArray(valList) && val) {
    return (valList.find((item) => item.code === val) || {}).name;
  }
  return val;
};

watch(
  () => [props.orderData, props.customerData],
  ([_orderData, _customerData]) => {
    orderDetail.value = _orderData as DetailOrder;
    customerDetail.value = _customerData as CustomerData;
  }
);
</script>

<template>
  <div class="p-10px">
    <div class="mb-20px">
      <span style="font-size: 20px">{{
        parseDict(orderDetail?.salesOrganization, 'salesOrganization')
      }}</span>
      <span style="font-size: 20px; margin-left: 15px">{{
        parseDict(orderDetail?.orderType, 'soCategory')
      }}</span>
    </div>
    <el-row
      v-for="(item, idx) in baseInfo"
      :key="`baseInfo-row${idx}`"
      :gutter="10"
      class="mb-10px"
    >
      <el-col
        v-for="(children, childIdx) in item"
        :key="`baseInfo-row${childIdx}`"
        :span="children.span"
      >
        {{ children.label }}:
        <span v-if="children.prop === 'orderStatus'">
          {{ orderStatus }}
        </span>
        <span v-else-if="children.prop === 'saleOrgVO'" :label="children.label">
          {{ (customerDetail?.[children.prop] || {}).distributionChannelName }}
        </span>
        <span v-else :label="children.label">
          {{ orderDetail?.[children.prop] || '' }}
        </span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="6"> EVM运营: {{ customerDetail?.evmOperator }} </el-col>
      <el-col :span="6">
        开票冻结:
        <el-tag
          v-for="df in freezeBillReasonList"
          :key="`invoiceFreeze${df}`"
          style="margin-right: 10px"
          type="success"
        >
          {{ parseDict(df, 'invoiceFreeze') }}
        </el-tag>
        <el-tag v-if="freezeBillReasonList.length === 0" type="info">无</el-tag>
      </el-col>
      <el-col :span="12">
        交货冻结:
        <el-tag
          v-for="df in freezeReasonList"
          :key="`deliveryFreeze${df}`"
          style="margin-right: 10px"
          type="success"
        >
          {{ parseDict(df, 'deliveryFreeze') }}
        </el-tag>
        <el-tag v-if="freezeReasonList.length === 0" type="info">无</el-tag>
      </el-col>
    </el-row>
  </div>
</template>
