<script setup lang="ts">
import { CheckboxValueType, ElMessageBox } from 'element-plus';
import { useVModel } from '@vueuse/core';
import { computed } from 'vue';
import { useCommonStore } from '@/stores/common';
import type { EditOrder } from '@/types/edit';

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const orderData = useVModel(props, 'modelValue', emit);

const commonStore = useCommonStore();

const isShowCombinedDelivery = computed(
  () => commonStore.isShowCombinedDelivery
);
const dictList = computed(() => commonStore.dictList);
const handleChange = (val: CheckboxValueType) => {
  if (val !== 'X') {
    if (
      orderData.value?.items?.length &&
      orderData.value?.items?.some(
        (item) =>
          item.customerDate !== orderData.value?.items?.[0]?.customerDate
      )
    ) {
      ElMessageBox.confirm(
        '订单发货方式修改为整单发货，请检查和确认是否清空订单行客户期望送达日期并重新维护',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          orderData.value?.items?.forEach((item) => (item.customerDate = ''));
        })
        .catch(() => {
          if (orderData.value) {
            orderData.value.autoBatching = 'X';
          }
        });
    }
  } else if (orderData.value && orderData.value.wholeCustomerReferenceDate) {
    ElMessageBox.confirm(
      '订单发货方式修改为分批发货，请检查和确认客户期望送达日期',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        orderData.value?.items.forEach(
          (item) =>
            (item.customerDate = orderData.value!.wholeCustomerReferenceDate)
        );
      })
      .catch(() => {
        if (orderData.value) {
          orderData.value.autoBatching = 'Z';
        }
      });
  }
};
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-form-item>
        <el-checkbox
          v-model="orderData.autoDelivery"
          true-label="X"
          false-label="Z"
        >
          自动发货
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item>
        <el-checkbox
          v-model="orderData.autoBatching"
          true-label="X"
          false-label="Z"
          @change="handleChange"
        >
          允许分批
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item>
        <el-checkbox
          v-model="orderData.customerDeliveryConfirmed"
          true-label="X"
          false-label="Z"
        >
          发货需与客户确认/预约
        </el-checkbox>
      </el-form-item>
    </el-col>
    <el-col v-if="isShowCombinedDelivery" :span="6">
      <el-form-item label="合单发货" prop="combinedDelivery">
        <el-select v-model="orderData.combinedDelivery" clearable>
          <el-option
            v-for="(item, index) in dictList.CombinedDelivery"
            :key="index"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
</template>
