<script setup lang="ts">
import { Ref, computed, inject, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import { useCommonStore } from '@/stores/common';
import { CustomerData } from '@/types/customer';
import { searchContactListByGroup } from '@/api/order';
import SelectContact from '@/components/SelectContact.vue';
import { DetailOrder } from '@/types/detail';
import { parseDict } from '../utils';
import CustomerDetail from './CustomerDetail.vue';
import type { EditOrder } from '@/types/edit';
import type { ContactType } from '@/types/common';

const commonStore = useCommonStore();

const { dictList } = storeToRefs(commonStore);

const emit = defineEmits(['update:modelValue', 'update:customerData']);

const orderData = inject<Ref<DetailOrder>>('orderData');

const getDeliveryDate = inject<() => void>('updateDeliveryDate');
const queryClient = inject<(data: EditOrder) => CustomerData>('queryClient');

const props = defineProps<{
  modelValue: EditOrder;
  customerData: CustomerData | null;
}>();

const showEditCustomer = ref(false);

const loadingReceiverContact = ref(false);
const loadingInvoiceContact = ref(false);
const loadingOrderContact = ref(false);

const receiverInfo = ref<Partial<ContactType>>({});
const receivingInvoice = ref<Partial<ContactType>>({});
const orderInfo = ref<Partial<ContactType>>({});

const receiverContactList = ref<Partial<ContactType>[]>([]);
const receivingInvoiceList = ref<Partial<ContactType>[]>([]);
const orderContactList = ref<Partial<ContactType>[]>([]);

const data = useVModel(props, 'modelValue', emit);
const customerData = useVModel(props, 'customerData', emit);

const isGBB = computed(
  () =>
    orderData &&
    orderData.value.salesOrganization &&
    orderData.value.salesOrganization.indexOf('13') === 0
);

// 收票联系人 remote
const queryReceivingInvoiceContactList = (contactName: string) => {
  commonQuery(
    contactName,
    'receivingInvoiceContactList',
    data?.value.invoiceReceiver
  );
};
const queryReceiverContactList = (contactName: string) => {
  commonQuery(contactName, 'receiverContactList');
};
const queryOrderContactList = (contactName: string) => {
  commonQuery(contactName, 'orderContactList');
};

const commonQuery = async (
  contactName: string,
  type: string,
  customerCode = orderData?.value?.customerNo
) => {
  const { customerNo, salesOrganization, productGroup, distributionChannel } =
    orderData?.value || {};
  if (customerNo && salesOrganization && productGroup && distributionChannel) {
    if (type === 'receiverContactList') {
      loadingReceiverContact.value = true;
    }
    if (type === 'orderContactList') {
      loadingOrderContact.value = true;
    }
    if (type === 'receivingInvoiceContactList') {
      loadingInvoiceContact.value = true;
    }
    await searchContactListByGroup({
      customerCode,
      contactName,
      distributionChannel,
      productGroup,
      salesOrganization,
    })
      .then((res) => {
        if (res?.code === 200) {
          if (type === 'receiverContactList') {
            receiverContactList.value = res.data.records;
          }
          if (type === 'orderContactList') {
            orderContactList.value = res.data.records;
          }
          if (type === 'receivingInvoiceContactList') {
            receivingInvoiceList.value = res.data.records;
          }
        }
      })
      .finally(() => {
        loadingReceiverContact.value = false;
        loadingOrderContact.value = false;
        loadingInvoiceContact.value = false;
      });
  } else {
    ElMessage.warning({
      message: '请选择销售范围',
    });
  }
};

// 收货联系人
const receiverContactChange = (val: ContactType | undefined) => {
  receiverInfo.value = val || {};
  data.value.receiverContact = Number(val?.contactId) || undefined;
  data.value.receiverPhone = val?.contactPhone;
  data.value.receiverProvinceCode = val?.province;
  data.value.receiverCityCode = val?.city;
  data.value.receiverDistrictCode = val?.region;
  data.value.receiverAddressId = val?.addressId;
  getDeliveryDate && getDeliveryDate();
};
// 订单联系人
const orderContactChange = (val: ContactType | undefined) => {
  orderInfo.value = val || {};
  data.value.orderContact = val?.contactId
    ? Number.parseInt(val?.contactId)
    : undefined;
  data.value.orderContactPhone = val?.contactPhone;
  getDeliveryDate && getDeliveryDate();
};
// 收票联系人
const receivingInvoiceChange = (val: ContactType | undefined) => {
  receivingInvoice.value = val || {};
  data.value.receivingInvoiceContact = val?.contactId
    ? Number.parseInt(val?.contactId)
    : undefined;
  data.value.receivingInvoiceName = val?.contactName || '';
  data.value.receivingInvoicePhone = val?.contactPhone;
  data.value.receivingInvoiceAddress = val?.address;
};

const initContact = () => {
  const {
    receivingInvoiceAddress,
    receivingInvoiceContact,
    receivingInvoiceName,
    receivingInvoicePhone,
    orderContactAddress,
    orderContact,
    orderContactName,
    orderContactPhone,
    receiverAddress,
    receiverContact,
    receiverName,
    receiverPhone,
  } = orderData?.value || {};
  receivingInvoice.value = {
    address: receivingInvoiceAddress || '',
    contactId: receivingInvoiceContact
      ? receivingInvoiceContact.toString()
      : '',
    contactName: receivingInvoiceName || '',
    contactPhone: receivingInvoicePhone || '',
  };
  orderInfo.value = {
    address: orderContactAddress || '',
    contactId: orderContact?.toString() || '',
    contactName: orderContactName || '',
    contactPhone: orderContactPhone || '',
  };
  receiverInfo.value = {
    address: receiverAddress || '',
    contactId: receiverContact?.toString() || '',
    contactName: receiverName || '',
    contactPhone: receiverPhone || '',
  };
  commonQuery(
    '',
    'receivingInvoiceContactList',
    orderData?.value.invoiceReceiver
  ).then(() => {
    receivingInvoiceList.value = [
      receivingInvoice.value,
      ...receivingInvoiceList.value.filter(
        (item) =>
          receivingInvoiceContact &&
          Number(item.contactId) !== data.value.receivingInvoiceContact
      ),
    ];
  });
  commonQuery('', 'orderContactList', orderData?.value.customerNo).then(() => {
    orderContactList.value = [
      orderInfo.value,
      ...orderContactList.value.filter(
        (item) =>
          orderContact && Number(item.contactId) !== data.value.orderContact
      ),
    ];
  });
  commonQuery('', 'receiverContactList', orderData?.value.customerNo).then(
    () => {
      receiverContactList.value = [
        receiverInfo.value,
        ...receiverContactList.value.filter(
          (item) =>
            receiverContact && Number(item.contactId) !== receiverContact
        ),
      ];
    }
  );
};
// 客户详情 update 事件
const handleUpdateCustomerDetail = async (val: EditOrder) => {
  const ifChangeInvoiceReceiver =
    data.value.invoiceReceiver !== val.invoiceReceiver ||
    data.value.invoiceReceiverName !== val.invoiceReceiverName;
  data.value = val;
  if (ifChangeInvoiceReceiver) {
    commonQuery('', 'receivingInvoiceContactList', val.invoiceReceiver || '');
    receivingInvoice.value = val.receivingInvoice || {};
    data.value = {
      ...data.value,
      ...val,
      paymentTerm: val.paymentTerm,
      invoiceReceiver: val.invoiceReceiver,
      invoiceReceiverName: val.invoiceReceiverName,
      receivingInvoiceContact: val.receivingInvoice?.contactId
        ? Number.parseInt(val.receivingInvoice.contactId)
        : undefined,
      receivingInvoiceName: val.receivingInvoice?.contactName,
      receivingInvoicePhone: val.receivingInvoice?.contactPhone,
      receivingInvoiceAddress: val.receivingInvoice?.address,
    };
    if (queryClient) {
      const res = await queryClient(data.value);
      const {
        corporationTaxNum,
        billingAddressAndPhone,
        bankName,
        bankNumber,
        customerPayAccountTypeName,
        shippingInfo,
        invoiceType,
        mergeBillingDemand,
        autoBilling,
        financialNote,
      } = res;
      const {
        customerSourceName,
        businessPartnerGroupName,
        customerClassificationName,
        customerNatureName,
        evmOperator,
      } = customerData.value as CustomerData;
      customerData.value = {
        ...res,
        customerSourceName,
        businessPartnerGroupName,
        customerClassificationName,
        customerNatureName,
        evmOperator,
      };
      data.value = {
        ...data.value,
        corporationTaxNum,
        billingAddressAndPhone,
        bankName,
        bankNumber,
        customerPayAccountTypeName,
        shippingInfo,
        invoiceType,
        mergeBillingDemand,
        autoBilling,
        financialNote,
      };
    }
  }
};

watch(
  () => data.value.receivingInvoiceContact,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      const {
        receivingInvoiceAddress,
        receivingInvoiceContact,
        receivingInvoiceName,
        receivingInvoicePhone,
      } = data.value || {};
      receivingInvoice.value = {
        address: receivingInvoiceAddress || '',
        contactId: receivingInvoiceContact
          ? receivingInvoiceContact.toString()
          : '',
        contactName: receivingInvoiceName || '',
        contactPhone: receivingInvoicePhone || '',
      };
    }
  }
);

onMounted(() => {
  initContact();
});
</script>

<template>
  <div v-if="orderData" class="flex items-center justify-between mb-20px">
    <div class="flex items-center">
      <span class="text-20px mr-20px">{{ orderData?.customerNo || '' }}</span>
      <span class="text-20px mr-20px">{{ orderData?.customerName || '' }}</span>
      <span>
        <el-tag
          v-if="customerData && customerData.businessPartnerGroupName"
          effect="dark"
          type=""
          class="mr-10px"
        >
          {{ customerData.businessPartnerGroupName }}
        </el-tag>
        <el-tag
          v-if="customerData && customerData.customerClassificationName"
          effect="dark"
          type="success"
          class="mr-10px"
        >
          {{ customerData.customerClassificationName }}
        </el-tag>
        <el-tag
          v-if="customerData && customerData.customerNatureName"
          effect="dark"
          type="warning"
        >
          {{ customerData.customerNatureName }}
        </el-tag>
      </span>
    </div>
    <el-button text type="primary" @click="showEditCustomer = true"
      >修改客户详情&gt;&gt;</el-button
    >
  </div>
  <el-row v-if="orderData" :gutter="10" class="mb-10px">
    <el-col :span="12">
      销售范围：{{
        `${orderData.salesOrganization}/${orderData.distributionChannel}/${orderData.productGroup}`
      }}&nbsp;
      {{
        parseDict(dictList, 'salesOrganization', orderData.salesOrganization)
      }}，{{
        parseDict(
          dictList,
          'distributionChannel',
          orderData.distributionChannel
        )
      }}，{{ parseDict(dictList, 'productGroup', orderData.productGroup) }}
    </el-col>
    <el-col :span="12">
      客户订单号：{{ orderData.customerReferenceNo }}
    </el-col>
  </el-row>
  <el-row v-if="orderData" :gutter="10" class="mb-10px">
    <el-col :span="6"
      >客户来源：{{
        customerData ? customerData.customerSourceName : ''
      }}</el-col
    >
    <el-col :span="6"
      >付款条件：{{
        parseDict(dictList, 'paymentTerms', orderData.paymentTerm)
      }}</el-col
    >
    <el-col :span="4"
      >含税/未税：{{ orderData.isTax === '0' ? '未税' : '含税' }}</el-col
    >
    <el-col v-if="isGBB" :span="8" style="margin-top: -5px">
      <el-form-item prop="customerReferenceDate" label="客户参考日期">
        <el-date-picker
          v-model="data.customerReferenceDate"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择日期"
          style="width: 100%"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20" class="mt-10px">
    <el-col :span="6">
      <el-form-item label="收货联系人" prop="receiverContact">
        <SelectContact
          v-model="receiverInfo"
          title="收货联系人"
          :contact-list="receiverContactList as ContactType[]"
          :loading="loadingReceiverContact"
          :remote-method="queryReceiverContactList"
          @change="receiverContactChange"
        />
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="收货人电话" prop="receiverPhone">
        <el-input
          :value="receiverInfo.contactPhone || ''"
          placeholder="请输入收货人电话"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="收货地址" prop="receiverAddress">
        <el-input
          :value="receiverInfo.address || ''"
          placeholder="请输入收货地址"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20" class="">
    <el-col :span="6">
      <el-form-item label="收票联系人" prop="receivingInvoiceContact">
        <SelectContact
          v-model="receivingInvoice"
          title="收票联系人"
          :contact-list="receivingInvoiceList as ContactType[]"
          :loading="loadingInvoiceContact"
          :remote-method="queryReceivingInvoiceContactList"
          @change="receivingInvoiceChange"
        />
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="收票人电话" prop="data.receivingInvoicePhone">
        <el-input
          :value="receivingInvoice.contactPhone || ''"
          placeholder="请输入收票人电话"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="收票地址" prop="receivingInvoiceAddress">
        <el-input
          :value="receivingInvoice.address || ''"
          placeholder="请输入收票地址"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="20" class="">
    <el-col :span="6">
      <el-form-item label="订单联系人" prop="orderContact">
        <SelectContact
          v-model="orderInfo"
          title="订单联系人"
          :contact-list="orderContactList as ContactType[]"
          :loading="loadingOrderContact"
          :remote-method="queryOrderContactList"
          @change="orderContactChange"
        />
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="订单人电话" prop="data.orderContactPhone">
        <el-input
          :value="orderInfo.contactPhone || ''"
          placeholder="请输入订单人电话"
          :disabled="true"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-dialog
    v-if="showEditCustomer"
    v-model="showEditCustomer"
    title="客户详情"
    :show-close="false"
    width="1000px"
  >
    <CustomerDetail
      :data="data"
      :customer-data="customerData"
      @close="() => (showEditCustomer = false)"
      @update="handleUpdateCustomerDetail"
    />
  </el-dialog>
</template>
