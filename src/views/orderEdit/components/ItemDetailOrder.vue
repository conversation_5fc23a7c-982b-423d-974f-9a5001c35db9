<script setup lang="ts">
import { Ref, computed, inject, onMounted, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { cloneDeep } from 'lodash';
import { useCommonStore } from '@/stores/common';
import customerMaterialTable from '@/components/CustomerMaterialTable.vue';
import { MaterialRelationRow, SearchItemType } from '@/types/item';
import {
  accurateQuery,
  getSkuDetail,
  searchCustomerMaterialRelation,
} from '@/api/order';
import { DetailOrder } from '@/types/detail';
import { formatPrice } from '@/utils';
import SelectSkus from '@/components/SelectSkus.vue';
import { formatSKU, getSimPosition } from '@/utils/edit';
import { isDiffCustomerRelation } from '@/utils/item';
import { SimPositionType, SupplyPosition } from '@/types/position';
import DiffCustomerMaterial from '@/components/DiffCustomerMaterial.vue';
import { CustomerData } from '@/types/customer';
import SelectItemQuantity from './common/SelectItemQuantity.vue';
import type { EditItem } from '@/types/edit';

type Item = EditItem;

const emit = defineEmits(['update:modelValue', 'changePrice']);
const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);

const props = defineProps<{
  modelValue: Item;
}>();

const { simPositionList, simDeliveryList, updatePositionList } =
  inject<{
    simPositionList: Ref<SupplyPosition[]>;
    simDeliveryList: Ref<SupplyPosition[]>;
    updatePositionList: (
      type: SimPositionType,
      posList: SupplyPosition[]
    ) => void;
  }>('sim') || {};
const orderData = inject<Ref<DetailOrder>>('orderData');
const isStockClearance = inject<Ref<boolean>>('isStockClearance');
const cusDetail = inject<Ref<CustomerData>>('customerData');

const data = useVModel(props, 'modelValue', emit);

const customerMaterialTableData = ref([]);
const rate = ref('');

const formatedSku = ref<EditItem>();
const oldSku = ref<EditItem>(); // 记录上一次的sku
const resultMaterialObj = ref(); // 查出来的客户物料关系
const inputMaterialObj = ref(); // 页面手动输入的客户物料关系
const showDiffDialog = ref(false); // 比较客户物料关系的弹窗
const selectedSku = computed(() => {
  return {
    ...data.value,
    skuNo: data.value?.materiel,
    materialDescribe: data.value?.sapMaterialName,
    customerSkuNo: data.value?.customerMaterialNo,
    customerSkuName: data.value?.customerMaterialName,
    customerSkuUnitCount: data.value?.customerSkuUnitCount,
    customerSkuUnit: data.value?.customerMaterialUnit,
    customerSkuSpecification: data.value?.customerSpecificationModel,
    index: 1,
  };
});

const initRate = () => {
  const { skuUnitCount, customerSkuUnitCount } = data.value;
  if (
    skuUnitCount &&
    customerSkuUnitCount &&
    skuUnitCount !== 0 &&
    customerSkuUnitCount !== 0
  ) {
    rate.value = formatPrice(customerSkuUnitCount / skuUnitCount, 6);
  }
  if (!data.value.customerMaterialQuantity) {
    data.value.customerMaterialQuantity = Number.parseFloat(
      formatPrice(data.value.quantity * Number.parseFloat(rate.value), 6)
    );
  }
};

const handleDiffCustomerRelation = (result: EditItem, detail: EditItem) => {
  resultMaterialObj.value = {
    customerMaterialNo: result.customerMaterialNo,
    customerMaterialName: result.customerMaterialName,
    customerMaterialUnit: result.customerMaterialUnit,
    customerSpecificationModel: result.customerSpecificationModel,
  };
  inputMaterialObj.value = {
    customerMaterialNo: detail.customerMaterialNo,
    customerMaterialName: detail.customerMaterialName,
    customerMaterialUnit: detail.customerMaterialUnit,
    customerSpecificationModel: detail.customerSpecificationModel,
  };
  return isDiffCustomerRelation(
    resultMaterialObj.value,
    inputMaterialObj.value
  );
};

const handleSku = async () => {
  data.value = {
    ...data.value,
    ...formatedSku.value,
    quantity: data.value?.quantity || formatedSku.value?.quantity || 0,
    customerMaterialQuantity:
      data.value?.customerMaterialQuantity ||
      formatedSku.value?.customerMaterialQuantity,
    freeTaxPrice:
      data.value?.freeTaxPrice || formatedSku.value?.freeTaxPrice || 0,
    taxPrice: data.value?.taxPrice || formatedSku.value?.taxPrice || 0,
    validateItemNo: data.value?.validateItemNo,
  };
  emit('changePrice');
  // if (orderData?.value?.isTax === '1') {
  //   data.value.freeTotalPrice = formatPrice(
  //     data.value.freeTaxPrice * data.value.quantity
  //   );
  // } else {
  //   data.value.taxTotalPrice = formatPrice(
  //     data.value.taxPrice * data.value.quantity
  //   );
  // }
  const factorySet: string[] = [];
  const skuSet: string[] = [];
  const { materiel, factory } = data.value;
  if (materiel && factory) {
    const found1 = (simPositionList?.value || []).some(
      (pos) => pos.sku === materiel && pos.factory === factory
    );
    const found2 = (simDeliveryList?.value || []).some(
      (pos) => pos.sku === materiel && pos.factory === factory
    );
    if (!found1 && !found2) {
      factorySet.push(factory);
      skuSet.push(materiel);
    }
  }
  if (factorySet.length > 0 && skuSet.length > 0 && updatePositionList) {
    const [list1, list2] = await Promise.all([
      getSimPosition(factorySet, skuSet, 1),
      getSimPosition(factorySet, skuSet, 3),
    ]);
    updatePositionList('position', list1);
    updatePositionList('delivery', list2);
  }
  initRate();
  searchMaterialRelation();
};

const handleDiff = () => {
  const isDiff = handleDiffCustomerRelation(
    formatedSku.value as EditItem,
    data.value
  );
  if (isDiff) {
    showDiffDialog.value = true;
  } else {
    handleSku();
  }
};

const handleDiffMaterialRelation = async (type: string, obj: EditItem) => {
  try {
    // 取消：cancel，覆盖：yes，不覆盖：no
    if (type === 'cancel') {
      data.value.materiel = oldSku.value?.materiel || '';
    } else {
      await handleSku();
      data.value.customerMaterialNo = obj.customerMaterialNo;
      data.value.customerSpecificationModel = obj.customerSpecificationModel;
      data.value.customerMaterialName = obj.customerMaterialName;
      data.value.customerMaterialUnit = obj.customerMaterialUnit;
    }
    showDiffDialog.value = false;
  } catch (error) {
    console.log(error);
  }
};

const changeSkuInTable = async (currentSelectSku: SearchItemType) => {
  if (!currentSelectSku.skuNo) return;
  const {
    salesOrganization,
    distributionChannel,
    productGroup,
    orderType,
    customerNo,
  } = orderData?.value || {};
  const params = {
    salesOrganization,
    distributionChannel,
    productGroup,
    orderType,
    customerNo,
  };
  const res = await getSkuDetail(currentSelectSku.skuNo, params);
  if (res.code === 200 && orderData) {
    const detail = res.data;
    oldSku.value = cloneDeep(data.value);
    data.value.materiel = currentSelectSku.skuNo;
    formatedSku.value = await formatSKU(
      currentSelectSku,
      detail,
      orderData.value,
      dictList.value,
      cusDetail?.value
    );
    if (currentSelectSku.dataSource !== '商品中心') {
      handleDiff();
    } else {
      // 来源为商品中心，查询是否有客户物料关系，有则比较不同，没有则按不覆盖处理
      const queryData = {
        sku: currentSelectSku.skuNo,
        customerCode: customerNo,
      };
      const res = await accurateQuery(queryData);
      if (res?.data.length) {
        handleDiff();
      } else {
        await handleSku();
        data.value.customerMaterialNo = oldSku.value.customerMaterialNo;
        data.value.customerSpecificationModel =
          oldSku.value.customerSpecificationModel;
        data.value.customerMaterialName = oldSku.value.customerMaterialName;
        data.value.customerMaterialUnit = oldSku.value.customerMaterialUnit;
      }
    }
  }
};
// "多" 选择事件
const handleMaterialClick = (item: MaterialRelationRow) => {
  data.value.customerMaterialNo = item.customerMaterialNo;
  data.value.customerSpecificationModel = item.customerMaterialSpecification;
  data.value.customerMaterialName = item.customerMaterialName;
  data.value.customerMaterialUnit = item.customerMaterialUnit;
  if (
    item.customerMaterialStandardQuantity &&
    item.zkhSkuStandardQuantity &&
    Number.parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
    Number.parseFloat(item.zkhSkuStandardQuantity) !== 0
  ) {
    rate.value = formatPrice(
      Number.parseFloat(item.customerMaterialStandardQuantity) /
        Number.parseFloat(item.zkhSkuStandardQuantity),
      6
    );
    data.value.customerMaterialQuantity =
      data.value.quantity * Number.parseFloat(rate.value);
  } else {
    rate.value = '';
  }
};

const searchMaterialRelation = async () => {
  try {
    const { materiel } = data.value;
    const { customerNo } = orderData?.value || {};
    const query = [
      {
        customerCode: customerNo,
        zkhSkuNo: materiel,
      },
    ];
    const res = await searchCustomerMaterialRelation({ queryList: query });
    if (res.code === 200) {
      if (Array.isArray(res.data) && res.data.length > 0) {
        customerMaterialTableData.value = res.data;
      } else {
        customerMaterialTableData.value = [];
      }
    } else {
      customerMaterialTableData.value = [];
    }
  } catch (error) {
    console.log(error);
  }
};

const handlePriceChange = () => {
  emit('changePrice');
};
onMounted(() => {
  initRate();
  searchMaterialRelation();
});
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="客户物料号" prop="customerMaterialNo">
        <template v-if="customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户物料号</span>
            <customerMaterialTable
              :table-list="customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-input
          v-model="data.customerMaterialNo"
          placeholder="请输入客户物料号"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="客户行号" prop="customerOrderNo">
        <el-input v-model="data.customerOrderNo" :disabled="isStockClearance" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="制造商型号" prop="manufacturerNo">
        <el-input
          v-model="data.manufacturerNo"
          disabled
          placeholder="请输入制造商型号"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="客户规格型号" prop="customerSpecificationModel">
        <template v-if="customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户规格型号</span>
            <customerMaterialTable
              :table-list="customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-input
          v-model="data.customerSpecificationModel"
          placeholder="请输入客户规格型号"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="商品编号">
        <SelectSkus
          :disabled="data.addType !== '1'"
          :selected-sku="selectedSku"
          :customer-no="orderData?.customerNo"
          :customer-material-no="data.customerMaterialNo"
          @handle-select-sku="changeSkuInTable"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="商品描述" prop="sapMaterialName">
        <el-input v-model="data.sapMaterialName" :disabled="true" />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="客户物料名称" prop="customerMaterialName">
        <template v-if="customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户物料名称</span>
            <customerMaterialTable
              :table-list="customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-input
          v-model="data.customerMaterialName"
          placeholder="请输入客户物料名称"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="数量" prop="quantity">
        <SelectItemQuantity
          v-model="data"
          :order-data="orderData"
          size="default"
          @change-amount="handlePriceChange"
        />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="客户物料数量" prop="customerMaterialQuantity">
        <template #label>
          <div class="label">
            <span>客户物料数量</span>
            <customerMaterialTable
              v-if="customerMaterialTableData.length > 1"
              :table-list="customerMaterialTableData"
              @change="handleMaterialClick"
            />
            <el-tooltip
              v-if="!!rate"
              :content="`当前商品数量与客户物料数量的比例为${rate}`"
              effect="dark"
              placement="top"
            >
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
        </template>
        <el-input-number
          v-model="data.customerMaterialQuantity"
          style="width: 100%"
          :min="0"
          :precision="6"
          :step="1"
          :disabled="isStockClearance"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="单位" prop="quantityUnit">
        <el-select
          v-model="data.quantityUnit"
          placeholder="请选择单位"
          disabled
          style="width: 100%"
        >
          <el-option
            v-for="item in data.packageInfoList"
            :key="item.unit"
            :label="item.unitName"
            :value="item.unit"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="客户物料单位" prop="customerMaterialUnit">
        <template v-if="customerMaterialTableData.length > 1" #label>
          <div class="label">
            <span>客户物料单位</span>
            <customerMaterialTable
              :table-list="customerMaterialTableData"
              @change="handleMaterialClick"
            />
          </div>
        </template>
        <el-select
          v-model="data.customerMaterialUnit"
          placeholder="请选择"
          :disabled="isStockClearance"
          style="width: 100%"
          clearable
          filterable
          allow-create
        >
          <el-option
            v-for="item in dictList['customerQuantityUnit']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <DiffCustomerMaterial
      v-model:show-dialog="showDiffDialog"
      :result-material-obj="resultMaterialObj"
      :input-material-obj="inputMaterialObj"
      @submit="handleDiffMaterialRelation"
    />
  </el-row>
</template>

<style scoped lang="scss">
.label {
  position: relative;
}
</style>
