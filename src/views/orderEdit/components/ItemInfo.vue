<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject, onMounted, ref } from 'vue';
import qs from 'qs';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { initGoodsList } from '@/utils/edit';
import { findNormalItemInDictList } from '@/utils/item';
import { useCommonStore } from '@/stores/common';
import { DetailOrder } from '@/types/detail';
import { isAddSkuBtnDisplay } from '@/api/order';
import OrderPendingDialog from '@/components/OrderPendingDialog.vue';
import { getButtonAuth } from '@/utils/auth';
import ItemSelect from './ItemSelect.vue';
import ItemCurrency from './ItemCurrency.vue';
import ItemTable from './ItemTable.vue';
import type { EditItem, EditOrder } from '@/types/edit';

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const orderData = inject<Ref<DetailOrder>>('orderData');

const getDeliveryDate = inject<() => void>('updateDeliveryDate');

const totalPrice = inject<{ update: () => void }>('totalPrice');

const data = useVModel(props, 'modelValue', emit);

const commonStore = useCommonStore();
const { dictList, acceptFileType } = storeToRefs(commonStore);

const loading = ref(false);
const downloadTemplate = () => {
  const { soNo } = data.value;
  if (soNo) {
    window.open(`/api-opc/v1/excel/batchDownloadSku?soNo=${soNo}`, '_blank');
  }
};

const uploadQueryUrl = computed(() => {
  const {
    salesOrganization,
    distributionChannel,
    productGroup,
    customerNo,
    orderType,
    soNo,
  } = orderData?.value || {};
  let url = '';
  if (salesOrganization && distributionChannel && productGroup && customerNo) {
    const params = {
      customerNo,
      salesOrganization,
      distributionChannel,
      productGroup,
      orderType,
      soNo,
    };
    const queryStr = qs.stringify(params);
    url += queryStr;
  }
  return url;
});

const handleBeforeUpload = () => {
  loading.value = true;
};

const handleUploadSuccess = (res: any) => {
  loading.value = false;
  if (res && res.code === 200 && orderData) {
    const { updateWay, itemVOS } = res.data;
    const items = initGoodsList(itemVOS, dictList.value);
    data.value.items = items as EditItem[];
    data.value.updateWay = updateWay;
    totalPrice?.update();
    getDeliveryDate && getDeliveryDate();
  } else {
    ElMessage.error(res.msg || '批量修改失败');
  }
};

const itemTable = ref();
const showDetailDlg = (sku: EditItem) => {
  itemTable.value?.handleDetail(sku);
};

const showButton = (buttonName?: string) => {
  return getButtonAuth('销售跟单', `修改_${buttonName}`);
};

const showAddSkuBtn = ref(false);
const handleAddSkuBtnDisplay = () => {
  if (orderData) {
    const params = {
      orderType: orderData.value?.orderType || '',
      orderSource: orderData.value?.orderSource || '',
      orderBasis: orderData.value?.orderBasis || '',
    };
    isAddSkuBtnDisplay(params).then((res) => {
      if (res?.code === 200) {
        showAddSkuBtn.value = res.data;
      }
    });
  }
};

const selectedItems = ref<EditItem[]>([]);
const handleCheckboxChange = () => {
  const $table = itemTable.value?.itemGrid;
  if ($table) {
    selectedItems.value = $table.getCheckboxRecords();
  }
};

const showOrderPendingDialog = ref(false);
const pendingType = ref('');
const handlePendingDialog = (type: string) => {
  showOrderPendingDialog.value = true;
  pendingType.value = type;
};

// 某一商品行存在挂起原因
const hasPendingReason = computed(() => {
  return selectedItems.value?.some((item) => {
    const hasNotAcceptDemandReason = item.notAcceptDemandReasons
      ?.split(',')
      .some((code) =>
        findNormalItemInDictList(dictList.value.notAcceptDemandReason, code)
      );
    const hasDnOrderPendingReason = item.dnOrderPendingReasons
      ?.split(',')
      .some((code) =>
        findNormalItemInDictList(dictList.value.dnOrderPendingReason, code)
      );
    return hasNotAcceptDemandReason || hasDnOrderPendingReason;
  });
});

const checkedNotAcceptDemandReasons = computed(() => {
  const list = selectedItems.value.flatMap((item) =>
    item.notAcceptDemandReasons ? item.notAcceptDemandReasons.split(',') : []
  );
  return Array.from(new Set(list));
});
const checkedDnOrderPendingReasons = computed(() => {
  const list = selectedItems.value.flatMap((item) =>
    item.dnOrderPendingReasons ? item.dnOrderPendingReasons.split(',') : []
  );
  return Array.from(new Set(list));
});

const handlePendingSubmit = (
  notAcceptDemandReasons: string[],
  dnOrderPendingReasons: string[]
) => {
  selectedItems.value.forEach((item) => {
    const found = data.value?.items?.find((sku) => {
      if (sku.validateItemNo) {
        return sku.validateItemNo === item.validateItemNo;
      }
      return sku.soItemNo === item.soItemNo;
    });
    if (found) {
      const _notAcceptDemandReasons =
        found.notAcceptDemandReasons?.split(',')?.filter(Boolean) || [];
      const _dnOrderPendingReasons =
        found.dnOrderPendingReasons?.split(',')?.filter(Boolean) || [];
      if (pendingType.value === 'pending') {
        found.notAcceptDemandReasons = Array.from(
          new Set(_notAcceptDemandReasons.concat(notAcceptDemandReasons))
        )?.join(',');
        found.dnOrderPendingReasons = Array.from(
          new Set(_dnOrderPendingReasons.concat(dnOrderPendingReasons))
        )?.join(',');
      } else {
        found.notAcceptDemandReasons = _notAcceptDemandReasons
          .filter((item: string) => {
            return !notAcceptDemandReasons.includes(item);
          })
          ?.join(',');
        found.dnOrderPendingReasons = _dnOrderPendingReasons
          .filter((item: string) => {
            return !dnOrderPendingReasons.includes(item);
          })
          ?.join(',');
      }
    }
  });
};

onMounted(() => {
  // 是否展示新增行按钮
  handleAddSkuBtnDisplay();
});
</script>

<template>
  <div class="flex">
    <div class="w-50%">
      <ItemSelect
        v-if="showAddSkuBtn"
        :data="data"
        @show-detail-dlg="showDetailDlg"
      />
    </div>
  </div>
  <div class="flex justify-between items-center">
    <div>
      <ItemCurrency :data="data" />
    </div>
    <div class="flex">
      <div
        v-if="/z001|z006|z007|z012/gim.test(orderData?.orderType || '')"
        class="flex mr-12px"
      >
        <el-button
          v-if="showButton('订单挂起')"
          type="primary"
          :disabled="selectedItems.length === 0"
          @click="handlePendingDialog('pending')"
        >
          订单挂起
        </el-button>
        <el-button
          v-if="showButton('取消挂起')"
          type="primary"
          :disabled="selectedItems.length === 0 || !hasPendingReason"
          @click="handlePendingDialog('cancel')"
        >
          取消挂起
        </el-button>
      </div>
      <div
        v-if="orderData?.orderType === 'Z001' && orderData?.updateByExcelSwitch"
        class="flex"
      >
        <el-button
          v-if="showButton('下载批量修改模板')"
          type="primary"
          plain
          style="margin-right: 12px"
          @click="downloadTemplate"
          >下载批量修改模板</el-button
        >
        <el-upload
          :action="`/api-opc/v1/excel/batchUpdateSku?${uploadQueryUrl}`"
          :disabled="false"
          :multiple="false"
          :accept="acceptFileType.soCommonType"
          :limit="1"
          :show-file-list="true"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
        >
          <el-button
            v-if="showButton('上传批量修改模板')"
            v-loading.fullscreen.lock="loading"
            type="primary"
            >上传批量修改模板</el-button
          >
        </el-upload>
      </div>
    </div>
  </div>
  <ItemTable
    ref="itemTable"
    v-model="data"
    @checkbox-change="handleCheckboxChange"
  />
  <OrderPendingDialog
    v-if="showOrderPendingDialog"
    v-model:show-dialog="showOrderPendingDialog"
    :pending-type="pendingType"
    :checked-not-accept-demand-reasons="checkedNotAcceptDemandReasons"
    :checked-dn-order-pending-reasons="checkedDnOrderPendingReasons"
    @submit="handlePendingSubmit"
  />
</template>
