<script setup lang="ts">
import { Ref, computed, inject, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { useCommonStore } from '@/stores/common';
import { DetailOrder } from '@/types/detail';
import { from814OrderBasis } from '@/constants/order';
import request from '@/utils/request';
import { getDefaultDirectDeliverySupplier } from '@/utils/item';
import type { SalesDictType } from '@/types/common';
import type { EditOrder } from '@/types/edit';

const commonStore = useCommonStore();

const { dictList } = storeToRefs(commonStore);

const orderData = inject<Ref<DetailOrder>>('orderData');
const isShowSapReturnDnNo = inject('isShowSapReturnDnNo');
const checkSelectWhsAuth = inject<(params: any) => void>('checkSelectWhsAuth');
const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const orderReasonList = computed(() => {
  const orList = dictList.value?.orderReason || [];
  let options = orList;
  const orderType = orderData?.value.orderType;
  if (orderType) {
    const typeOptions = orList.filter((or) => or.parentCode === orderType);
    if (typeOptions.length > 0) {
      options = typeOptions;
    } else {
      options = orList.filter((or) => or.parentCode === '');
    }
  }
  // Z007 兼容历史数据
  if (orList && orderType === 'Z007') {
    const finalList: SalesDictType[] = [];
    for (const item of orList) {
      if (!finalList.some((fItem) => fItem.code === item.code)) {
        finalList.push(item);
      } else {
        const findIndex = finalList.findIndex(
          (fItem) => fItem.code === item.code
        );
        if (item.parentCode === 'Z007') {
          finalList.splice(findIndex, 1);
          finalList.push(item);
        }
      }
    }
    return [
      ...finalList.filter((e) => e.status !== 'stop'),
      ...finalList.filter((e) => e.status === 'stop'),
    ];
  }
  return options;
});

// 白名单用户可以选择对账差异调整
const administrator = ref(false);
const getPermission = async () => {
  const res = await request({
    url: '/api-opc/v1/so/template/so/create/modify/invoice/receiver/permission',
    method: 'get',
  });
  if (res.code === 200 && res.data === 'success') {
    administrator.value = true;
  } else {
    administrator.value = false;
  }
};
const disabledHistory = (item: SalesDictType) => {
  const orderType = orderData?.value.orderType;
  if (orderType === 'Z006') {
    return item.parentCode !== 'Z006' || item.status === 'stop';
  }
  if (administrator.value && item.code === '038') return false;
  if (orderType === 'Z007') {
    return item.parentCode !== 'Z007' || item.status === 'stop';
  }
  return item.status === 'stop' || item.code === '055';
};

const isSAP814 = computed(() =>
  from814OrderBasis.includes(data.value?.orderBasis || '')
);

watch(
  () => data.value.orderReason,
  (newVal, preVal) => {
    if (orderData && checkSelectWhsAuth) {
      checkSelectWhsAuth({
        customerNo: orderData.value?.customerNo,
        orderBasis: orderData.value?.orderBasis,
        orderReason: newVal,
        orderType: orderData.value?.orderType,
        salesOrg: orderData.value?.salesOrganization,
        orderSource: orderData.value?.orderSource,
        addItem: true,
      });
      if (preVal === '038' && newVal !== '038') {
        data.value.items
          .filter((item) => item.addType === '1')
          .forEach((item) => {
            item.directDeliverySupplier = getDefaultDirectDeliverySupplier(
              orderData.value?.salesOrganization,
              orderData.value?.orderType,
              dictList.value
            );
            item.position = item.directDeliverySupplier === '2' ? '-1' : '';
          });
      }
    }
  }
);

onMounted(() => {
  getPermission();
});
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="创建人" prop="creator">
        <el-input v-model="data.creator" disabled />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="订单原因" prop="orderReason">
        <el-select
          v-model="data.orderReason"
          placeholder="请选择订单原因"
          clearable
          filterable
          style="width: 100%"
          :disabled="isSAP814"
        >
          <el-option
            v-for="item in orderReasonList"
            :key="`${item.code}-${item.label}-${item.parentCode}`"
            :disabled="disabledHistory(item)"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item
        v-if="isShowSapReturnDnNo"
        label="sap退货交货单"
        prop="sapReturnDnNo"
      >
        <el-input
          v-model="data.sapReturnDnNo"
          placeholder="请输入814单号"
          :disabled="!orderData?.sapReturnDnNoCanModify"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-form-item label="订单备注" prop="orderNote">
        <el-input
          v-model="data.orderNote"
          type="textarea"
          placeholder="订单备注"
        />
      </el-form-item>
    </el-col>
  </el-row>
</template>
