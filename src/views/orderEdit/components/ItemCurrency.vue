<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { Ref, computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import type { OrderTotalAount } from '@/types/order';
import type { EditOrder } from '@/types/edit';

// const symbolMap = {
//   USD: '$',
//   CNY: '￥',
//   EUR: '€',
//   MYR: 'RM',
//   THB: '฿',
//   VND: '₫',
//   SGD: 'S$',
//   TWD: 'NT$',
//   HKD: 'HK$',
// } as const;

// type CurrencySymbol = keyof typeof symbolMap;

const props = defineProps<{
  data: EditOrder;
}>();

const data = useVModel(props, 'data');

const commonStore = useCommonStore();
const { dictList } = storeToRefs(commonStore);

const currency = computed(() => {
  const found = (data.value.items || []).find((item) => item.currency);
  return found ? found.currency : '';
});
const currencySymbol = computed(() => {
  const found = dictList.value?.currencySymbol?.find(
    (item) => item.parentCode === currency.value
  );
  if (found) {
    return found.code;
  }
  return '';
});

const totalPrice = inject<{ total: Ref<OrderTotalAount> }>('totalPrice');
</script>

<template>
  <div class="flex text-#909399 flex-col">
    <div>
      <span
        >折前订单含税金额：{{ currencySymbol
        }}{{ totalPrice?.total.value.allTaxTotalPrice }}</span
      >
      <span class="ml-10px"
        >折前订单未税金额：{{ currencySymbol
        }}{{ totalPrice?.total.value.allFreeTotalPrice }}</span
      >
    </div>
    <div>
      <span
        >折后订单含税金额：{{ currencySymbol
        }}{{ totalPrice?.total.value.allTaxTotalDiscountPrice }}</span
      >
      <span class="ml-10px"
        >折后订单未税金额：{{ currencySymbol
        }}{{ totalPrice?.total.value.allFreeTotalDiscountPrice }}</span
      >
    </div>
  </div>
</template>
