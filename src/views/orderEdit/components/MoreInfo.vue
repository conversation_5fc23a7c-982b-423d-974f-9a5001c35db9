<script setup lang="ts">
import { Ref, inject, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { EditOrder } from '@/types/edit';
import request from '@/utils/request';
import { DetailOrder } from '@/types/detail';
import MoreDeliveryInfo from './MoreDeliveryInfo.vue';
import MoreInvoiceInfo from './MoreInvoiceInfo.vue';

const emit = defineEmits(['update', 'close']);

const activeTab = ref('delivery');

const props = defineProps<{
  data: EditOrder;
}>();

const detailData = inject<Ref<DetailOrder>>('orderData');
const tempData = props.data ? { ...props.data } : null;
const orderData = ref<EditOrder | null>(tempData);
const deliveryInfoRef = ref();
const invoiceInfoRef = ref();

const validateMoreInfo = async () => {
  // 检查签单返回是否可选
  const { customerNo, salesOrganization, distributionChannel } =
    detailData?.value || {};
  const res: any = await request({
    url: '/api-opc/v1/so/getSigningBackOptionByCustomer',
    method: 'POST',
    data: {
      customerNumber: customerNo,
      vkorg: salesOrganization,
      vtweg: distributionChannel,
    },
  });
  if (res && res.code === 200) {
    if (
      res.data?.length === 0 ||
      res.data?.includes(orderData.value?.signingBack)
    ) {
      return true;
    } else {
      ElMessage.error('签单返回不符合要求，请重新选择！');
      return false;
    }
  } else {
    ElMessage.error(res.msg || '查询当前客户可选签单枚举失败');
  }
  return true;
};
const handleSave = () => {
  deliveryInfoRef.value?.deliveryFormRef?.validate((deliveryValid: boolean) => {
    if (deliveryValid) {
      invoiceInfoRef.value?.invoiceFormRef?.validate(
        async (invoiceValid: boolean) => {
          if (invoiceValid) {
            const preValidRes = await validateMoreInfo();
            if (!preValidRes) return;
            emit('update', orderData.value);
            emit('close');
          } else {
            ElMessage.error('请检查发票信息');
          }
        }
      );
    } else {
      ElMessage.error('请检查交货信息');
    }
  });
  // emit('update', orderData.value);
  // emit('close');
};

const handleCancel = () => {
  emit('close');
};
</script>

<template>
  <el-scrollbar height="90vh" class="edit-more">
    <el-tabs v-model="activeTab" class="more-tabs">
      <el-tab-pane label="交货信息" name="delivery">
        <MoreDeliveryInfo
          v-if="orderData"
          ref="deliveryInfoRef"
          v-model="orderData"
        />
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="invoice">
        <MoreInvoiceInfo
          v-if="orderData"
          ref="invoiceInfoRef"
          v-model="orderData"
        />
      </el-tab-pane>
    </el-tabs>
    <div class="ba-row-center">
      <el-button type="primary" @click="handleSave">确认保存</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </el-scrollbar>
</template>

<style lang="scss">
.edit-more {
  .el-tabs__header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
  }
  .ba-row-center {
    display: flex;
    align-items: center;
    justify-content: center;
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 10;
    padding: 10px;
  }
}
</style>
