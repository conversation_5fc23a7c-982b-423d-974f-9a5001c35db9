<script setup lang="ts">
import { Ref, computed, inject, onMounted, reactive, ref } from 'vue';
import {
  ElMessage,
  ElMessageBox,
  FormRules,
  UploadFile,
  UploadFiles,
  UploadProps,
  UploadUserFile,
} from 'element-plus';
import dayjs from 'dayjs';
import { DetailOrder } from '@/types/detail';
import { ContactType } from '@/types/common';
import CustomerSelect from '@/views/maintainment/CustomerSelect.vue';
import SelectContact from '@/components/SelectContact.vue';
import { getFileUrl, searchContactListByGroup } from '@/api/order';
import request from '@/utils/request';
import { useCommonStore } from '@/stores/common';
import type { DocMetaData, EditOrder } from '@/types/edit';

export interface ReceiverForm {
  invoiceReceiver: string;
  invoiceReceiverName: string;
  receivingInvoice: Partial<ContactType>;
  receivingInvoiceContact: number | undefined;
  receivingInvoicePhone: string;
  receivingInvoiceAddress: string;
  docMetaData?: DocMetaData;
}

interface FileResponse {
  name: string;
  objectKey: string;
  bucketName: string;
}

const props = defineProps<{
  data: EditOrder;
  showDialog: boolean;
}>();

const emit = defineEmits(['update:showDialog', 'submit']);

const commonStore = useCommonStore();
const acceptFileType = computed(() => commonStore.acceptFileType || {});
const detailData = inject<Ref<DetailOrder>>('orderData');

const formData = ref<ReceiverForm>({
  invoiceReceiver: '',
  invoiceReceiverName: '',
  receivingInvoice: {},
  receivingInvoiceContact: undefined,
  receivingInvoicePhone: '',
  receivingInvoiceAddress: '',
});
const dlgVisible = computed({
  get: () => props.showDialog,
  set: (val: boolean) => emit('update:showDialog', val),
});

const formRef = ref();
const rules = reactive<FormRules<ReceiverForm>>({
  invoiceReceiver: [
    { required: true, message: '请选择新收票方', trigger: 'blur' },
  ],
  receivingInvoiceContact: [
    { required: true, message: '请选择收票联系人', trigger: 'blur' },
  ],
});
const receivingInvoiceList = ref<ContactType[]>([]);
const loadingInvoiceContact = ref(false);
const queryReceivingInvoiceList = async (contactName: string) => {
  const { salesOrganization, productGroup, distributionChannel } =
    detailData?.value || {};
  if (
    formData.value.invoiceReceiver &&
    salesOrganization &&
    productGroup &&
    distributionChannel
  ) {
    await searchContactListByGroup({
      customerCode: formData.value.invoiceReceiver,
      contactName,
      distributionChannel,
      productGroup,
      salesOrganization,
    })
      .then((res) => {
        if (res?.data?.records?.length) {
          receivingInvoiceList.value = res.data.records;
        } else {
          receivingInvoiceList.value = [];
        }
      })
      .finally(() => {
        loadingInvoiceContact.value = false;
      });
  }
};

const resetReceivingInvoice = () => {
  formData.value.receivingInvoice = {};
  formData.value.receivingInvoiceContact = undefined;
  formData.value.receivingInvoicePhone = '';
  formData.value.receivingInvoiceAddress = '';
  queryReceivingInvoiceList('');
};

const customerSelect = (val: any) => {
  console.log(val);
  formData.value.invoiceReceiver = val?.customerNumber;
  formData.value.invoiceReceiverName = val?.customerName;
  resetReceivingInvoice();
};

const queryReceivingInvoiceContactList = (val: string) => {
  queryReceivingInvoiceList(val);
};

const receivingInvoiceChange = (val: ContactType | undefined) => {
  formData.value.receivingInvoice = val || {};
  formData.value.receivingInvoiceContact = val?.contactId
    ? Number.parseInt(val?.contactId)
    : undefined;
  formData.value.receivingInvoicePhone = val?.contactPhone || '';
  formData.value.receivingInvoiceAddress = val?.address || '';
};

const omsAppName = computed(() => {
  return /boss-uat|local/.test(window.location.href)
    ? 'ecorp-uat'
    : 'ecorp-pro';
});

const uploadList = ref<UploadUserFile[]>([]);

const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  if (uploadList.value.some((item: any) => item.name === file.name)) {
    ElMessage.error({
      message: '文件已存在',
    });
    return false;
  }
  const size = file.size / 1024 / 1024;
  const isGtLimit = size > 10;
  let msg = '';
  let pass = true;
  if (isGtLimit) {
    pass = false;
    msg += `【${file.name}】大小：${size}M，上传文件不能超过10MB！<br/>`;
  }
  if (!pass) {
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: msg,
    });
  }
  return pass;
};

const handleUpload = (fileList: (UploadFile & Record<string, any>)[]) => {
  console.log(fileList);

  if (fileList.every((file) => file.status === 'success')) {
    const list = fileList.map((file) => {
      const res = file.response as FileResponse[];
      return {
        name: file.name || file.fileName || (res && res[0] && res[0].name),
        fileName: file.name || file.fileName || (res && res[0] && res[0].name),
        ossKey: file.ossKey || (res && res[0] && res[0].objectKey),
        bucketName: file.bucketName || (res && res[0] && res[0].bucketName),
        upUserName: commonStore.userName,
        attachmentType: 'general',
        printDirection: 'cross',
        uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };
    });
    // return list;
    console.log(list);
    uploadList.value = list;
  }
};

const handleUploadSucess: UploadProps['onSuccess'] = (
  _res,
  _file,
  fileList
): void => {
  console.log(_res, _file, fileList);
  handleUpload(fileList);
};

const handleUploadRemove: UploadProps['onRemove'] = (
  _file: UploadFile,
  fileList: UploadFiles
) => {
  handleUpload(fileList);
};

const handleUploadError: UploadProps['onError'] = (err: Error) => {
  ElMessage.error(err.message || '上传失败');
};

const onPreview: UploadProps['onPreview'] = async (file: any) => {
  console.log(file);
  let url = '';
  if (file.url) {
    url = file.url;
  } else {
    // 调接口获取url，然后再跳转
    const ossKey = file?.ossKey || file?.response[0]?.objectKey;
    if (ossKey) {
      const res = await getFileUrl(ossKey);
      console.log('preview', res);
      url = res.data;
    }
  }

  if (url) {
    try {
      window.open(url.replace(/http(s)?/, 'https'));
    } catch (error) {
      console.log(error);
    }
  } else ElMessage.error('无法获取文件url，预览失败');
};

const errMsg = ref('');

const loading = ref(false);

const updateInvoiceReceiver = async () => {
  try {
    const data = {
      isUploadFile: uploadList.value.length > 0 ? '1' : '0',
      newCustomer: formData.value?.invoiceReceiver,
      newCustomerName: formData.value?.invoiceReceiverName,
      oldCustomer: detailData?.value?.customerNo,
      sapOrderNos: detailData?.value?.sapOrderNo,
      distributionChannel: detailData?.value?.distributionChannel,
      saleOrganization: detailData?.value?.salesOrganization,
    };
    const res = await request({
      url: `/api-opc/v1/so/template/updateInvoiceValidate`,
      method: 'POST',
      data,
    });
    if (res?.code === 200) {
      const docMetaData = {
        source: 'BOSS',
        dimension: 'order',
        businessId: detailData?.value?.sapOrderNo,
        docMetaDataList: uploadList.value,
        docUploadScene: 'invoiceReceiverModify',
      };
      errMsg.value = '';
      emit('submit', { ...formData.value, docMetaData });
      dlgVisible.value = false;
    } else if (res.code === 301) {
      ElMessageBox.confirm(
        res.msg ||
          '开票抬头客户信用冻结，请确认是否继续？点击继续系统将触发消息给对应销售',
        '操作提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'error',
        }
      )
        .then(async () => {
          // 触发IT消息提醒给客户抬头下对应销售
          const sendRes = await request({
            url: `/api-opc/v1/so/template/sendCustomerCenterMsg`,
            method: 'POST',
            data: {
              ...data,
              msgTemplate: 'ModifyInvoiceReceiverTemplateMsg',
            },
          });
          if (sendRes.code === 200) {
            ElMessage.success('消息发送成功');
          } else {
            ElMessage.error(sendRes.msg || '消息发送失败');
          }
        })
        .catch(() => {
          console.log('已取消操作');
        });
    } else {
      errMsg.value = res?.msg || '操作失败';
    }
  } catch (error) {
    console.log(error);
  }
};

const submit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      updateInvoiceReceiver();
    }
  });
};

const administrator = ref(false);
const getPermission = async () => {
  try {
    const res = await request({
      url: `/api-opc/v1/so/template/so/modify/invoice/receiver/permission`,
      method: 'GET',
      params: {
        soNo: detailData?.value?.soNo,
      },
    });
    if (res.code === 200 && res.data) {
      administrator.value =
        res.data.isModifyInvoiceReceiverAdministrator === '1';
    }
  } catch (error) {
    console.log(error);
  }
};

const oldInvoiceReceiver = computed(
  () =>
    `${detailData?.value?.invoiceReceiver}-${detailData?.value?.invoiceReceiverName}` ||
    ''
);

onMounted(() => {
  getPermission();
});
</script>

<template>
  <el-dialog
    v-model="dlgVisible"
    title="更换收票方（开票抬头）"
    width="600px"
    :v-loading="loading"
  >
    <div class="mb-20px color-red flex justify-center">
      <div class="max-w-500px break-words">{{ errMsg }}</div>
    </div>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-suffix=":"
    >
      <el-form-item label="原收票方">
        <el-input :value="oldInvoiceReceiver" disabled />
      </el-form-item>
      <el-form-item label="新收票方" prop="invoiceReceiver">
        <CustomerSelect
          v-model:value="formData.invoiceReceiver"
          v-model:propsName="formData.invoiceReceiverName"
          @select="customerSelect"
        />
      </el-form-item>
      <el-form-item label="收票联系人" prop="receivingInvoiceContact">
        <SelectContact
          v-model="formData.receivingInvoice"
          title="收票联系人"
          :contact-list="receivingInvoiceList as ContactType[]"
          :loading="loadingInvoiceContact"
          :remote-method="queryReceivingInvoiceContactList"
          @change="receivingInvoiceChange"
        />
      </el-form-item>
      <el-form-item v-if="administrator" label="换抬头凭证">
        <el-upload
          class="inline-block w-500px"
          action="/ali-upload"
          multiple
          :accept="acceptFileType.soCommonType"
          :limit="5"
          :show-file-list="true"
          :with-credentials="true"
          :data="{ appName: omsAppName }"
          :on-success="handleUploadSucess"
          :on-remove="handleUploadRemove"
          :on-error="handleUploadError"
          :before-upload="handleBeforeUpload"
          :on-preview="onPreview"
          :file-list="uploadList"
          list-type="text"
        >
          <el-button type="primary">换抬头凭证上传</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-center">
        <el-button type="primary" @click="submit">确认更换抬头</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss">
.receiver-selecet {
  .option {
    display: flex;

    span {
      width: 20%;

      &:last-child {
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
