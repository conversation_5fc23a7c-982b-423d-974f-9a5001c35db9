<script lang="ts" setup>
import { Ref, computed, inject, reactive, ref, watch } from 'vue';
import { VxeGridProps, VxeTableEvents } from 'vxe-table';
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import _ from 'lodash';
import {
  isForecastOrder,
  isFreeOrder,
  isInnerOrderReason,
} from '@/utils/orderType';
import { useCommonStore } from '@/stores/common';
import { TaxEnum } from '@/types/common';
import { DetailOrder } from '@/types/detail';
import { getOrderMapItem } from '@/utils/edit';
import { from814OrderBasis } from '@/constants/order';
import { getButtonAuth } from '@/utils/auth';
import { formatPrice } from '../utils';
import SelectFactory from './common/SelectFactory.vue';
import SelectItemQuantity from './common/SelectItemQuantity.vue';
import SelectDirectDeliverySupplier from './common/SelectDirectDeliverySupplier.vue';
import SelectPosition from './common/SelectPosition.vue';
import SelectDeliveryPosition from './common/SelectDeliveryPosition.vue';
import SelectCustomerDate from './common/SelectCustomerDate.vue';
import InputItemPrice from './common/InputItemPrice.vue';
import ItemDetail from './ItemDetail.vue';
import type { EditItem, EditOrder } from '@/types/edit';

type RowVO = EditItem;
type Item = EditItem;

const emit = defineEmits(['update:modelValue', 'checkboxChange']);

// to-do types
const costCenterOptions = inject<any>('costCenterOptions');

const orderData = inject<Ref<DetailOrder>>('orderData');

const isStockClearance = inject<Ref<boolean>>('isStockClearance');

const totalPrice = inject<{ update: () => void }>('totalPrice');

const getDeliveryDate = inject<() => void>('updateDeliveryDate');

const commonStore = useCommonStore();

const { mmDictList } = storeToRefs(commonStore);

const props = defineProps<{
  modelValue: EditOrder;
}>();
const showDlg = ref(false);
const selectedItem = ref<EditItem | null>(null);
const data = useVModel(props, 'modelValue', emit);
// const loadings = ref<boolean[]>([]);
const isForecast = computed(() => {
  return orderData ? isForecastOrder(orderData?.value.orderType) : false;
});
const isFree = computed(() => {
  return orderData ? isFreeOrder(orderData?.value.orderType) : false;
});
const isZ007 = computed(() => {
  return orderData?.value.orderType === 'Z007';
});

const isSAP814 = computed(() =>
  from814OrderBasis.includes(data.value?.orderBasis || '')
);
const gridOptions = reactive<VxeGridProps<RowVO>>({
  border: true,
  maxHeight: 600,
  showHeaderOverflow: true,
  size: 'small',
  align: 'center',
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  rowConfig: {
    height: 80,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    { title: '', type: 'checkbox', width: 50, fixed: 'left' },
    {
      title: '项目行',
      field: 'orderItemNo',
      width: 130,
      fixed: 'left',
      slots: { default: 'orderItemNo_slot' },
    },
    { title: '商品编号', field: 'materiel', width: 100, fixed: 'left' },
    {
      title: '商品描述',
      field: 'sapMaterialName',
      width: 300,
      showOverflow: true,
    },
    {
      title: '工厂',
      field: 'factory',
      width: 260,
      slots: { default: 'factory_slot' },
    },
    {
      title: '数量',
      field: 'quantity',
      width: 160,
      headerClassName: 'star',
      slots: { default: 'quantity_slot' },
    },
    {
      title: '已参考数量',
      field: 'clearedQty',
      width: 100,
      visible: isForecast.value,
      formatter({ row }) {
        if (orderData) {
          const _row = getOrderMapItem(row, orderData.value);
          if (_row) {
            return _row.clearedQty;
          }
        }
        return '';
      },
    },
    {
      title: '已交货数量',
      field: 'deliveryQty',
      width: 100,
      visible: !isForecast.value,
      formatter({ row }) {
        if (orderData) {
          const _row = getOrderMapItem(row, orderData.value);
          if (_row) {
            return _row.deliveryQty;
          }
        }
        return '';
      },
    },
    {
      title: '批次号',
      field: 'appointSapBatch',
      width: 100,
      visible: isStockClearance?.value,
    },
    {
      title: '销售考核成本单价',
      field: 'freeTaxSaleAssessmentUnitPrice',
      width: 100,
      visible: isStockClearance?.value,
      formatter({ cellValue }) {
        return formatPrice(cellValue, 6);
      },
    },
    {
      title: '建议含税销售价',
      field: 'suggestPrice',
      width: 200,
      visible: !isStockClearance?.value,
      formatter({ cellValue }) {
        return formatPrice(cellValue, 6);
      },
    },
    {
      title: '含税单价',
      field: 'taxPrice',
      width: 200,
      headerClassName: 'star',
      slots: { default: 'taxPrice_slot' },
    },
    {
      title: '未税单价',
      field: 'freeTaxPrice',
      width: 200,
      headerClassName: 'star',
      slots: { default: 'freeTaxPrice_slot' },
    },
    {
      title: '税率',
      field: 'taxRate',
      width: 60,
      formatter({ cellValue }) {
        return cellValue != null ? `${Math.round(cellValue * 100)}%` : '--';
      },
    },
    {
      title: '整行税额',
      field: 'taxAmount',
      width: 120,
      // formatter({ row }) {
      //   let taxAmount = 0;
      //   const { quantity = 0, taxRate, freeTaxPrice = 0 } = row;
      //   if (orderData && taxRate != null) {
      //     if (orderData?.value.isTax === '1') {
      //       taxAmount = freeTaxPrice * quantity * taxRate;
      //     } else {
      //       taxAmount = freeTaxPrice * quantity * taxRate;
      //     }
      //     return formatPrice(taxAmount);
      //   }
      //   return '';
      // },
    },
    {
      title: '货币',
      field: 'currency',
      width: 80,
    },
    {
      title: '选择直发',
      field: 'directDeliverySupplier',
      headerClassName: 'star',
      width: 180,
      slots: { default: 'directDeliverySupplier_slot' },
    },
    {
      title: '发货仓',
      field: 'position',
      headerClassName: 'star',
      width: 200,
      slots: { default: 'position_slot' },
    },
    {
      title: '集货仓',
      field: 'deliveryPosition',
      width: 200,
      slots: { default: 'deliveryPosition_slot' },
    },
    {
      title: '请求发货日期',
      field: 'deliveryDate',
      width: 150,
    },
    {
      title: '客户期望送达日期',
      field: 'customerDate',
      headerClassName: isForecast.value ? 'star' : '',
      width: 150,
      slots: { default: 'customerDate_slot' },
    },
    {
      title: '订单预计送达日期',
      field: 'orderPlanArrivalDate',
      width: 150,
    },
    {
      title: '订单预计发货日期',
      field: 'orderPlanDeliveryDate',
      width: 150,
    },
    {
      title: '物流时效',
      field: 'leadTime',
      width: 100,
    },
    // {
    //   title: '是否接受标期',
    //   field: 'refuseSystemDeliveryDate',
    //   width: 150,
    //   slots: {
    //     default: 'refuseSystemDeliveryDate_slot',
    //     header: 'refuseSystemDeliveryDate_header',
    //   },
    // },
    {
      title: '成本中心',
      field: 'costCenter',
      width: 200,
      visible: isZ007.value,
      slots: { default: 'costCenter_slot', header: 'costCenter_header' },
    },
    {
      title: '总账科目',
      field: 'generalLedgerAccount',
      width: 200,
      visible: isZ007.value,
      slots: {
        default: 'generalLedgerAccount_slot',
        header: 'generalLedgerAccount_header',
      },
    },
    {
      title: '原始销售订单号',
      field: 'originSapOrderNo',
      width: 140,
      visible: isSAP814.value,
    },
    {
      title: 'SAP退货单单号',
      field: 'referenceOrderNo',
      width: 140,
      visible: isSAP814.value,
    },
    {
      title: '退货单行号',
      field: 'referenceOrderItemNo',
      width: 100,
      visible: isSAP814.value,
    },
    {
      title: '操作',
      field: 'operation',
      width: 110,
      fixed: 'right',
      slots: { default: 'operation_slot' },
    },
  ],
  toolbarConfig: {
    slots: {},
  },
  data: [],
});
const handleDetail = (row: Item) => {
  selectedItem.value = row;
  showDlg.value = true;
};

const handleDelete = (row: Item) => {
  // const idx = (data.value.items || []).findIndex(
  //   (item) => item.validateItemNo === row.validateItemNo
  // );
  // if (idx > -1) {
  //   data.value.items.splice(idx, 1);
  // }
  data.value.items = (data.value.items || []).filter(
    (item) => item.validateItemNo !== row.validateItemNo
  );
  getDeliveryDate && getDeliveryDate();
};

const handleQuantity = () => {
  totalPrice?.update();
  getDeliveryDate && getDeliveryDate();
};
const handleClose = () => {
  showDlg.value = false;
  selectedItem.value = null;
};
const handleSave = (row: Item) => {
  const items = _.cloneDeep(data.value.items);
  const idx = items.findIndex((_row) => {
    if (_row.validateItemNo) {
      return _row.validateItemNo === row.validateItemNo;
    }
    return _row.soItemNo === row.soItemNo;
  });
  if (idx > -1) {
    items[idx] = { ...row };
    data.value.items = items;
    totalPrice?.update();
    getDeliveryDate && getDeliveryDate();
  }
};
const handlePriceChange = () => {
  totalPrice?.update();
};

// const changeAllRefuseSystemDeliveryDate = (val: CheckboxValueType) => {
//   data.value.items.forEach((item) => {
//     item.refuseSystemDeliveryDate = val.toString() as BooleanType;
//   });
// };
watch(
  () => data.value.items,
  (_data) => {
    if (_data && Array.isArray(_data)) {
      console.log('-----', _data);
      gridOptions.data = [..._data];
    }
  },
  { deep: false, immediate: true }
);

const handleCheckboxChange: VxeTableEvents.CheckboxAll<RowVO> = () => {
  emit('checkboxChange');
};
const itemGrid = ref();

// 是否展示交货挂起tag（’99‘为解除挂起，这种情况不展示）
const showDnOrderPendingReasonTag = (row: EditItem) => {
  return (
    (row.dnOrderPendingReasons || '')
      ?.split(',')
      ?.filter((code) => !!code && code !== '99')?.length > 0
  );
};

defineExpose({
  handleDetail,
  itemGrid,
});
</script>

<template>
  <div>
    <vxe-grid
      ref="itemGrid"
      v-bind="gridOptions"
      @checkbox-change="handleCheckboxChange"
      @checkbox-all="handleCheckboxChange"
    >
      <template #orderItemNo_slot="{ row }">
        <el-tag
          v-if="row.notAcceptDemandReasons"
          type="success"
          style="margin-right: 2px"
          >不纳入需求</el-tag
        >
        <el-tag
          v-if="showDnOrderPendingReasonTag(row)"
          style="margin-right: 2px"
          >交货挂起</el-tag
        >
        <span>{{ row.soItemNo || row.idx || '--' }}</span>
      </template>
      <template #factory_slot="{ rowIndex }">
        <SelectFactory
          v-model="data.items[rowIndex]"
          :order-data="orderData"
          :disabled="isStockClearance"
          size="small"
        />
      </template>
      <template #quantity_slot="{ rowIndex }">
        <SelectItemQuantity
          v-model="data.items[rowIndex]"
          size="small"
          :order-data="orderData"
          @change-amount="handleQuantity"
        />
      </template>
      <template #taxPrice_slot="{ row, rowIndex }">
        <InputItemPrice
          v-if="orderData?.isTax === '1'"
          v-model="data.items[rowIndex]"
          :tax="TaxEnum.Yes"
          size="small"
          :disabled="isFree || !!isStockClearance"
          :order-data="orderData"
          @change="handlePriceChange"
        />
        <span v-else>{{ formatPrice(row.taxPrice) }}</span>
      </template>
      <template #freeTaxPrice_slot="{ row, rowIndex }">
        <InputItemPrice
          v-if="orderData?.isTax === '0'"
          v-model="data.items[rowIndex]"
          :tax="TaxEnum.No"
          size="small"
          :disabled="isFree || !!isStockClearance"
          :order-data="orderData"
          @change="handlePriceChange"
        />
        <span v-else>{{ formatPrice(row.freeTaxPrice) }}</span>
      </template>
      <template #directDeliverySupplier_slot="{ rowIndex }">
        <SelectDirectDeliverySupplier
          v-model="data.items[rowIndex]"
          :disabled="isStockClearance"
          :order-data="orderData"
          size="small"
        />
      </template>
      <template #position_slot="{ rowIndex }">
        <SelectPosition
          v-model="data.items[rowIndex]"
          :order-data="orderData"
          :disabled="isStockClearance"
          size="small"
        />
      </template>
      <template #deliveryPosition_slot="{ rowIndex }">
        <SelectDeliveryPosition
          v-model="data.items[rowIndex]"
          :order-data="orderData"
          :disabled="isStockClearance"
          size="small"
        />
      </template>
      <template #customerDate_slot="{ rowIndex }">
        <SelectCustomerDate
          v-model="data.items[rowIndex]"
          :order-data="data"
          :disabled="isStockClearance"
          size="small"
        />
      </template>
      <template #costCenter_header>
        <span
          ><span
            v-if="isInnerOrderReason(data.orderReason || '')"
            class="color-#f56c6c mr-4px"
            >*</span
          >成本中心</span
        >
      </template>
      <template #costCenter_slot="{ row }">
        <el-select-v2
          v-model="row.costCenter"
          :options="costCenterOptions"
          :class="{
            tableCellError:
              !row.costCenter && isInnerOrderReason(data.orderReason || ''),
          }"
          filterable
          size="small"
          value-key="costCenter"
          placeholder="请选择"
        />
      </template>
      <template #generalLedgerAccount_header>
        <span
          ><span
            v-if="isInnerOrderReason(data.orderReason || '')"
            class="color-#f56c6c mr-4px"
            >*</span
          >总账科目</span
        >
      </template>
      <template #generalLedgerAccount_slot="{ row }">
        <el-select
          v-model="row.generalLedgerAccount"
          :class="{
            tableCellError:
              !row.generalLedgerAccount &&
              isInnerOrderReason(data.orderReason || ''),
          }"
          filterable
          size="small"
          value-key="value"
          placeholder="请选择"
        >
          <el-option
            v-for="item in mmDictList.generalLedger"
            :key="item.value"
            :label="`${item.value} ${item.name}`"
            :value="item.value"
          />
        </el-select>
      </template>
      <template #operation_slot="{ row }">
        <div class="flex justify-center">
          <el-button
            v-if="getButtonAuth('销售跟单', `修改_查看商品行详情`)"
            text
            type="primary"
            size="small"
            @click="handleDetail(row)"
            >详情</el-button
          >
          <el-button
            v-if="
              row.addType === '1' &&
              getButtonAuth('销售跟单', `修改_删除商品行详情`)
            "
            text
            type="primary"
            size="small"
            style="margin-left: 0"
            @click="handleDelete(row)"
            >删除</el-button
          >
        </div>
      </template>
    </vxe-grid>
    <el-dialog
      v-if="showDlg && selectedItem"
      v-model="showDlg"
      title="商品详情"
      :show-close="false"
      width="1000px"
      append-to-body
    >
      <ItemDetail
        v-if="orderData"
        :data="selectedItem"
        :order-data="orderData"
        :edit-data="data"
        @cancel="handleClose"
        @save="handleSave"
      />
    </el-dialog>
  </div>
</template>

<style setup lang="scss">
.tableCellError .el-input,
.tableCellError .el-select__wrapper {
  border: 1px solid #ff4949;
  border-radius: 4px;
}
.star .vxe-cell--title::before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
.description-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
}
</style>
