<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useVModel } from '@vueuse/core';
import { ref } from 'vue';
import { useCommonStore } from '@/stores/common';
import { setValueIfIncludesZero } from '@/utils/index';
import type { EditOrder } from '@/types/edit';

const commonStore = useCommonStore();

const { dictList, orderServiceDict } = storeToRefs(commonStore);

const emit = defineEmits(['update:modelValue']);

const props = defineProps<{
  modelValue: EditOrder;
}>();

const data = useVModel(props, 'modelValue', emit);

const calOptionDisabled = (field: keyof EditOrder, item: any) => {
  const val = (data.value[field] as string)?.split(',');
  if (val?.includes('0')) {
    return item.code !== '0';
  }
  // 禁用货运上限7，到达上限时，到达上限时需要把除了已选项和无要求（code为0）项之外的其他选项置灰
  if (field === 'disableShipping' && val?.length >= 7) {
    return !val.includes(item.code) && item.code !== '0';
  }
  // 车辆要求 国五、国六互斥
  if (field === 'vehicleReq') {
    if (val?.includes('10')) {
      return item.code === '14';
    } else if (val?.includes('14')) {
      return item.code === '10';
    }
  }
  return false;
};

const specifiedReceiptTime = ref<string[]>(
  (data.value.specifiedReceiptTime || '').split(',').filter((item) => !!item)
);
const disableShipping = ref<string[]>(
  (data.value.disableShipping || '').split(',').filter((item) => !!item)
);
const designatedShipping = ref<string[]>(
  (data.value.designatedShipping || '').split(',').filter((item) => !!item)
);
const vehicleReq = ref<string[]>(
  (data.value.vehicleReq || '').split(',').filter((item) => !!item)
);
const deliveryRequirements = ref<string[]>(
  (data.value.deliveryRequirements || '').split(',').filter((item) => !!item)
);

const changeSpecifiedReceiptTime = (val: string[]) => {
  specifiedReceiptTime.value = setValueIfIncludesZero(val);
  data.value.specifiedReceiptTime = setValueIfIncludesZero(val).join(',');
};
const changeDisableShipping = (val: string[]) => {
  disableShipping.value = setValueIfIncludesZero(val);
  data.value.disableShipping = setValueIfIncludesZero(val).join(',');
};
const changeDesignatedShipping = (val: string[]) => {
  designatedShipping.value = setValueIfIncludesZero(val);
  data.value.designatedShipping = setValueIfIncludesZero(val).join(',');
};
const changeVehicleReq = (val: string[]) => {
  vehicleReq.value = setValueIfIncludesZero(val);
  data.value.vehicleReq = setValueIfIncludesZero(val).join(',');
};
const changeDeliveryRequirements = (val: string[]) => {
  deliveryRequirements.value = setValueIfIncludesZero(val);
  data.value.deliveryRequirements = setValueIfIncludesZero(val).join(',');
};

// 禁用货运与指定货运互斥
// watch(
//   () => data.value.designatedShipping,
//   () => {
//     if (data.value.designatedShipping?.length) {
//       disableShipping.value = [];
//       data.value.disableShipping = '';
//     }
//   }
// );
// watch(
//   () => data.value.disableShipping,
//   () => {
//     if (data.value.disableShipping?.length) {
//       designatedShipping.value = [];
//       data.value.designatedShipping = '';
//     }
//   }
// );
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-form-item prop="exportProcessingZone" label-width="0">
        <el-checkbox
          v-model="data.exportProcessingZone"
          label="保税区/出口加工区"
          true-label="X"
          false-label="Z"
        />
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="预约送货" prop="scheduleDelivery">
        <el-select
          v-model="data.scheduleDelivery"
          placeholder="请选择预约送货方式"
          clearable
        >
          <el-option
            v-for="item in dictList['scheduleDelivery']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="指定收货时间">
        <el-select
          v-model="specifiedReceiptTime"
          placeholder="请选择指定收货时间"
          multiple
          @change="changeSpecifiedReceiptTime"
        >
          <el-option
            v-for="item in orderServiceDict['specifiedReceiptTime']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('specifiedReceiptTime', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="装运条件">
        <el-select
          v-model="data.shippingCondition"
          placeholder="请选择装运条件"
        >
          <el-option
            v-for="item in dictList['shippingConditions']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="送卸货要求">
        <el-select
          v-model="data.deliveryUnloadingReq"
          placeholder="请选择送卸货要求"
        >
          <el-option
            v-for="item in dictList['deliveryUnloadingReq']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="禁用货运">
        <el-select
          v-model="disableShipping"
          placeholder="请选择禁用货运"
          multiple
          clearable
          @change="changeDisableShipping"
        >
          <el-option
            v-for="item in orderServiceDict['disableShipping']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('disableShipping', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="指定货运">
        <el-select
          v-model="designatedShipping"
          placeholder="请选择指定货运"
          multiple
          clearable
          @change="changeDesignatedShipping"
        >
          <el-option
            v-for="item in orderServiceDict['designatedShipping']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('designatedShipping', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="车辆要求">
        <el-select
          v-model="vehicleReq"
          placeholder="请选择车辆要求"
          multiple
          @change="changeVehicleReq"
        >
          <el-option
            v-for="item in orderServiceDict['vehicleReq']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="
              item.disabled ||
              item.persistDisabled ||
              calOptionDisabled('vehicleReq', item)
            "
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="配送员要求">
        <el-select
          v-model="deliveryRequirements"
          placeholder="请选择配送员要求"
          multiple
          clearable
          @change="changeDeliveryRequirements"
        >
          <el-option
            v-for="item in dictList['deliveryRequirement']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.disabled || item.persistDisabled"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <el-form-item label="签收要求">
        <el-select
          v-model="data.signingReq"
          placeholder="请选择签收要求"
          clearable
        >
          <el-option
            v-for="item in orderServiceDict['signingReq']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.disabled || item.persistDisabled"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="叉车相关">
        <el-select
          v-model="data.forkliftRelated"
          placeholder="请选择叉车相关"
          clearable
        >
          <el-option
            v-for="item in orderServiceDict['forkliftRelated']?.options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="item.disabled || item.persistDisabled"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </el-row>
</template>
