<script setup lang="ts">
import { computed, onMounted, provide, reactive, ref, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  FormRules,
} from 'element-plus';
import { storeToRefs } from 'pinia';
import { cloneDeep, endsWith } from 'lodash';
import DividerHeader from '@/components/DividerHeader.vue';
import { useCommonStore } from '@/stores/common';
import request from '@/utils/request';
import { CustomerData } from '@/types/customer';
import { isInnerOrderReason } from '@/utils/orderType';
import {
  generateItemNo,
  getSimPosition,
  initGoodsList,
  updateDeliveryData,
} from '@/utils/edit';
import { SimPositionType, SupplyPosition } from '@/types/position';
import {
  calculatePrice as calculatePriceApi,
  createOrAddSketch,
  getDeliveryTime,
  saveDocumentMetaData,
  transformSoFailDataPoint,
} from '@/api/order';
import RowMore from '@/components/RowMore.vue';
import { PriceData, PriceItems } from '@/types/price';
import { isNull } from '@/utils';
import { getButtonAuth } from '@/utils/auth';
import { from814OrderBasis } from '@/constants/order';
import DeliverySite from './components/DeliverySite.vue';
import DeliveryDate from './components/DeliveryDate.vue';
import BaseInfo from './components/BaseInfo.vue';
import OtherInfo from './components/OtherInfo.vue';
import OrderInfo from './components/OrderInfo.vue';
import CustomerInfo from './components/CustomerInfo.vue';
import ItemInfo from './components/ItemInfo.vue';
import MoreInfo from './components/MoreInfo.vue';
import { getOrderOptUrl } from './utils';
import CustomerDateConfirmDlg from './components/CustomerDateConfirmDlg.vue';
import type { OrderTotalAount } from '@/types/order';
import type { CostCenterList, DetailOrder } from '@/types/detail';
import type {
  BTBResponse,
  EditItem,
  EditOrder,
  SaveDraftOrder,
  itemResultList,
} from '@/types/edit';

const route = useRoute();
const commonStore = useCommonStore();
const {
  dictList,
  orderServiceDict,
  mmDictList,
  sapReturnOrderValidator,
  holidaysList,
  isShowCombinedDelivery,
} = storeToRefs(commonStore);

const orderNo = route.params.id as string;

const data = ref<EditOrder | null>(null);
const detailData = ref<DetailOrder | null>(null);
const customerData = ref<CustomerData | null>(null);
const loading = ref(false);
const formRef = ref<FormInstance>();
const showDlg = ref(false);
const simPositionList = ref<SupplyPosition[]>([]);
const simDeliveryList = ref<SupplyPosition[]>([]);

// 交期确认弹窗相关-待删
const showCustomerDateConfirmDlg = ref(false);
const conflictMsg = ref('');

const totalPrice = ref<OrderTotalAount>({
  allTaxTotalPrice: 0,
  allTaxTotalDiscountPrice: 0,
  allFreeTotalPrice: 0,
  allFreeTotalDiscountPrice: 0,
});

const calculatePrice = async (): Promise<PriceData | undefined> => {
  if (detailData.value && detailData.value.isTax != null) {
    const { orderBasis, isTax } = detailData.value;
    const items = (data.value?.items || []).map((sku) => ({
      discountAmount: Number(sku.discountAmount),
      itemNo: sku.soItemNo || sku.idx,
      promotionalDiscountRate: isNull(sku.promotionalDiscountRate)
        ? 100
        : sku.promotionalDiscountRate,
      quantity: sku.quantity || 0,
      sku: sku.materiel,
      taxRate: sku.taxRate || 0,
      unitPrice: isTax === '1' ? sku.taxPrice : sku.freeTaxPrice,
    }));
    const queryData = {
      orderBasis,
      isTax,
      items,
    };
    const res = await calculatePriceApi(queryData);
    if (res.code === 200) {
      return res.data;
    }
  }
};

const updateTotalPrice = async () => {
  try {
    const priceData = await calculatePrice();
    if (priceData) {
      totalPrice.value = {
        ...priceData,
        allTaxTotalPrice: priceData?.taxTotalPrice,
        allFreeTotalPrice: priceData?.freeTotalPrice,
        allTaxTotalDiscountPrice: priceData?.discountTotalTaxAmount,
        allFreeTotalDiscountPrice: priceData?.discountFreeTotalPrice,
      };
      (priceData.priceItems || []).forEach((item: PriceItems) => {
        const newItems = [...(data.value?.items || [])];
        const foundIdx = newItems.findIndex(
          (sku) => sku.soItemNo === item.itemNo || sku.idx === item.itemNo
        );
        if (foundIdx > -1 && data.value) {
          let foundItem = data.value?.items[foundIdx];
          foundItem = {
            ...foundItem,
            ...item,
            freeTaxPrice: item.untaxedUnitPrice,
          };
          newItems[foundIdx] = foundItem;
          data.value.items = newItems;
        }
      });
    }
  } catch (error) {
    console.log(error);
  }
};

provide('totalPrice', {
  total: totalPrice,
  update: updateTotalPrice,
  calculatePrice,
});

const costCenterOptions = ref<CostCenterList[]>([]);
provide('costCenterOptions', costCenterOptions);

const morePositionDisabled = ref(false);
provide('morePositionDisabled', morePositionDisabled);
provide('orderData', detailData);
provide('customerData', customerData);
provide('loading', loading);

// 是否是库存出清订单
const isStockClearance = computed(() => {
  return (
    detailData.value?.orderType === 'Z001' &&
    detailData.value?.orderBasis === 'STOCK_CLEARANCE'
  );
});
provide('isStockClearance', isStockClearance);

const updatePositionList = (
  type: SimPositionType,
  posList: SupplyPosition[]
) => {
  if (type === 'position') {
    simPositionList.value.push(...posList);
  } else {
    simDeliveryList.value.push(...posList);
  }
};
provide('sim', {
  simPositionList,
  simDeliveryList,
  updatePositionList,
});

interface SkuDemandQty {
  qty: number;
  sku: string;
  validateItemNo: string;
}
const getDeliveryDate = async () => {
  const skuDemandQtyList = (data.value?.items || [])
    .slice()
    .filter((item: EditItem) => item.addType === '1')
    .map((item: EditItem) => ({
      qty: item.quantity,
      sku: item.materiel || '',
      validateItemNo: item.validateItemNo || '',
    }))
    .filter((item: SkuDemandQty) => item.qty && item.sku);
  const { orderType, customerNo, salesOrganization } = detailData.value || {};
  const {
    receiverContact,
    receiverProvinceCode,
    receiverCityCode,
    receiverDistrictCode,
    receiverAddressId,
    serviceCenterSelfTransport,
    orderContact,
    specifiedReceiptDayOfWeek,
    receiptTimeCategory,
    acceptSupplierDelivery,
    otherLabelReq,
    fastenerLabelReq,
    labelPasteWay,
    hideLogo,
    fastenerDetect,
    packagingReq,
    fastenerSpecialPackageReq,
    deliveryOtherNote,
    bidCustomer,
    dnIncidentalWay,
    autoBatching,
  } = data.value || {};
  if (!salesOrganization) {
    return ElMessage.error('销售组织不能为空！');
  }
  if (!receiverProvinceCode) {
    return ElMessage.error('客户收货省份代码不能为空！');
  }
  if (!receiverCityCode) {
    return ElMessage.error('客户收货城市代码不能为空！');
  }
  if (skuDemandQtyList?.length && orderType !== 'Z014') {
    const queryData = {
      orderType,
      customer: customerNo,
      salesOrganization,
      skuDemandQtyList: skuDemandQtyList.map((item: SkuDemandQty) => ({
        qty: item.qty,
        sku: item.sku,
      })),
      receiptContactId: receiverContact,
      demandProvinceCode: receiverProvinceCode,
      demandCityCode: receiverCityCode,
      demandDistrictCode: receiverDistrictCode,
      demandStreetTownCode: receiverAddressId,
      serviceCenterSelfTransport,
      orderContactId: orderContact,
      specifiedReceiptDayOfWeek,
      receiptTimeCategory,
      acceptSupplierDelivery,
      otherLabelReq,
      fastenerLabelReq,
      labelPasteWay,
      hideLogo,
      fastenerDetect,
      packagingReq,
      fastenerSpecialPackageReq,
      deliveryOtherNote,
      bidCustomer,
      dnIncidentalWay,
    };
    const params = {
      autoBatching,
    };
    try {
      const res = await getDeliveryTime(queryData, params);
      if (res?.code === 200 && res?.success && res?.data) {
        updateDeliveryData({
          autoBatching: autoBatching || '',
          sourceList: skuDemandQtyList,
          targetList: res.data,
          itemList: data.value?.items || [],
        });
      } else {
        ElMessage.error('查询交期出错了，请稍后再试！');
      }
    } catch (error) {
      console.log(error);
    }
  }
};

provide('updateDeliveryDate', getDeliveryDate);

const isZ007 = computed(() => {
  return detailData.value?.orderType === 'Z007';
});

const isForecastOrder = computed(() => {
  return (
    detailData.value?.orderType === 'Z002' ||
    detailData.value?.orderType === 'Z014'
  );
});

const isServiceOrder = computed(() => {
  return (
    detailData.value?.orderType === 'Z005' ||
    detailData.value?.orderType === 'Z013'
  );
});
const isFreeOrder = computed(() => {
  return (
    detailData.value?.orderType === 'Z006' ||
    detailData.value?.orderType === 'Z007'
  );
});

const queryOrder = async (id: string) => {
  const res = await request({
    url: `/api-opc/v1/so/template/so/detail?omsNo=${id}&wantedFields=skuFactoryPriceMap`,
    method: 'GET',
  });
  if (res && res.code === 200) {
    const _data = res.data as DetailOrder;
    return _data;
  } else if (res && res.code !== 200 && res.msg) {
    ElMessageBox.alert(res.msg, '错误', {
      type: 'error',
      confirmButtonText: '确定',
    });
  }
  return null;
};

const queryClient = async (data: DetailOrder) => {
  const {
    customerNo,
    distributionChannel,
    productGroup,
    salesOrganization,
    receiverContact,
  } = data;
  const res = await request({
    url: '/api-opc/v1/customer/get',
    method: 'GET',
    params: {
      distributionChannel,
      productGroup,
      salesOrganization,
      customerNumber: customerNo,
      receiverContactId: receiverContact,
    },
  });
  if (res && res.code === 200) {
    return res.data;
  } else if (res && res.code !== 200 && res.msg) {
    ElMessageBox.alert(res.msg, '错误', {
      type: 'error',
      confirmButtonText: '确定',
    });
  }
  return null;
};

provide('queryClient', queryClient);

const toggleMap = reactive({
  baseInfo: false,
  orderInfo: false,
  customerInfo: false,
  deliveryDate: false,
  deliverySite: false,
});

const showButton = (buttonName?: string) => {
  return getButtonAuth('销售跟单', `修改_${buttonName}`);
};

const handleToggle = (key: keyof typeof toggleMap, status: boolean) => {
  toggleMap[key] = status;
};

const queryCostCenterOptions = async (data: DetailOrder, val: string) => {
  const res = await request({
    url: '/api-mm/config/costCenter/get',
    method: 'get',
    params: {
      companyCode: data.salesOrganization,
      costCenter: val,
    },
  });
  if (res.code === 0) {
    return res.data;
  }
  return [];
};

const queryMorePositionDisabledConfig = async () => {
  const res = await request({
    url: '/api-acm-config?methodType=getConfig&id=more-position-disabled',
    method: 'get',
  });
  if (res) {
    return res.enable;
  }
  return null;
};

const initItemByOrderDetail = () => {
  // to-do 跳转修改页的url要改
  // https://boss-uat-4.zkh360.com/orderSale/formal/edit/1010187959?omsNo=Z0012312021337369550&sapOrderNo=1010187959&selectedItem=10
  if (route.query.selectedItem) {
    const item = data.value?.items?.find(
      (item: EditItem) => item.soItemNo === route.query.selectedItem
    );
    if (item) {
      const newItem: EditItem = cloneDeep(item);
      newItem.referType = '03';
      newItem.omsReferenceOrderItemNo = newItem.soItemNo;
      newItem.omsReferenceOrderNo = detailData.value?.soNo;
      // newItem.quantity = newItem.replenishNum;
      newItem.addType = '1';
      newItem.soItemNo = '';
      newItem.sapItemNo = '';
      newItem.validateItemNo = generateItemNo();
      // newItem.clearedQty = 0;
      if (data.value?.items) {
        data.value?.items.unshift(newItem);
      }
    } else {
      ElMessage.error({
        message: `查询行号${route.query.selectedItem}错误！`,
      });
    }
  }
};

const allowSelectWhsAuth = ref(false);
const checkSelectWhsAuth = async (params: {
  customerNo: string;
  orderBasis: string;
  orderReason: string;
  orderType: string;
  salesOrg: string;
  orderSource: string;
  addItem?: boolean;
}) => {
  if (!params.customerNo || !params.salesOrg) return;
  const res = await request({
    url: '/api-opc/v2/sketch/checkSelectWhsAuth',
    method: 'get',
    params,
  });
  if (res.code === 200) {
    allowSelectWhsAuth.value = res.data;
  }
};
provide('allowSelectWhsAuth', allowSelectWhsAuth);
provide('checkSelectWhsAuth', checkSelectWhsAuth);

const init = async () => {
  if (orderNo) {
    const _detaiData = await queryOrder(orderNo);
    if (_detaiData) {
      detailData.value = _detaiData;
      const factoryList = _detaiData.items.map((item) => item.factory);
      const skuNoList = _detaiData.items.map((item) => item.materiel);
      const reqArr = [
        queryClient(_detaiData),
        queryMorePositionDisabledConfig(),
        getSimPosition(factoryList, skuNoList, 1),
        getSimPosition(factoryList, skuNoList, 3),
        checkSelectWhsAuth({
          customerNo: _detaiData.customerNo,
          orderBasis: _detaiData.orderBasis,
          orderReason: _detaiData.orderReason,
          orderType: _detaiData.orderType,
          salesOrg: _detaiData.salesOrganization,
          orderSource: _detaiData.orderSource,
          addItem: true,
        }),
      ];
      if (JSON.stringify(dictList.value) === '{}') {
        reqArr.push(commonStore.getDictList());
      }
      if (JSON.stringify(orderServiceDict.value) === '{}') {
        reqArr.push(commonStore.getOrderServiceDict());
      }
      if (JSON.stringify(mmDictList.value) === '{}') {
        reqArr.push(commonStore.getMMDictList());
      }
      if (holidaysList.value.length === 0) {
        reqArr.push(commonStore.getHolidays());
      }
      reqArr.push(
        commonStore.getSapReturnOrderValidator(),
        commonStore.getGrayWhiteSwitch()
      );
      if (isZ007.value) {
        reqArr.push(queryCostCenterOptions(_detaiData, ''));
      }
      const retArr = await Promise.all(reqArr);
      customerData.value = retArr[0];
      morePositionDisabled.value = retArr[1];
      simPositionList.value = retArr[2];
      simDeliveryList.value = retArr[3];
      if (isZ007.value) {
        costCenterOptions.value = (retArr.at(-1) || []).map(
          (item: CostCenterList) => ({
            label: `${item.costCenter} ${item.description}`,
            value: item.costCenter,
            ...item,
          })
        );
      }
      data.value = {
        ..._detaiData,
        items: initGoodsList(_detaiData.items, dictList.value) || [],
      };
      // 集货补货来源的修改
      initItemByOrderDetail();
      updateTotalPrice();
      getDeliveryDate();
    }
  }
};

onMounted(async () => {
  try {
    loading.value = true;
    await init();
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
});

const rules = reactive<FormRules<EditOrder>>({
  bidCustomer: [{ required: true, message: '请选择交期条件', trigger: 'blur' }],
});

const closeDialog = () => {};

const handleCancel = () => {
  if (orderNo) {
    window.location.href = `/orderSale/formal/detail/${orderNo}?soNo=${orderNo}`;
  }
};

const isSkuValid = async () => {
  if (data.value?.items?.length) {
    const errList: string[] = [];
    let quantityError = false;
    let positionError = false;
    let factoryError = false;
    let directDeliverySupplierError = false;
    let customerDateError = false;
    let taxedPriceError = false;
    let freeTaxPriceError = false;
    let costCenterError = false;
    let generalLedgerError = false;
    let customerMaterialNameError = false;
    const validateUnitArr: string[] = []; // 校验客户物料单位
    const acceptSupplierDeliveryArr: string[] = []; // 校验供应商直发冲突
    data.value.items.forEach((sku: EditItem) => {
      if (
        !sku.materiel &&
        !quantityError &&
        (!sku.quantity || sku.quantity === 0) &&
        (!sku.customerMaterialQuantity || sku.customerMaterialQuantity === 0)
      ) {
        errList.push('商品信息商品数量和客户物料数量不能同时为空！');
        quantityError = true;
      }
      if (!positionError && !sku.position && sku.materiel) {
        errList.push('商品信息库位信息不能为空！');
        positionError = true;
      }
      if (!factoryError && !sku.factory && sku.materiel) {
        errList.push('商品信息工厂信息不能为空！');
        factoryError = true;
      }
      if (
        !customerMaterialNameError &&
        !sku.customerMaterialName &&
        !sku.materiel
      ) {
        errList.push('商品客户物料名称信息不能为空！');
        customerMaterialNameError = true;
      }
      if (
        !isForecastOrder.value &&
        !directDeliverySupplierError &&
        !sku.directDeliverySupplier &&
        sku.materiel
      ) {
        errList.push('商品信息直送供应商不能为空！');
        directDeliverySupplierError = true;
      }
      if (isForecastOrder.value && !customerDateError && !sku.customerDate) {
        errList.push('商品信息客户期望送达日期不能为空！');
        customerDateError = true;
      }
      if (
        !isServiceOrder.value &&
        !isForecastOrder.value &&
        !isFreeOrder.value
      ) {
        if (
          !taxedPriceError &&
          !sku.taxPrice &&
          detailData.value?.isTax === '1'
        ) {
          errList.push('商品信息含税价格不能为空！');
          taxedPriceError = true;
        }
        if (
          !freeTaxPriceError &&
          !sku.freeTaxPrice &&
          detailData.value?.isTax !== '1'
        ) {
          errList.push('商品信息未税价格不能为空！');
          freeTaxPriceError = true;
        }
      }
      const costCenterDesc = costCenterOptions.value.find(
        (item) => item.costCenter === sku.costCenter
      )?.description;
      if (
        isZ007.value &&
        !costCenterError &&
        (!sku.costCenter || !costCenterDesc) &&
        isInnerOrderReason(data.value?.orderReason || '')
      ) {
        errList.push('成本中心/描述不能为空！');
        costCenterError = true;
      }
      if (
        isZ007.value &&
        !generalLedgerError &&
        !sku.generalLedgerAccount &&
        isInnerOrderReason(data.value?.orderReason || '')
      ) {
        errList.push('总账科目不能为空！');
        generalLedgerError = true;
      }
      if (
        sku.customerMaterialUnit?.length &&
        sku.customerMaterialUnit.length > 10
      ) {
        validateUnitArr.push(`行${sku.soItemNo}-sku为${sku.materiel}`);
      }
      if (
        data.value?.acceptSupplierDelivery === 0 &&
        sku.quantity > sku.clearedQty &&
        sku.directDeliverySupplier === '1'
      ) {
        if (detailData.value?.acceptSupplierDelivery === 1) {
          // 客户接受直发由是改为否的话只要含有直发行就弹提示
          acceptSupplierDeliveryArr.push(`行号${sku.soItemNo || sku.idx}`);
        } else if (sku.soItemNo) {
          // 商品行由非直发改为直发
          const findItem = detailData.value?.items?.find(
            (oldSku) =>
              sku.soItemNo === oldSku.soItemNo &&
              oldSku.directDeliverySupplier !== '1'
          );
          findItem && acceptSupplierDeliveryArr.push(`行号${sku.soItemNo}`);
        } else {
          // 没有soItemNo是新增行
          acceptSupplierDeliveryArr.push(`行号${sku.idx}`);
        }
      }
    });
    // 校验sku必填
    const skuError =
      !quantityError &&
      !positionError &&
      !factoryError &&
      !customerDateError &&
      !directDeliverySupplierError &&
      !taxedPriceError &&
      !freeTaxPriceError &&
      !costCenterError &&
      !generalLedgerError &&
      !customerMaterialNameError;
    if (!skuError) {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: errList.join('<br>'),
      });
      return skuError;
    }
    // 校验sku客户物料单位
    if (validateUnitArr?.length > 0) {
      const contentMsg = `<div style="max-height: 300px;overflow: auto">【${validateUnitArr.join(',')}】的客户物料单位大于10位</div>`;
      ElMessageBox.alert(contentMsg, '错误', {
        type: 'error',
        dangerouslyUseHTMLString: true,
      });
      return false;
    }
    // 校验供应商直发
    if (acceptSupplierDeliveryArr?.length > 0) {
      try {
        const contentMsg = `<div style="max-height: 300px;overflow: auto">存在直发冲突冻结行：${acceptSupplierDeliveryArr.join(',')}，是否修改客户接受供应商直发=是</div>`;
        await ElMessageBox.confirm(contentMsg, '提示', {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
        });
        data.value.acceptSupplierDelivery = 1;
      } catch (error) {
        console.log(error);
        return false;
      }
    }
  }
  return true;
};

const isOrderValid = async () => {
  const { orderReason } = data.value || {};
  const { orderType } = detailData.value || {};
  console.log(data.value, detailData.value);
  if (
    !orderReason &&
    orderType !== 'Z001' &&
    orderType !== 'Z002' &&
    orderType !== 'Z012' &&
    orderType !== 'Z014' &&
    orderType !== 'ZEV1' &&
    orderType !== 'ZEV3'
  ) {
    ElMessage.error({
      dangerouslyUseHTMLString: true,
      message: '订单信息订单原因不能为空！',
    });
    return false;
  }
  try {
    // 校验收票方
    if (
      data.value?.receivingInvoiceContact !==
      detailData.value?.receivingInvoiceContact
    ) {
      await ElMessageBox.confirm(
        '您修改了收票方，请在更多信息内确认相关的开票信息，若确认无误则点击保存即可完成修改',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning',
          center: true,
        }
      );
    }
    // 校验背靠背订单，背靠背订单只校验一次
    if (showBTBOrder.value === false) {
      await checkBTBOrder();
      showBTBOrder.value = true;
    }
    // 校验禁用货运
    // if (
    //   data.value?.disableShipping &&
    //   (data.value.disableShipping.length > 300 ||
    //     data.value?.disableShipping?.split(',')?.length > 7)
    // ) {
    //   ElMessage.error({
    //     dangerouslyUseHTMLString: true,
    //     message: '禁用货运字段最多选择7项且字符串长度不能大于300！',
    //   });
    //   return false;
    // }
    // 校验交货信息
    let hasConflictFields = false;
    let conflictMessage =
      '交货信息中存在冲突字段，订单创建后将自动修正：<br />';
    if (
      data.value?.hideLogo === 'X' &&
      (data.value?.dnSignatureReq?.includes('01') ||
        data.value?.dnSignatureReq?.includes('05'))
    ) {
      conflictMessage +=
        '因隐藏logo=是，故修正送货单签章要求不等于盖红章或每页盖章，紧固件特殊包装要求等于无要求；<br />';
      hasConflictFields = true;
    }

    if (
      data.value?.receiptTimeCategory !== 'X' &&
      (data.value?.specifiedReceiptDayOfWeek?.includes('06') ||
        data.value?.specifiedReceiptDayOfWeek?.includes('07'))
    ) {
      conflictMessage +=
        '因周末与节假日均可收=否，故取消固定送货周期=周六/周日选项；';
      hasConflictFields = true;
    }
    if (
      isShowCombinedDelivery.value &&
      data.value?.combinedDelivery === '4' &&
      data.value?.autoDelivery !== 'Z'
    ) {
      conflictMessage +=
        '合单发货=手动指定合单时，自动发货需等于否，系统已自动修正；';
      hasConflictFields = true;
    }
    // data.value?.items?.forEach((sku, index) => {
    //   if (
    //     sku.isChangeCustomerDate &&
    //     sku.addType &&
    //     sku.refuseSystemDeliveryDate !== 'X' &&
    //     sku.customerDate &&
    //     sku.originSkuArrivalDate &&
    //     new Date(sku.customerDate) < new Date(sku.originSkuArrivalDate)
    //   ) {
    //     conflictMessage += `第${index + 1}行SKU:${sku.materiel}【客户期望送达日期】${sku.customerDate}小于【原始标准送达日期】${sku.originSkuArrivalDate}，请与客户确认是否接受标期<br />`;
    //     hasConflictFields = true;
    //     showCustomerDateConfirmDlg.value = true;
    //   }
    //   if (
    //     sku.isChangeCustomerDate &&
    //     sku.refuseSystemDeliveryDate !== 'X' &&
    //     sku.customerDate &&
    //     sku.supplierPlanArrivalDate &&
    //     new Date(sku.customerDate) < new Date(sku.supplierPlanArrivalDate)
    //   ) {
    //     conflictMessage += `第${index + 1}行SKU:${sku.materiel || sku.materiel}【客户期望送达日期】${sku.customerDate}小于【供应侧最新预计送达日期】${sku.supplierPlanArrivalDate}，请与客户确认是否接受标期<br />`;
    //     hasConflictFields = true;
    //     showCustomerDateConfirmDlg.value = true;
    //   }
    // });
    // // 交期二次确认弹窗
    // if (showCustomerDateConfirmDlg.value) {
    //   conflictMsg.value = conflictMessage;
    //   showCustomerDateConfirmDlg.value = true;
    //   return false;
    // }
    if (hasConflictFields) {
      await ElMessageBox.confirm(conflictMessage, {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showClose: false,
        closeOnClickModal: false,
      });
    }
  } catch (error) {
    console.log(error);
    return false;
  }
  return true;
};

const showBTBOrder = ref(false);

const checkBTBOrder = async () => {
  const queryData = data.value?.items
    ?.filter((item: EditItem) => item.soNo && item.soItemNo)
    ?.map((item: EditItem) => {
      return {
        soNo: item.soNo,
        soItemNo: item.soItemNo,
        quantity: item.quantity,
        factory: item.factory,
        position: item.position,
        directDeliverySupplier: item.directDeliverySupplier,
      };
    });
  const res = await request({
    url: `/api-opc/v1/so/getBackToBackRemindInfos`,
    method: 'post',
    data: queryData,
  });
  if (res?.code === 200 && res?.data) {
    let unclearBTBInfo = '';
    let deliveryBTBInfo = '';
    res.data.forEach((item: BTBResponse) => {
      if (item?.unclearBTBInfo) {
        unclearBTBInfo += `${item.unclearBTBInfo}</br>`;
      }
      if (item?.deliveryBTBInfo) {
        deliveryBTBInfo += `${item.deliveryBTBInfo}</br>`;
      }
    });
    if (unclearBTBInfo || deliveryBTBInfo) {
      try {
        await ElMessageBox.confirm(
          `<div class="btb-wrapper">${unclearBTBInfo}${deliveryBTBInfo}</div>`,
          '订单背靠背信息',
          {
            confirmButtonText: '继续',
            dangerouslyUseHTMLString: true,
            type: 'warning',
          }
        );
        return Promise.resolve();
      } catch (error) {
        return Promise.reject(error);
      }
    } else {
      return Promise.resolve();
    }
  } else {
    ElMessageBox.alert('获取背靠背订单信息失败');
  }
};
const validateOrderData = async () => {
  try {
    const skuValid = await isSkuValid();
    console.log('sku校验', skuValid);
    if (!skuValid) return false;
    const orderValid = await isOrderValid();
    console.log('订单校验', orderValid);
    if (!orderValid) return false;
    return true;
  } catch (error) {
    console.log(error);
  }
};

const saveDraft = (data: SaveDraftOrder, createWorkList: boolean) => {
  loading.value = true;
  createOrAddSketch(data, createWorkList)
    .then((res) => {
      if (res.code === 200) {
        let msgList;
        if (res.data.msgList && Array.isArray(res.data.msgList)) {
          msgList = res.data.msgList.join(';');
        }
        const str = `<div style="max-height:400px;max-width:350px;overflow:auto;white-space: break-spaces;">${msgList}</div>`;
        ElMessageBox.alert(str, '操作提示', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          callback: () => {
            window.location.replace(
              `/sr/draft/list?voucherNoList=${res.data.sketchOrderNo}`
            );
          },
        });
      } else {
        ElMessageBox.alert(res.message || res.msg || '操作失败！', '错误', {
          type: 'error',
        });
      }
    })
    .catch((error) => {
      console.log(error);
      ElMessageBox.alert(error.message || error.msg || '操作失败');
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleUpdate = async () => {
  if (data.value && detailData.value) {
    const orderUrl = getOrderOptUrl(
      dictList.value,
      detailData.value.orderType,
      'update'
    );
    const queryOrderData = cloneDeep(data.value);
    delete queryOrderData.receiverDocMetaData;
    if (!queryOrderData.tradeNo) {
      queryOrderData.tradeNo = '*';
    }
    if (orderUrl) {
      const res = await request({
        url: `/api-opc/v4/oms/${orderUrl}`,
        method: 'post',
        data: queryOrderData,
      });
      const { errMsgList, remindList, headerResultList, itemResultList } =
        res.data || {};
      if (res && res.code === 200) {
        if (data.value.receiverDocMetaData) {
          await saveDocumentMetaData(data.value.receiverDocMetaData);
        }
        const str = '订单修改成功!';
        const msg =
          remindList && remindList.length > 0
            ? `${str};` + `${remindList.join(' ')}`
            : str;
        ElMessageBox.alert(msg, '订单修改成功', {
          confirmButtonText: '确定',
          callback: () => {
            window.location.replace(
              `/orderSale/formal/detail/${detailData.value?.soNo}?soNo=${detailData.value?.soNo}`
            );
          },
        });
      } else {
        const isAddType =
          itemResultList?.findIndex(
            (item: EditItem) => item.addType && item.addType === '1'
          ) > -1;
        if (errMsgList && Array.isArray(errMsgList)) {
          let message = '';
          message += errMsgList.join('<br/>');
          // 只有新增行报错时才需要报错草稿，已有行报错不允许保存草稿
          if (isAddType) {
            const saveDraftData: SaveDraftOrder = cloneDeep(queryOrderData);
            saveDraftData.items = saveDraftData.items
              .filter((item: EditItem) => item.addType === '1')
              .map((item: EditItem) => {
                const curItem = itemResultList.find(
                  (i: EditItem) => i.validateItemNo === item.validateItemNo
                );
                if (curItem) {
                  return {
                    ...item,
                    validatorResultDTOList: curItem.itemValidatorResultList,
                    orderItemNo: curItem.orderItemNo,
                  };
                }
                return item;
              });
            // 触发协议价审批的条件
            const negotiatedDiscountValidator =
              headerResultList?.length === 0 &&
              itemResultList?.every(
                (item: itemResultList) =>
                  item?.itemValidatorResultList?.length === 1 &&
                  [
                    'negotiatedDiscountValidator',
                    'negotiatedDiscountValidatorV2',
                  ].includes(item.itemValidatorResultList[0].validateCode)
              );
            const confirmButtonText = negotiatedDiscountValidator
              ? '一键提交价格审批并保存草稿'
              : '保存草稿';
            const createWorkList = !!negotiatedDiscountValidator;
            ElMessageBox.confirm(
              `<div style="max-height: 300px;overflow: auto">${message}</div`,
              '错误提示',
              {
                dangerouslyUseHTMLString: true,
                confirmButtonText,
                cancelButtonText: '取消',
                type: 'warning',
              }
            )
              .then(() => {
                saveDraftData.validatorResultDTOList = headerResultList;
                saveDraftData.sketchOrderScene = 'manualItemUpdateFailToSketch';
                saveDraft(saveDraftData, createWorkList);
              })
              .catch(() => {
                saveDraftData.validatorResultAllDTO = {
                  headerResultList,
                  itemResultList,
                };
                saveDraftData.actionSource = '2';
                transformSoFailDataPoint(saveDraftData);
              });
          } else {
            ElMessageBox.alert(
              `<div style="max-height: 300px;overflow: auto">${message}</div`,
              '错误提示',
              {
                type: 'error',
                customClass: 'custom-return-error',
                dangerouslyUseHTMLString: true,
              }
            );
          }
        } else {
          ElMessageBox.alert(res.msg, '错误', {
            type: 'error',
            customClass: 'custom-return-error',
            dangerouslyUseHTMLString: true,
          });
        }
      }
      console.log(res);
    }
  }
};
const handleSubmit = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async (valid) => {
    try {
      loading.value = true;
      if (!valid) return ElMessage.error('请填写必填项！');
      if (data.value && detailData.value) {
        const validRes = await validateOrderData();
        console.log('整体校验', validRes);
        if (!validRes) return;
        await handleUpdate();
        console.log('submit!');
      } else {
        console.log('error submit!');
        return false;
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};

const submitEditAgain = async (type: string) => {
  try {
    loading.value = true;
    // 不接受标期并提交
    if (type === 'refuse') {
      data.value?.items?.forEach((sku) => {
        if (
          sku.isChangeCustomerDate &&
          sku.addType &&
          (!sku.refuseSystemDeliveryDate ||
            sku.refuseSystemDeliveryDate !== 'X') &&
          sku.customerDate &&
          sku.originSkuArrivalDate &&
          new Date(sku.customerDate) < new Date(sku.originSkuArrivalDate)
        ) {
          sku.refuseSystemDeliveryDate = 'X';
        }
        if (
          sku.isChangeCustomerDate &&
          (!sku.refuseSystemDeliveryDate ||
            sku.refuseSystemDeliveryDate !== 'X') &&
          sku.customerDate &&
          sku.supplierPlanArrivalDate &&
          new Date(sku.customerDate) < new Date(sku.supplierPlanArrivalDate)
        ) {
          sku.refuseSystemDeliveryDate = 'X';
        }
      });
    }
    await handleUpdate();
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const handleUpdateMoreInfo = (_data: EditOrder) => {
  console.log(_data);
  data.value = _data;
};

const isSAP814 = computed(() =>
  from814OrderBasis.includes(data.value?.orderBasis || '')
);
// sap退货单号展示条件：zkh发货 && 发货仓以04结尾 && 订单原因=对账差异调整
const isShowSapReturnDnNo = computed(() => {
  const skuValid = (data.value?.items || []).some(
    (item) =>
      item.directDeliverySupplier === '0' && endsWith(item.position, '04')
  );
  return (
    !isSAP814.value &&
    /z001|z006|z007/gim.test(detailData?.value?.orderType || '') &&
    data.value?.orderReason === '038' &&
    skuValid
  );
});

provide('isShowSapReturnDnNo', isShowSapReturnDnNo);

const setRules = () => {
  const { orderType, sapReturnDnNoCanModify } = detailData.value || {};
  if (!isForecastOrder.value) {
    rules.receiverContact = [
      { required: true, message: '请选择收货联系人', trigger: 'blur' },
    ];
    rules.orderContact = [
      { required: true, message: '请选择订单联系人', trigger: 'blur' },
    ];
    rules.receivingInvoiceContact = [
      { required: true, message: '请选择收票联系人', trigger: 'blur' },
    ];
  } else {
    rules.receiverContact = [];
    rules.orderContact = [];
    rules.receivingInvoiceContact = [];
  }
  if (
    !(orderType === 'Z001') &&
    !(orderType === 'Z002') &&
    !(orderType === 'Z012') &&
    !(orderType === 'Z014') &&
    !(orderType === 'ZEV1') &&
    !(orderType === 'ZEV3')
  ) {
    rules.orderReason = [
      { required: true, message: '请选择订单原因', trigger: 'blur' },
    ];
  } else {
    rules.orderReason = [];
  }
  if (
    isShowSapReturnDnNo.value &&
    !sapReturnOrderValidator.value &&
    sapReturnDnNoCanModify
  ) {
    rules.sapReturnDnNo = [
      { required: true, message: '请输入sap退货交货单', trigger: 'blur' },
    ];
  } else {
    rules.sapReturnDnNo = [];
  }
  if (isShowCombinedDelivery.value) {
    rules.combinedDelivery = [
      { required: true, message: '请输入合单发货', trigger: 'blur' },
    ];
  } else {
    rules.combinedDelivery = [];
  }
  rules.deliverySensitivityInterval =
    data.value?.deliverySensitivity === 'X'
      ? [{ required: true, message: '必填', trigger: 'blur' }]
      : [];
};
watchEffect(() => {
  setRules();
});
</script>

<template>
  <el-scrollbar height="calc(100% - 50px)">
    <div v-loading="loading" class="p-2">
      <DividerHeader>
        <div class="flex justify-between">
          <span>基本信息</span>
          <RowMore @fold="(status) => handleToggle('baseInfo', status)" />
        </div>
      </DividerHeader>
      <div v-show="!toggleMap.baseInfo">
        <BaseInfo :order-data="detailData" :customer-data="customerData" />
      </div>
      <el-form
        v-if="data"
        ref="formRef"
        :model="data"
        label-width="160px"
        :rules="rules"
      >
        <DividerHeader>
          <div class="flex justify-between">
            <span>订单信息</span>
            <RowMore @fold="(status) => handleToggle('orderInfo', status)" />
          </div>
        </DividerHeader>
        <div v-show="!toggleMap.orderInfo">
          <OrderInfo v-if="data" v-model="data" />
        </div>
        <DividerHeader>
          <div class="flex justify-between">
            <span>客户信息</span>
            <RowMore @fold="(status) => handleToggle('customerInfo', status)" />
          </div>
        </DividerHeader>
        <div v-show="!toggleMap.customerInfo">
          <CustomerInfo
            v-if="data"
            v-model="data"
            v-model:customer-data="customerData"
          />
        </div>
        <DividerHeader>
          <div class="flex justify-between">
            <span>交期要素</span>
            <RowMore @fold="(status) => handleToggle('deliveryDate', status)" />
          </div>
        </DividerHeader>
        <div v-show="!toggleMap.deliveryDate">
          <DeliveryDate v-if="data" v-model="data" />
        </div>
        <DividerHeader>
          <div class="flex justify-between">
            <span>交付要素</span>
            <RowMore @fold="(status) => handleToggle('deliverySite', status)" />
          </div>
        </DividerHeader>
        <div v-show="!toggleMap.deliverySite">
          <DeliverySite v-if="data" v-model="data" />
        </div>
        <DividerHeader>商品信息</DividerHeader>
        <ItemInfo v-if="data" v-model="data" />
        <DividerHeader>其他信息</DividerHeader>
        <OtherInfo v-if="data" v-model="data" />
        <div
          class="flex items-center justify-center h-50px fixed bottom-0 z-999 w-full bg-#ffff"
        >
          <el-button v-if="showButton('编辑更多')" @click="showDlg = true"
            >编辑更多</el-button
          >
          <el-button
            v-if="showButton('保存')"
            type="primary"
            @click="handleSubmit(formRef)"
            >保存</el-button
          >
          <el-button v-if="showButton('取消')" @click="handleCancel"
            >取消</el-button
          >
        </div>
        <el-dialog
          v-if="showDlg"
          v-model="showDlg"
          title="编辑更多"
          :before-close="closeDialog"
          :show-close="false"
          width="1000px"
          top="10px"
        >
          <MoreInfo
            v-if="data"
            :data="data"
            @close="() => (showDlg = false)"
            @update="handleUpdateMoreInfo"
          />
        </el-dialog>
      </el-form>
      <CustomerDateConfirmDlg
        v-model:show-dialog="showCustomerDateConfirmDlg"
        :fail-content="conflictMsg"
        @submit="submitEditAgain"
      />
    </div>
  </el-scrollbar>
</template>
