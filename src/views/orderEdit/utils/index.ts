import { OrderType } from '@/constants';
import { ItemAount } from '@/types/order';
import { EditItem } from '@/types/edit';
import type { DictList } from '@/types/common';

type Item = EditItem;

// 通过字典获得当前请求url
export function getOrderOptUrl(
  dictList: DictList,
  orderType: OrderType,
  optType: string
) {
  const orderOptUrls = dictList.orderOptUrl;
  if (orderOptUrls && orderOptUrls.length > 0) {
    const foundDict = orderOptUrls.find((dict) => {
      if (dict.parentCode) {
        const itemInfo = dict.parentCode.split('-');
        if (itemInfo && itemInfo.length > 0) {
          return itemInfo[0] === orderType && itemInfo[1] === optType;
        }
      }
      return false;
    });
    if (foundDict) {
      return foundDict.code;
    }
  }
  return '';
}

export const parseDict = (
  dictList: DictList,
  code: string,
  val: string | undefined
) => {
  const valList = dictList[code] || [];
  if (valList && Array.isArray(valList) && val) {
    return (valList.find((item) => item.code === val) || {}).name;
  }
  return val;
};

export function formatPrice(value = 0, fractionDigits = 6) {
  const num = Number(value);
  const roundNum =
    Math.round((num + Number.EPSILON) * 10 ** (fractionDigits + 1)) /
    10 ** (fractionDigits + 1);
  const formattedNum = roundNum.toFixed(fractionDigits);
  // 去除末尾的零和不必要的小数点
  return formattedNum.replace(/(\.\d*?)0+$/, '$1').replace(/\.$/, '');
}

export function calSkuAmount(isTax: string, skuList: Item[]) {
  const result = skuList.reduce(
    (acc: ItemAount, sku: Item) => {
      const {
        taxRate = 0,
        discountAmount,
        quantity,
        taxPrice,
        freeTaxPrice,
      } = sku;
      const taxTotalPrice = taxPrice * quantity;
      const freeTotalPrice = freeTaxPrice * quantity;
      const dp = discountAmount ? Number.parseFloat(discountAmount) : 0;
      const newValue: ItemAount = {
        taxedTotal: 0,
        unTaxedTotal: 0,
        taxedDiscountTotal: 0,
        unTaxedDiscountTotal: 0,
      };
      newValue.taxedTotal = taxTotalPrice + acc.taxedTotal;
      newValue.unTaxedTotal = freeTotalPrice + acc.unTaxedTotal;
      if (isTax === '0') {
        newValue.taxedDiscountTotal =
          acc.taxedDiscountTotal + taxTotalPrice - dp * (1 + taxRate);
        newValue.unTaxedDiscountTotal =
          acc.unTaxedDiscountTotal + freeTotalPrice - dp;
      }
      if (isTax === '1') {
        newValue.taxedDiscountTotal =
          acc.taxedDiscountTotal + taxTotalPrice - dp;
        newValue.unTaxedDiscountTotal =
          acc.unTaxedDiscountTotal + freeTotalPrice - dp / (1 + taxRate);
      }
      return newValue;
    },
    {
      taxedTotal: 0,
      taxedDiscountTotal: 0,
      unTaxedTotal: 0,
      unTaxedDiscountTotal: 0,
    }
  );
  return result;
}

export function caculateTotalAmount(isTax: string, items: Item[]) {
  const { taxedTotal, taxedDiscountTotal, unTaxedTotal, unTaxedDiscountTotal } =
    calSkuAmount(isTax, items || []);
  const allTaxTotalPrice = formatPrice(taxedTotal);
  const allTaxTotalDiscountPrice = formatPrice(taxedDiscountTotal);
  const allFreeTotalPrice = formatPrice(unTaxedTotal);
  const allFreeTotalDiscountPrice = formatPrice(unTaxedDiscountTotal);
  return {
    allTaxTotalPrice,
    allTaxTotalDiscountPrice,
    allFreeTotalPrice,
    allFreeTotalDiscountPrice,
  };
}
