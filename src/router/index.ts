import { createRouter, createWebHistory } from 'vue-router';
import OperationLog from '@/views/OperationLog.vue';
import LogisticsStep from '@/views/logisticsStep/LogisticsStep.vue';
import Maintainment from '@/views/maintainment/MaintainmentList.vue';
import MaintainmentDetail from '@/views/maintainment/MaintainmentDetail.vue';
import DraftList from '@/views/draft/List.vue';
import StockClearanceList from '@/views/draft/StockClearanceList.vue';
import SoPaymentModify from '@/views/SoPaymentModify/index.vue';
import AiChat from '@/views/AiChat.vue';

const OrderCreate = () => import('@/views/orderCreate/index.vue');
const OrderEdit = () => import('@/views/orderEdit/index.vue');
const CreateSpecialOrder = () => import('@/views/CreateSpecialOrder.vue');

const router = createRouter({
  history: createWebHistory('/sr/'),
  routes: [
    {
      path: '/operation-log',
      name: 'OperationLog',
      component: OperationLog,
    },
    {
      path: '/logistics',
      name: 'LogisticsStep',
      component: LogisticsStep,
    },
    {
      // 新的客户物料维护页，boss中访问/insteadOrder/maintainmentV3
      path: '/maintainment',
      name: 'maintainmentList',
      component: Maintainment,
    },
    {
      path: '/maintainment/detail/:id',
      name: 'maintainmentDetail',
      component: MaintainmentDetail,
    },
    {
      path: '/draft/list',
      name: 'DraftList',
      component: DraftList,
      meta: {
        title: '草稿列表',
        showDropdownTask: true,
      },
    },
    {
      path: '/stockClearance/list',
      name: 'StockClearanceList',
      component: StockClearanceList,
      meta: {
        title: '库存出清订单列表',
        showDropdownTask: true,
      },
    },
    {
      path: '/draft/:orderNo',
      name: 'DraftEdit',
      component: OrderCreate,
      meta: {
        title: '草稿订单',
        showDropdownTask: true,
        collapseMenu: true,
      },
    },
    {
      path: '/create/:companyCode/:categoryCode',
      name: 'OrderCreate',
      component: OrderCreate,
      meta: {
        title: '创建订单',
        collapseMenu: true,
      },
    },
    {
      path: '/edit/:id',
      name: 'OrderEdit',
      component: OrderEdit,
      meta: {
        title: '修改订单',
        collapseMenu: true,
      },
    },
    {
      path: '/createSpecialOrder',
      name: 'CreateSpecialOrder',
      component: CreateSpecialOrder,
      meta: {
        title: '基于sap-814销售退货单号选择商品行',
        collapseMenu: true,
      },
    },
    {
      path: '/so-payment-modify',
      name: 'SoPaymentModify',
      component: SoPaymentModify,
      meta: { title: '销售订单款期修改' },
    },
    {
      path: '/ai-chat',
      name: 'AiChat',
      component: AiChat,
      meta: { title: 'AI聊天' },
    },
  ],
});

router.beforeEach((to, _from, next) => {
  let title: string = (to.meta.title || '销售订单') as string;
  if (to.name === 'OrderEdit') {
    title = title || '修改订单';
    const { id } = to.params;
    if (id) {
      title = `修改${id}`;
    }
  }
  document.title = title;
  next();
});

export default router;
