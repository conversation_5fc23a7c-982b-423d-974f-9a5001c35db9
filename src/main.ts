import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';

import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
import VxeUI from 'vxe-pc-ui';
import 'vxe-pc-ui/lib/style.css';

import App from './App.vue';
import router from './router';

import './assets/main.css';
import 'virtual:uno.css';
import '@/utils/version.ts';

const app = createApp(App);

app.use(ElementPlus, {
  locale: zhCn,
});

app.use(VxeUI);
app.use(VXETable);
app.use(createPinia());
app.use(router);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.mount('#app');
