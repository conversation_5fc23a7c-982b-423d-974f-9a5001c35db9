import { SaveDraftOrder } from '@/types/edit';
import request from '@/utils/request';

const opc = '/api-opc';
const apiMM = '/api-mm';
const omsConfig = '/oms-config';
const customerProductHost = '/api-customer-product';

export function listDict() {
  return request({
    url: `${opc}/v1/so/template/values/all`,
    params: {
      excludeKey:
        'shippingPosition,salesAreaCustomerWhite,purchaseGroup,purchaseGroupEmail,chemicalPosition',
    },
  });
}

export function getDictVersion() {
  return request({
    url: `${opc}/v1/dict/values/values/version`,
  });
}

export function getOrderServiceMap() {
  return request({
    url: `${opc}/v1/kh-mdm/order/service/list`,
    method: 'GET',
  });
}

export function getDictionaryList(params = {}) {
  return request({
    url: `${apiMM}/config/dictionary/list`,
    method: 'get',
    params,
  });
}

// 查询sku接口切换白名单
export function querySearchSkuSwitch() {
  return request({
    url: `${opc}/v2/sku/like/switch`,
  });
}

export function getUser() {
  return request({
    url: '/internal-api/user',
    method: 'get',
  });
}

export function getOpsAuthorization(params: Record<string, unknown>) {
  return request({
    url: `${opc}/v1/so/ops/authorization/config`,
    method: 'get',
    params,
  });
}

export function getDraftDetail(params: Record<string, unknown>) {
  return request({
    url: `${opc}/v2/sketch/detail`,
    method: 'POST',
    params,
  });
}

/**
 * 用户选择销售范围之后，触发获取用户详情的信息
 * @param {*} customerNumber
 * @param {*} distributionChannel
 * @param {*} productGroup
 * @param {*} salesOrganization
 */
export function getClientDetail(
  customerNumber: string,
  distributionChannel: string,
  productGroup: string,
  salesOrganization: string,
  receiverContactId: string
) {
  const params = {
    customerNumber,
    distributionChannel,
    productGroup,
    salesOrganization,
    receiverContactId,
  };
  return request({
    url: `${opc}/v1/customer/get`,
    params,
  });
}

export function searchContactListByGroup(obj: Record<string, unknown>) {
  const params = {
    current: 1,
    size: 100,
    ...obj,
  };
  return request({
    params,
    url: `${opc}/v1/contact/list`,
  });
}

export function getDeliveryWarehouse(data: Record<string, unknown>) {
  return request({
    url: `${omsConfig}/supply-network/query/common`,
    method: 'post',
    data,
  });
}

/**
 * 快速导入sku商品
 * @param {*} data
 */
export function uploadSku(data: Record<string, unknown>) {
  return request({
    url: `${opc}/v1/sku/uploadSku`,
    method: 'post',
    data,
  });
}

/**
 * 拉取SKU详情
 * @param {*} skuNo
 */
export function getSkuDetail(skuNo: string, params: Record<string, unknown>) {
  let url = `/v1/so/template/skuNo/${skuNo}`;
  if (params) {
    const queryStr: string[] = [];
    Object.keys(params).forEach((key) => {
      queryStr.push(`${key}=${params[key]}`);
    });
    if (queryStr.length > 0) {
      const s = queryStr.join('&');
      url += `?${s}`;
    }
  }
  return request({
    url: opc + url,
  });
}

// 查询sku及其关联sku
export function getSkuList(params: Record<string, unknown>) {
  let url = `/v1/sku/get/v2`;
  if (params) {
    const queryStr: string[] = [];
    Object.keys(params).forEach((key) => {
      queryStr.push(`${key}=${params[key]}`);
    });
    if (queryStr.length > 0) {
      const s = queryStr.join('&');
      url += `?${s}`;
    }
  }
  return request({
    url: opc + url,
  });
}

// 查询交期
export function getDeliveryTime(
  data: Record<string, unknown>,
  params: Record<string, unknown>
) {
  return request({
    url: `${opc}/v4/oms/order/calculateWaveDeliveryDate`,
    method: 'POST',
    data,
    params,
  });
}

/** 查询成本中心
 * listShippingAddress
 * @param {*} params { companyCode: string }
 */
export function getCostCenter(params: Record<string, unknown>) {
  return request({
    url: `${apiMM}/config/costCenter/get`,
    method: 'get',
    params,
  });
}

export function searchMaterial(params: Record<string, unknown>) {
  return request({
    url: `${opc}/v1/sku/customer/relation`,
    method: 'post',
    params,
  });
}

/**
 * 商品模糊查询接口
 * @param {*} search
 */
export function searchSkuList(search: Record<string, unknown>) {
  return request({
    url: `${opc}/v1/so/template/sku/like`,
    params: {
      vague: search,
    },
  });
}

/**
 * 商品模糊查询-新接口
 * @param {*} search
 */
export function searchSkuListV2(data: Record<string, unknown>) {
  return request({
    url: `${opc}/v2/sku/like`,
    method: 'post',
    data,
  });
}
/**
 * 查询精确客户物料关系
 * @param {*} search
 */
export function accurateQuery(data: Record<string, unknown>) {
  return request({
    url: `${opc}/v2/sku/accurateQuery`,
    method: 'post',
    data,
  });
}

// 获取批量下载模板地址
export function queryTemplateExcelUrls() {
  return request({
    url: '/api-acm-config?methodType=getConfig&id=template-excel-urls',
    method: 'get',
  });
}
// 获取上传文件类型
export function queryAcceptFileType() {
  return request({
    url: '/api-acm-config?methodType=getConfig&id=boss-upload-accept-file-type',
    method: 'get',
  });
}
export function queryButtonAuthConfig() {
  return request({
    url: '/api-acm-config?methodType=getConfig&id=boss-mm-auth.config',
    method: 'get',
  });
}

/**
 * 根据客户编码和skuNo获取询价记录列表
 * @param {*} customerNumber
 * @param {*} skuNo
 */
export function enquiryList(customerId: string, skuNo: string) {
  const formData = new FormData();
  formData.append('customerId', customerId);
  formData.append('skuNo', skuNo);
  return request({
    url: `${opc}/inquiryRecords`,
    method: 'post',
    data: formData,
  });
}

/**
 * 获取客户订单记录
 * @param {*} customerNumber
 * @param {*} skuNo
 * @param {*} page
 */
export function orderRecordList(
  customerNumber: string,
  skuNo: string,
  page = 1
) {
  return request({
    url: `${opc}/v1/saps/soVoucherRecords`,
    params: {
      current: page,
      customerCode: customerNumber,
      skuNo,
    },
  });
}

/**
 * 客户多物料关系查询
 * @param {*} data {customerCode: string, zkhSkuNo: string}
 */
export function searchCustomerMaterialRelation(data: Record<string, unknown>) {
  return request({
    url: `${customerProductHost}/customerProduct/relation/service/batch/query/listByAccurateParam`,
    method: 'post',
    data,
  });
}

export function saveDraft(
  data: Record<string, unknown>,
  params?: {
    createWorkList?: boolean;
    afterCreateValidateError?: boolean;
  }
) {
  const { createWorkList = false, afterCreateValidateError = false } =
    params || {};
  return request({
    url: `${opc}/v2/sketch/save`,
    method: 'POST',
    data,
    params: {
      createWorkList,
      afterCreateValidateError,
    },
  });
}

export function createOrderByDraftApi(
  data: Record<string, unknown>,
  params: Record<string, unknown>
) {
  return request({
    url: `${opc}/v2/sketch/fromSketch/createOrAddSo`,
    method: 'post',
    data,
    params,
  });
}

export function createOrderByFormalApi(
  url: string,
  data: Record<string, unknown>,
  params?: Record<string, unknown>
) {
  return request({
    url: `${opc}/v4/oms/${url}`,
    method: 'post',
    data,
    params,
  });
}

// 价格审批
export function createWorkOrderApi(
  data: Record<string, unknown>,
  params: Record<string, string>
) {
  return request({
    url: `${opc}/v2/sketch/salePrice/create`,
    method: 'post',
    data,
    params,
  });
}
// 上传附件
export function saveDocumentMetaData(data: Record<string, unknown>) {
  return request({
    url: `${opc}/v1/doc/saveDocumentMetaData`,
    method: 'post',
    data,
  });
}

export function getFileUrl(ossKey: string) {
  return request({
    url: '/documentApi/v1/ecorp/downloadFile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: {
      ossKeyName: ossKey,
    },
  });
}

// 转正式订单失败埋点
export function transformSoFailDataPoint(data: Record<string, unknown>) {
  return request({
    url: `${opc}/v2/sketch/transformSoFailDataPoint`,
    method: 'POST',
    data,
  });
}

export function getContactById(params: any) {
  return request({
    params,
    url: `${opc}/v1/customer/contact/get`,
  });
}

// 修改账期下拉查询
export function queryPaymentTerms(params: any) {
  return request({
    url: `${opc}/v1/so/template/query/paymentTerms`,
    method: 'get',
    params,
  });
}

// 正式订单新增行失败创建or加回草稿单
export function createOrAddSketch(
  data: SaveDraftOrder,
  createWorkList = false
) {
  return request({
    url: `${opc}/v2/sketch/fromSo/createOrAddSketch?createWorkList=${createWorkList}`,
    method: 'POST',
    data,
  });
}

// 修改订单新增行是否展示
export function isAddSkuBtnDisplay(params: Record<string, string>) {
  return request({
    url: `${opc}/v1/so/template/isAddSkuBtnDisplay`,
    method: 'get',
    params,
  });
}

export function exchange(params: any) {
  return request({
    url: `${opc}/v1/common/rate/exchange`,
    params,
  });
}

// 查询销售组织支持的发票类型
export function findInvoiceType(params: any) {
  return request({
    url: `${opc}/v1/so/template/findInvoiceType/salesOrg`,
    params,
  });
}
// 订单价格计算
export function calculatePrice(data: any) {
  return request({
    url: `${opc}/v1/so/calculatePrice`,
    method: 'post',
    data: {
      ...data,
      items: data.items.map((item: any) => ({
        ...item,
        unitPrice: item.unitPrice || 0,
      })),
    },
  });
}
// 获取库存sku扩展信息
export function queryStockSkuInfo(data: any) {
  return request({
    url: `${opc}/v1/sku/query/stock/sku/info`,
    method: 'post',
    data,
  });
}

export function queryOrderBasisFactory() {
  return request({
    url: `${opc}/v2/sketch/orderBasisFactory/list`,
    method: 'post',
  });
}
