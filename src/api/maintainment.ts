import request from '@/utils/request';
import type { FormData, ListItem } from '@/constants';

const sellOrderHost = '/api-opc';
const apiCustomerProduct = '/api-customer-product';

export function searchClients(query: string) {
  return request({
    url: `${sellOrderHost}/v1/so/template/customer/like`,
    params: { value: query },
  });
}

export const getList = (data: FormData) => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/query/list`,
    method: 'post',
    data,
  });
};

export const batchOperation = (data: Record<string, unknown>) => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/batchOperate/customerMaterialRelation`,
    method: 'post',
    data,
  });
};

export const getEnumData = () => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/enum/list`,
    method: 'post',
  });
};

export const addCustomerProduct = (data: Record<string, unknown>) => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/insert/customerMaterialRelation`,
    method: 'post',
    data,
  });
};

export const editCustomerProduct = (data: ListItem) => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/update/customerMaterialRelation`,
    method: 'post',
    data,
  });
};

export const getImportTemplate = () => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/relation/batchInsert/template/url`,
    method: 'post',
  });
};

export const getLog = (data: Record<string, unknown>) => {
  return request({
    url: `${apiCustomerProduct}/customerProduct/relation/query/opLog/list`,
    method: 'post',
    data,
  });
};

// export const batchAddCustomerProduct = (data) => {
//   return request({
//     url: `${apiCustomerProduct}/customerProduct/relation/batchInsert/customerMaterialRelation`,
//     method: 'post',
//     data,
//   });
// };

// export const batchEditCustomerProduct = (data) => {
//   return request({
//     url: `${apiCustomerProduct}/customerProduct/relation/batchUpdate/customerMaterialRelation`,
//     method: 'post',
//     data,
//   });
// }
