/**
 * @Author: luozhi<PERSON>
 * @Date: 2024-07-29 10:54:30
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-09-10 11:06:45
 * @Description: file content
 */
import request from '@/utils/request';

export interface TemplateQueryParams {
  appointmentKeyId?: string;
  configType: string;
  customizedAlias: string;
  filterConfigKey?: string;
  isDefault?: 'Y' | 'N' | null;
  keyJson: Record<string, string>;
  keyResolveRule: string;
  owner?: string;
  templateInfo: string;
}

export interface TemplateDTO {
  id: string;
  configType: string;
  customizedAlias: string;
  filterConfigKey: string;
  isDefault: 'Y' | 'N' | null;
  keyJson: Record<string, string>;
  keyResolveRule: string;
  owner: string;
  templateInfo: string;
}

export interface PageInfo {
  pageNum: number;
  pageSize: number;
}

const formCenter = '/api-form-center'

export function addTemplate(data: TemplateQueryParams) {
  return request({
    url: `${formCenter}/front-end/display/template/add`,
    method: 'POST',
    data,
  });
}

export function deleteTemplate(params: { id: string }) {
  return request({
    url: `${formCenter}/front-end/display/template/delete`,
    method: 'get',
    params,
  });
}

export function editTemplate(data: TemplateQueryParams) {
  return request({
    url: `${formCenter}/front-end/display/template/edit`,
    method: 'POST',
    data,
  });
}

export function getTemplateList(data: TemplateQueryParams & PageInfo) {
  return request({
    url: `${formCenter}/front-end/display/template/query`,
    method: 'POST',
    data,
  });
}

export function getShareTemplate(id: any) {
  return request({
    url: `${formCenter}/front-end/display/template/shareWithCodeId`,
    method: 'get',
    params: {
      id
    },
  });
}