import { defineStore } from 'pinia';
import { ref } from 'vue';
import request from '@/utils/request';

export interface DictItem {
  name: string;
  value: string;
}

export type DictKey =
  | 'orderCreateStatus'
  | 'attachStatus'
  | 'eventName'
  | 'orderSource'
  | 'subOrderSourceMap'
  | 'sketchItemCreateStatus';

export type Dict = {
  [K in DictKey]?: DictItem[];
};

export const useDraftListStore = defineStore('draft-list', () => {
  const dict = ref<Dict | null>(null);

  async function query() {
    const res = await request({
      url: '/api-opc/v2/sketch/enum/list',
      method: 'POST',
    });
    if (res.success === true && res.data) {
      dict.value = res.data as Dict;
    }
  }
  return { dict, query };
});
