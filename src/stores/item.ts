import { defineStore, storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import _ from 'lodash';
import { Z003Transform, formatPrice, isNull } from '@/utils/index';
import {
  assign,
  findLastLineNo,
  getDefaultDirectDeliverySupplier,
  getPositionByItem,
  updateSkuByChangeFactory,
} from '@/utils/item';
import { generate } from '@/utils/uuid';
import {
  calculatePrice as calculatePriceApi,
  getDeliveryTime,
  getSkuDetail,
  getSkuList,
  queryStockSkuInfo,
  uploadSku,
} from '@/api/order';
import { PriceData, PriceItems } from '@/types/price';
import { useCustomerStore } from './customer';
import { useCommonStore } from './common';
import { useOrderStore } from './order';
import { useDeliveryStore } from './delivery';
import type { SalesDictType, TaxEnum } from '@/types/common';
import type {
  CommonPositionMap,
  FactoryItem,
  IDetailData,
  ItemType,
  SearchItemType,
  SkuVoType,
  StockSkuInfo,
  WaveDeliveryDateResponse,
  WaveFillItem,
} from '@/types/item';

interface AddGoodsParams {
  skuVOList?: SkuVoType[];
  currentSelectSku?: SearchItemType;
  index?: number;
  factoryList: FactoryItem[];
}

export const useItemStore = defineStore('item', () => {
  const itemList = ref<ItemType[]>([]);
  const selectedItemList = ref<ItemType[]>([]);

  const customerStore = useCustomerStore();
  const commonStore = useCommonStore();
  const orderStore = useOrderStore();
  const deliveryStore = useDeliveryStore();

  const { deliveryData } = storeToRefs(deliveryStore);

  const positionListMap = ref({});

  const factoryList = ref<FactoryItem[]>([]);

  const totalAmount = ref<Record<string, number>>({
    taxedTotalAmount: 0,
    untaxedTotalAmount: 0,
    taxedDiscountTotal: 0,
    unTaxedDiscountTotal: 0,
  });

  const showSelectedItem = ref(false);
  const selectedIndex = ref(0);

  const dictList = computed(() => commonStore?.dictList);
  const companyCode = computed(() => orderStore?.companyCode);
  const orderType = computed(() => orderStore.orderData?.orderType);
  const salesOrganization = computed(
    () => customerStore?.selectedSalesRange.salesOrganization
  );
  const customerNumber = computed(() => customerStore?.customer.customerNumber);
  const isTax = computed(() => customerStore.cusDetail?.isTax);
  const serviceCenterSelfTransport = computed(
    () => customerStore?.cusDetail?.serviceCenterSelfTransport
  );
  const acceptSupplierDelivery = computed(
    () => customerStore?.cusDetail?.acceptSupplierDelivery
  );
  const receiptContactId = computed(
    () => customerStore.contactData?.receiverContact?.contactId
  );
  const orderContactId = computed(
    () => customerStore.contactData?.orderContact?.contactId
  );

  const demandProvinceCode = computed(
    () => customerStore.contactData?.receiverContact?.receiverProvinceCode
  );
  const demandCityCode = computed(
    () => customerStore.contactData?.receiverContact?.receiverCityCode
  );
  const demandDistrictCode = computed(
    () => customerStore.contactData?.receiverContact?.receiverDistrictCode
  );
  const demandStreetTownCode = computed(
    () => customerStore.contactData?.receiverContact?.receiverAddressId
  );
  const specifiedReceiptDayOfWeek = computed(
    () => deliveryData.value?.specifiedReceiptDayOfWeek
  );
  const receiptTimeCategory = computed(
    () => deliveryData.value?.receiptTimeCategory
  );
  const autoBatching = computed(() => deliveryData.value?.autoBatching);
  const customerReferenceDate = computed(
    () => deliveryData.value?.customerReferenceDate
  );

  function editStore(
    this: {
      $patch: (data: unknown) => void;
    },
    data: Record<string, unknown>
  ) {
    this.$patch({
      ...data,
    });
  }

  function updateItemProps(data: Record<string, unknown>) {
    itemList.value = itemList.value.map((item) => {
      return {
        ...item,
        ...data,
      };
    });
  }

  function clearItemList() {
    itemList.value = [];
  }
  async function formatAddSku(data: any, sku: ItemType, index: number) {
    const {
      customerReferenceDate,
      orderType,
      isTax,
      taxType,
      factoryList,
      dictList,
      currency,
      urgent,
      customerDateSensitive,
      selectedSalesRange,
      exchangeRate = 1,
    } = data;
    let { companyCode } = data;
    const {
      skuNo,
      packageInfoList,
      factoryProductPriceVOMap,
      directDeliverySupplier,
      discountAmount = 0,
      customerMaterialQuantity = 0,
      uuid,
    } = sku;
    const idx = findLastLineNo(itemList.value);
    const item: ItemType = {
      ...sku,
      skuNo,
      urgent,
      currency,
      customerDateSensitive,
      packageInfoList,
      idx: idx + index * 10,
      uuid: uuid || generate(),
      discountAmount: Number.parseFloat(discountAmount) || 0,
      customerMaterialQuantity:
        Number.parseFloat(customerMaterialQuantity) || 0,
      // 选择直发
      directDeliverySupplier:
        directDeliverySupplier ||
        getDefaultDirectDeliverySupplier(
          selectedSalesRange?.salesOrganization,
          orderType,
          dictList
        ),
    };
    if (orderStore.orderData?.orderBasis === 'STOCK_CLEARANCE') {
      item.directDeliverySupplier = '0';
      item.sku90Turnover = item.sku90Turnover || 0;
      item.freeTaxSaleAssessmentUnitPrice =
        item.freeTaxSaleAssessmentUnitPrice || 0;
    }
    // 交货挂起原因-可编辑的行如果行上没有订单挂起原因，就默认读联系人上的订单挂起原因配置
    if (
      (!item.itemEditable || item.itemEditable === '1') &&
      item.dnOrderPendingReasons === undefined
    ) {
      item.dnOrderPendingReasons = (
        orderStore.orderData.dnOrderPendingReasons || ''
      )
        .split(',')
        .filter((item: string) => !!item);
    }
    if (customerReferenceDate) {
      item.customerDate = customerReferenceDate;
    }
    // 处理单位
    if (packageInfoList?.length === 0) {
      const { unitCode, unitName } = sku;
      if (unitCode && unitName) {
        item.packageInfoList.push({
          skuNo,
          unitName,
          ruleDes: `1${unitName}/${unitName}`,
          conversion: 1,
          unit: unitCode,
        });
      }
    }
    if (item.packageInfoList && item.packageInfoList.length > 0) {
      item.quantityUnit = item.packageInfoList[0].unit;
    }
    // 如果行上面没有工厂信息
    if (!item.factory) {
      const filterFactoryList = factoryList
        .filter((i: SalesDictType) => {
          const codeNum = Number(i.code);
          return (
            factoryProductPriceVOMap &&
            factoryProductPriceVOMap[codeNum] &&
            factoryProductPriceVOMap[codeNum].taxRate != null
          );
        })
        .sort((i1: SalesDictType, i2: SalesDictType) => {
          const codeNum1 = Number(i1.code);
          const codeNum2 = Number(i2.code);
          return codeNum1 - codeNum2;
        });
      // 创建页，需要通过companyCode赋值
      if (companyCode) {
        let companyCodeNum = Number.parseInt(companyCode, 10);
        if (companyCode === '1300') {
          companyCode = '1000';
        }
        // 销售组织 -> 工厂
        try {
          const findFac = filterFactoryList.find(
            (item: FactoryItem) =>
              item.code.slice(0, 2) === companyCode.slice(0, 2)
          );
          if (findFac && findFac.code) {
            companyCodeNum = Number.parseInt(findFac.code, 10);
          }
        } catch (error) {
          console.log(error);
        }
        if (
          factoryProductPriceVOMap &&
          factoryProductPriceVOMap[companyCodeNum] &&
          factoryProductPriceVOMap[companyCodeNum].taxRate != null
        ) {
          item.factory = String(companyCodeNum) as ItemType['factory'];
        }
        if (filterFactoryList?.length > 0 && !item.factory) {
          try {
            await ElMessageBox.confirm(
              `该商品在${companyCodeNum}工厂下未配置税率，选择其他工厂添加商品?`,
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                center: true,
              }
            );
            item.factory = filterFactoryList[0].code;
          } catch (error) {
            console.log(error);
            return { ...item, type: 'error' };
          }
        }
      }
    }
    if (item.factory) {
      try {
        updateSkuByChangeFactory(
          item,
          dictList,
          orderType,
          currency,
          taxType,
          exchangeRate,
          isTax
        );
      } catch (error) {
        console.log(error);
        return { ...item, type: 'error' };
      }
    } else {
      // 无工厂报错
      console.log(
        item.skuNo,
        '该商品在所选择的销售范围下未配置价格或税率，请选择其他商品或在商品中心维护该商品的销售配置'
      );
      return { ...item, type: 'error' };
    }
    return item;
  }
  async function addItemList(data: any) {
    const { items } = data;
    if (items?.length > 0) {
      // const idx = findLastLineNo(itemList.value);
      const errorItems: ItemType[] = [];
      const formatedItems: ItemType[] = [];
      for (const [index, sku] of items.entries()) {
        const item = await formatAddSku(data, sku, index);
        if (item.type === 'error') {
          errorItems.push(item);
        } else {
          formatedItems.push(item);
        }
      }
      formatedItems.forEach((sku: ItemType) => {
        itemList.value.push(sku);
        selectedItemList.value.push(sku);
      });
      if (errorItems?.length > 0) {
        const msg = errorItems.map((item) => item.skuNo).join(',');
        await ElMessageBox.confirm(
          `<div style="max-height: 400px; overflow: auto;">${msg}添加失败，该商品在所选择的销售范围下未配置价格或税率，请选择其他商品或在商品中心维护该商品的销售配置</div>`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true,
          }
        );
      }
      // 计算价格
      updatePrice();
      // 获取仓网数据
      const map = (await getPositionByItem(formatedItems)) as CommonPositionMap;
      if (map) {
        positionListMap.value = {
          ...positionListMap.value,
          ...map,
        };
      }
    }
  }

  function formatGoodsDetail(sku: ItemType, currentSelectSku?: SearchItemType) {
    if (
      sku?.fillDefaultCustomProperty !== 'Z' &&
      Array.isArray(sku?.customPropertyList)
    ) {
      sku.customPropertyList = sku.customPropertyList.map((property: any) => ({
        ...property,
        placeholder: property.customPropertyRemark,
        customPropertyRemark: '',
      }));
    }
    if (currentSelectSku) {
      const {
        customerSkuNo,
        customerSkuName,
        customerSkuUnitCount,
        customerSkuUnit,
        customerSkuSpecification,
        skuUnitCount,
      } = currentSelectSku;
      // 客户物料关系相关字段赋值
      sku.customerMaterialNo = customerSkuNo;
      sku.customerMaterialName = customerSkuName;
      sku.customerMaterialUnit = customerSkuUnit;
      sku.customerSpecificationModel = customerSkuSpecification;
      sku.customerSkuUnitCount = customerSkuUnitCount || 0;
      sku.skuUnitCount = skuUnitCount || 0;
      if (
        Number.parseFloat(sku.skuUnitCount) !== 0 &&
        Number.parseFloat(sku.customerSkuUnitCount) !== 0
      ) {
        sku.materialRate = formatPrice(
          Number.parseFloat(sku.customerSkuUnitCount) /
            Number.parseFloat(sku.skuUnitCount),
          6
        );
      }
    }
    return sku;
  }
  // 快速导入商品
  function uploadGoods(data: AddGoodsParams) {
    const { skuVOList, currentSelectSku, index } = data;
    const params = {
      salesOrganization: customerStore?.selectedSalesRange?.salesOrganization,
      distributionChannel:
        customerStore?.selectedSalesRange?.distributionChannel,
      productGroup: customerStore?.selectedSalesRange?.productGroup,
      orderType: orderType.value,
      uploadSkuInfoVOList: skuVOList,
      customerNo: customerStore.customer?.customerNumber,
    };
    return uploadSku(params).then(async (result) => {
      if (result && result.code === 200) {
        const { uploadSkuInfoDetailVOList, failMsg } = result.data;
        // 非订单原因：对账差异调整禁止添加客户限售商品
        if (failMsg && orderStore.orderData?.orderReason !== '038') {
          ElMessageBox.alert(failMsg, '操作提示', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            type: 'error',
          });
          return;
        }
        const skuList = [];
        const companyCode = orderStore.companyCode;
        for (let sku of uploadSkuInfoDetailVOList) {
          sku = {
            ...sku,
            ...formatGoodsDetail(sku, currentSelectSku),
          };
          skuList.push(sku);
        }
        const params = {
          orderType: orderStore.orderData?.orderType,
          isTax: customerStore.cusDetail?.isTax,
          taxType: customerStore.cusDetail?.taxType,
          exchangeRate: customerStore.customer?.exchangeRate || 1,
          dictList: commonStore.dictList,
          currency: customerStore.customer?.currency,
          urgent: orderStore.orderData?.urgent,
          customerDateSensitive: customerStore.customerDateSensitive,
          factoryList: data?.factoryList,
          selectedSalesRange: customerStore?.selectedSalesRange,
          items: skuList,
          companyCode,
        };
        // 有index表示无SKU行，添加SKU后更新已有行信息；没有index表示直接添加SKU行
        if (index !== undefined) {
          const foundItem = await changeGoodsInTable(params, skuList[0], index);
          return { result, foundItem };
        } else {
          addItemList(params);
        }
      }
      return { result };
    });
  }
  // 导入sap814订单商品
  async function importSAP814Goods(items: ItemType[]) {
    const skuList = [];
    const companyCode = orderStore.companyCode;
    for (let sku of items) {
      sku = {
        ...sku,
        ...formatGoodsDetail(sku),
      };
      skuList.push(sku);
    }
    const params = {
      orderType: orderStore.orderData?.orderType,
      isTax: customerStore.cusDetail?.isTax,
      taxType: customerStore.cusDetail?.taxType,
      exchangeRate: customerStore.customer?.exchangeRate || 1,
      dictList: commonStore.dictList,
      currency: customerStore.customer?.currency,
      urgent: orderStore.orderData?.urgent,
      customerDateSensitive: customerStore.customerDateSensitive,
      factoryList: factoryList.value,
      selectedSalesRange: customerStore?.selectedSalesRange,
      items: skuList,
      companyCode,
    };
    await addItemList(params);
  }

  // 单行添加商品
  function addGoodsFromRow(data: AddGoodsParams) {
    const { currentSelectSku } = data;
    const params = {
      skuNo: currentSelectSku?.skuNo || '',
      customerNo: customerStore.customer?.customerNumber,
      salesOrganization: customerStore?.selectedSalesRange?.salesOrganization,
      distributionChannel:
        customerStore?.selectedSalesRange?.distributionChannel,
      productGroup: customerStore?.selectedSalesRange?.productGroup,
      orderType: orderType.value,
    };
    return getSkuList(params).then(async (result) => {
      if (result && result.code === 200) {
        // 非订单原因：对账差异调整禁止添加客户限售商品
        if (
          result?.bizCode &&
          result.bizCode === 200001 &&
          orderStore.orderData?.orderReason !== '038'
        ) {
          ElMessageBox.alert(result?.msg, '操作提示', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            type: 'error',
          });
          return;
        }
        const skuList = [];
        const companyCode = orderStore.companyCode;
        if (result.data?.length > 0) {
          // 筛选主商品对应的组合商品
          const filteredArr: ItemType[] = [];
          result.data.forEach((item: ItemType) => {
            // relateType为-1（普通商品）或为0时才添加
            if (!item.relateType || item.relateType === -1) {
              item.relateSkuList =
                result.data
                  .filter((item: ItemType) => item.relateType === 2)
                  .map((item: ItemType) => ({ ...item, uuid: generate() })) ||
                [];
              filteredArr.push(item, ...item.relateSkuList);
            }
          });
          for (let sku of filteredArr) {
            sku = {
              ...sku,
              quantity: 0,
              ...formatGoodsDetail(sku, currentSelectSku),
            };
            skuList.push(sku);
          }
          const params = {
            orderType: orderStore.orderData?.orderType,
            isTax: customerStore.cusDetail?.isTax,
            taxType: customerStore.cusDetail?.taxType,
            exchangeRate: customerStore.customer?.exchangeRate || 1,
            dictList: commonStore.dictList,
            currency: customerStore.customer?.currency,
            urgent: orderStore.orderData?.urgent,
            customerDateSensitive: customerStore.customerDateSensitive,
            factoryList: data?.factoryList,
            selectedSalesRange: customerStore?.selectedSalesRange,
            items: skuList,
            companyCode,
          };
          await addItemList(params);
        }
      }
      return { result };
    });
  }

  // 商品详情添加sku（新增空行场景）
  function addGoodsFromDetail(data: AddGoodsParams) {
    const { currentSelectSku, index } = data;
    const params = {
      customerNo: customerStore.customer?.customerNumber,
      salesOrganization: customerStore?.selectedSalesRange?.salesOrganization,
      distributionChannel:
        customerStore?.selectedSalesRange?.distributionChannel,
      productGroup: customerStore?.selectedSalesRange?.productGroup,
      orderType: orderType.value,
    };
    return getSkuDetail(currentSelectSku?.skuNo || '', params).then(
      async (result) => {
        if (result && result.code === 200) {
          // 非订单原因：对账差异调整禁止添加客户限售商品
          if (
            result?.bizCode &&
            result.bizCode === 200001 &&
            orderStore.orderData?.orderReason !== '038'
          ) {
            ElMessageBox.alert(result?.msg, '操作提示', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true,
              type: 'error',
            });
            return;
          }
          const skuList = [];
          const companyCode = orderStore.companyCode;
          let sku = result.data;
          sku = {
            ...sku,
            quantity: 0,
            ...formatGoodsDetail(sku, currentSelectSku),
          };
          skuList.push(sku);
          const params = {
            orderType: orderStore.orderData?.orderType,
            isTax: customerStore.cusDetail?.isTax,
            taxType: customerStore.cusDetail?.taxType,
            exchangeRate: customerStore.customer?.exchangeRate || 1,
            dictList: commonStore.dictList,
            currency: customerStore.customer?.currency,
            urgent: orderStore.orderData?.urgent,
            customerDateSensitive: customerStore.customerDateSensitive,
            factoryList: data?.factoryList,
            selectedSalesRange: customerStore?.selectedSalesRange,
            items: skuList,
            companyCode,
          };
          // 有index表示无SKU行，添加SKU后更新已有行信息；没有index表示直接添加SKU行
          if (index !== undefined) {
            const foundItem = await changeGoodsInTable(
              params,
              skuList[0],
              index
            );
            return { result, foundItem };
          } else {
            addItemList(params);
          }
        }
        return { result };
      }
    );
  }

  async function changeGoodsInTable(data: any, sku: ItemType, index: number) {
    let foundItem = _.cloneDeep(itemList.value[index]);
    const formatedSku = await formatAddSku(data, sku, index);
    foundItem = {
      ...foundItem,
      ...formatedSku,
      idx: foundItem.idx,
      uuid: foundItem.uuid,
      freeTaxPrice: foundItem.freeTaxPrice || formatedSku.freeTaxPrice,
      taxPrice: foundItem.taxPrice || formatedSku.taxPrice,
    };
    if (formatedSku.type === 'error') {
      foundItem.factory = undefined;
      await ElMessageBox.confirm(
        `${foundItem.skuNo}该商品在所选择的销售范围下未配置价格或税率，请选择其他商品或在商品中心维护该商品的销售配置`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );
    }
    // if (isTax.value === '0') {
    //   foundItem.taxPrice = formatPrice(
    //     foundItem.freeTaxPrice * (1 + foundItem.taxRate),
    //     6
    //   );
    // } else {
    //   foundItem.freeTaxPrice = formatPrice(
    //     foundItem.taxPrice / (1 + foundItem.taxRate),
    //     6
    //   );
    // }
    // 获取仓网数据
    const map = (await getPositionByItem([foundItem])) as CommonPositionMap;
    if (map) {
      positionListMap.value = {
        ...positionListMap.value,
        ...map,
      };
    }
    return foundItem;
  }
  function addNullGoods() {
    const sku = {} as ItemType;
    sku.idx = findLastLineNo(itemList.value);
    sku.uuid = generate();
    // 数量
    sku.quantity = 0;
    // 单位
    sku.packageUnit = '';
    // 备注
    sku.memo = '';
    // 客户行号
    sku.customerLine = '';
    // 领用人
    sku.recipient = '';
    // 需求部门
    sku.demandDepartment = '';
    // 直发备注
    sku.sendWarehouseMemo = '';
    // 选择直发
    sku.directDeliverySupplier = getDefaultDirectDeliverySupplier(
      customerStore?.selectedSalesRange?.salesOrganization,
      orderStore.orderData?.orderType,
      commonStore.dictList
    );
    sku.position = sku.directDeliverySupplier === '2' ? '-1' : '';
    // 拉取货币列表
    sku.currency = customerStore.customer?.currency;
    // 首个交期
    sku.customerDate = orderStore.orderData.orderDate;
    // 工厂
    sku.factory = '1000';
    // 价格
    sku.taxPrice = 0;
    sku.freeTaxPrice = 0;
    // 整单客户期望日期对齐抬头
    if (orderStore.orderData.autoBatching !== 'X') {
      sku.customerDate = orderStore.orderData.customerReferenceDate || '';
    }
    itemList.value.push(sku);
    selectedItemList.value.push(sku);
  }
  async function initDraftItemList(data: any) {
    const dictList = commonStore.dictList;
    const isTax = customerStore.cusDetail?.isTax;
    const skuList: ItemType[] = [];
    if (isTax && data?.itemList?.length > 0) {
      for (const item of data.itemList) {
        const {
          materiel,
          factory,
          soItemNo,
          orderItemNo,
          position,
          fillDefaultCustomProperty,
          customPropertyList,
          sapMaterialName,
          materielDescribe,
        } = item;
        const newItem = {
          ...item,
          idx: soItemNo || orderItemNo,
          skuNo: materiel,
          materialDescribe: sapMaterialName || materielDescribe,
          factory,
          position,
          notAcceptDemandReasons:
            item.notAcceptDemandReasons &&
            (item.notAcceptDemandReasons?.split(',')?.filter(Boolean) || []),
          dnOrderPendingReasons:
            item.dnOrderPendingReasons &&
            (item.dnOrderPendingReasons?.split(',')?.filter(Boolean) || []),
        };
        if (
          fillDefaultCustomProperty !== 'Z' &&
          Array.isArray(customPropertyList)
        ) {
          newItem.customPropertyList = customPropertyList.map(
            (property: any) => ({
              ...property,
              placeholder: property.customPropertyRemark,
              customPropertyRemark: '',
            })
          );
        }
        skuList.push(newItem);
      }
      const params = {
        orderType,
        isTax,
        taxType: customerStore.cusDetail?.taxType,
        exchangeRate: customerStore.customer?.exchangeRate || 1,
        dictList,
        currency: customerStore.customer?.currency,
        urgent: orderStore.orderData?.urgent,
        customerDateSensitive: customerStore.customerDateSensitive,
        factoryList: data?.factoryList,
        selectedSalesRange: customerStore?.selectedSalesRange,
        items: skuList,
      };
      await addItemList(params);
      // setTotalAmount(itemList.value, isTax);
    }
  }

  function updateDeliveryData(data: {
    autoBatching: boolean | string;
    sourceList: IDetailData[];
    targetList: WaveDeliveryDateResponse[];
  }) {
    const { autoBatching, sourceList, targetList } = data;
    let errorMsg = '<div style="max-height: 300px;overflow: auto">';
    let hasError = false;
    const fillList: WaveFillItem[] = [];
    let newSourceList = sourceList;
    targetList?.forEach((result: WaveDeliveryDateResponse) => {
      const sourceItem = newSourceList.find(
        (source: IDetailData) =>
          source.sku === result.material && source.qty === result.quantity
      );
      newSourceList = newSourceList.filter(
        (item: IDetailData) => item !== sourceItem
      );
      const { waveDeliveryDate, promoteInfo, skuArrivalDate } = result;
      const index = itemList.value.findIndex(
        (item: ItemType) => item.uuid === sourceItem?.uuid
      );
      if (!waveDeliveryDate && promoteInfo) {
        hasError = true;
        errorMsg += `第${
          index + 1
        }行SKU【${sourceItem?.sku}】查询交期失败：${promoteInfo}<br />`;
      }
      fillList.push({ ...result, waveDeliveryDate, index, skuArrivalDate });
    });
    errorMsg += '</div>';
    // autoBatching 不勾选就是整单
    if (autoBatching === 'Z' || !autoBatching) {
      try {
        const max: WaveFillItem = fillList
          .filter((x) => x.waveDeliveryDate)
          .sort(
            (x, y) =>
              new Date(y.waveDeliveryDate).getTime() -
              new Date(x.waveDeliveryDate).getTime()
          )[0];
        fillList.forEach((item) => {
          item.waveDeliveryDate = max.waveDeliveryDate;
        });
      } catch (error) {
        console.log(error);
      }
    }
    fillList.forEach(
      ({
        waveDeliveryDate,
        index,
        skuArrivalDate,
        sysDeliveryDate,
        originSkuArrivalDate,
        originSkuDeliveryDate,
      }) => {
        itemList.value[index].deliveryDate = waveDeliveryDate;
        itemList.value[index].skuArrivalDate = skuArrivalDate;
        itemList.value[index].sysDeliveryDate = sysDeliveryDate;
        itemList.value[index].originSkuArrivalDate = originSkuArrivalDate;
        itemList.value[index].originSkuDeliveryDate = originSkuDeliveryDate;
      }
    );
    if (hasError) {
      ElMessageBox.alert(errorMsg, '操作提示', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        type: 'warning',
      });
    }
  }

  async function getDeliveryDate() {
    const skuDemandQtyList = (itemList.value || [])
      .slice()
      .map((item: any) => ({
        qty: item.quantity,
        sku: item.skuNo,
        uuid: item.uuid,
      }))
      .filter((item: any) => item.qty && item.sku);
    if (skuDemandQtyList?.length && orderType.value !== 'Z014') {
      const formatedSpecifiedReceiptDayOfWeek = Array.isArray(
        specifiedReceiptDayOfWeek.value
      )
        ? specifiedReceiptDayOfWeek.value.join(',')
        : specifiedReceiptDayOfWeek.value;
      const data = {
        orderType: orderStore.orderData?.orderType,
        customer: customerNumber.value,
        salesOrganization: salesOrganization.value,
        skuDemandQtyList: skuDemandQtyList.map((item: any) => ({
          qty: item.qty,
          sku: item.sku,
        })),
        demandProvinceCode: demandProvinceCode.value,
        demandCityCode: demandCityCode.value,
        demandDistrictCode: demandDistrictCode.value,
        demandStreetTownCode: demandStreetTownCode.value,
        serviceCenterSelfTransport: serviceCenterSelfTransport.value,
        receiptContactId: receiptContactId.value,
        orderContactId: orderContactId.value,
        specifiedReceiptDayOfWeek: formatedSpecifiedReceiptDayOfWeek,
        receiptTimeCategory: receiptTimeCategory.value,
        acceptSupplierDelivery: acceptSupplierDelivery.value,
        otherLabelReq: Array.isArray(deliveryData.value?.otherLabelReq)
          ? deliveryData.value?.otherLabelReq?.join(',')
          : deliveryData.value?.otherLabelReq,
        fastenerLabelReq: Array.isArray(deliveryData.value?.fastenerLabelReq)
          ? deliveryData.value?.fastenerLabelReq?.join(',')
          : deliveryData.value?.fastenerLabelReq,
        labelPasteWay: Array.isArray(deliveryData.value?.labelPasteWay)
          ? deliveryData.value?.labelPasteWay?.join(',')
          : deliveryData.value?.labelPasteWay,
        hideLogo: deliveryData.value?.hideLogo,
        fastenerDetect: deliveryData.value?.fastenerDetect,
        packagingReq: Array.isArray(deliveryData.value?.packagingReq)
          ? deliveryData.value?.packagingReq?.join(',')
          : deliveryData.value?.packagingReq,
        fastenerSpecialPackageReq:
          deliveryData.value?.fastenerSpecialPackageReq,
        deliveryOtherNote: deliveryData.value?.deliveryOtherNote,
        bidCustomer: deliveryData.value?.bidCustomer,
        dnIncidentalWay: deliveryData.value?.dnIncidentalWay,
      };
      const params = {
        autoBatching: autoBatching.value === true ? 'X' : 'Z',
      };
      if (!data.salesOrganization) {
        return ElMessage.error('销售组织不能为空！');
      }
      if (!data.demandProvinceCode) {
        return ElMessage.error('客户收货省份代码不能为空！');
      }
      if (!data.demandCityCode) {
        return ElMessage.error('客户收货城市代码不能为空！');
      }
      try {
        const res = await getDeliveryTime(data, params);
        if (res?.code === 200 && res?.success && res?.data) {
          updateDeliveryData({
            autoBatching: autoBatching.value,
            sourceList: skuDemandQtyList,
            targetList: res.data,
          });
        } else {
          ElMessage.error('查询交期出错了，请稍后再试！');
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async function calculatePrice(): Promise<PriceData | undefined> {
    const isTax = customerStore.cusDetail?.isTax;
    const items = itemList.value?.map((sku) => ({
      discountAmount: sku.discountAmount || 0,
      itemNo: sku.idx,
      promotionalDiscountRate: isNull(sku.promotionalDiscountRate)
        ? 100
        : sku.promotionalDiscountRate,
      quantity: sku.quantity || 0,
      sku: sku.skuNo,
      taxRate: sku.taxRate || 0,
      unitPrice: isTax === '1' ? sku.taxPrice : sku.freeTaxPrice,
    }));
    const data = {
      orderBasis: orderStore.orderData?.orderBasis,
      isTax,
      items,
    };
    const res = await calculatePriceApi(data);
    if (res.code === 200) {
      return res.data;
    }
  }
  async function updatePrice() {
    try {
      const priceData = await calculatePrice();
      if (priceData) {
        totalAmount.value = {
          ...priceData,
          taxedTotalAmount: priceData?.taxTotalPrice || 0,
          untaxedTotalAmount: priceData?.freeTotalPrice || 0,
          taxedDiscountTotal: priceData?.discountTotalTaxAmount || 0,
          unTaxedDiscountTotal: priceData?.discountFreeTotalPrice || 0,
        } as unknown as Record<string, number>;
        (priceData.priceItems || []).forEach((item: PriceItems) => {
          const foundIdx = itemList.value.findIndex(
            (sku) => String(sku.idx) === String(item.itemNo)
          );
          if (foundIdx > -1) {
            let found = itemList.value[foundIdx];
            found = {
              ...found,
              ...item,
              freeTaxPrice: item.untaxedUnitPrice,
            };
            itemList.value[foundIdx] = found;
          }
        });
      }
      console.log('更新行上价格', itemList.value);
    } catch (error) {
      console.log(error);
    }
  }

  // function setTotalAmount(skus: ItemType[], isTax: TaxEnum) {
  //   const {
  //     taxedTotal,
  //     unTaxedTotal,
  //     taxedDiscountTotal,
  //     unTaxedDiscountTotal,
  //   } = calSkuAmount(skus, isTax);
  //   totalAmount.value.taxedTotalAmount = taxedTotal || 0;
  //   totalAmount.value.untaxedTotalAmount = unTaxedTotal || 0;
  //   totalAmount.value.taxedDiscountTotal = taxedDiscountTotal || 0;
  //   totalAmount.value.unTaxedDiscountTotal = unTaxedDiscountTotal || 0;
  // }

  async function getStockSkuInfo(skuList: ItemType[]) {
    const items = (skuList || []).map((item) => ({
      batchNo: item.appointSapBatch,
      position: item.position,
      sku: item.sku,
      factory: item.factory,
      itemNo: String(item.idx),
      taxRate: item.taxRate,
    }));
    const data = {
      items,
    };
    const res = await queryStockSkuInfo(data);
    if (res.code === 200 && res.data) {
      (res.data || []).forEach((item: StockSkuInfo) => {
        const foundIdx = itemList.value.findIndex(
          (sku) => String(sku.idx) === String(item.itemNo)
        );
        if (foundIdx > -1) {
          let foundItem = itemList.value[foundIdx] as ItemType;
          foundItem = {
            ...foundItem,
            ...item,
            appointSapBatch: item.batchNo,
            stockProviderNo: item.supplierNo,
            stockProviderName: item.supplierName,
            freeTaxPrice: item.unTaxMoveAveragePrice,
            taxPrice: item.taxMoveAveragePrice,
          };
          itemList.value[foundIdx] = foundItem;
        }
      });
    } else {
      ElMessage.error(res.msg || '查询失败，请稍后再试！');
    }
  }

  async function updateItem(data: any) {
    // const exchangeRate = customerStore.customer?.exchangeRate || 1;
    // const isTax = customerStore.cusDetail?.isTax;
    const { type, index, value } = data;
    let foundItem = itemList.value[index] as ItemType;
    // const taxRate = Number.parseFloat(foundItem.taxRate);
    const { factoryProductPriceVOMap, appointSapBatch, position } = foundItem;
    const facCode = foundItem?.ifSalePriceChannelLimit
      ? Number(Z003Transform(salesOrganization.value))
      : Number.parseInt(value, 10);
    switch (type) {
      case 'factory':
        foundItem.position = '';
        if (
          factoryProductPriceVOMap &&
          facCode &&
          factoryProductPriceVOMap[facCode]
        ) {
          const { suggestPrice } = factoryProductPriceVOMap[facCode];
          foundItem.suggestPrice = formatPrice(suggestPrice, 6);
        }
        break;
      case 'quantity':
        foundItem.quantity = value;
        // foundItem.taxAmount = foundItem.freeTaxPrice
        //   ? formatPrice(foundItem.freeTaxPrice * value * taxRate)
        //   : 0;
        if (value !== null && foundItem.materialRate) {
          foundItem.customerMaterialQuantity = formatPrice(
            value * foundItem.materialRate,
            6
          );
        }
        if (foundItem.relateSkuList?.length) {
          foundItem.relateSkuList.forEach((item: ItemType) => {
            const relateSku = itemList.value.find(
              (sku) => sku.uuid === item.uuid
            );
            if (relateSku && item.relateCount) {
              relateSku.quantity = foundItem.quantity * item.relateCount;
              if (relateSku.materialRate) {
                relateSku.customerMaterialQuantity = formatPrice(
                  relateSku.quantity * relateSku.materialRate,
                  6
                );
              }
            }
          });
        }
        break;
      // case 'taxPrice':
      //   foundItem.taxPrice = value;
      //   foundItem._taxPrice = value * exchangeRate;
      //   if (!Number.isNaN(taxRate)) {
      //     foundItem.freeTaxPrice = value ? value / (1 + taxRate) : 0;
      //     foundItem._freeTaxPrice =
      //       (value ? value / (1 + taxRate) : 0) * exchangeRate;
      //     foundItem.taxAmount = value
      //       ? formatPrice(foundItem.freeTaxPrice * foundItem.quantity * taxRate)
      //       : 0;
      //   }
      //   break;
      // case 'freeTaxPrice':
      //   foundItem.freeTaxPrice = value;
      //   foundItem._freeTaxPrice = value * exchangeRate;
      //   if (!Number.isNaN(taxRate)) {
      //     foundItem.taxPrice = value ? value * (1 + taxRate) : 0;
      //     foundItem._taxPrice =
      //       (value ? value * (1 + taxRate) : 0) * exchangeRate;
      //     foundItem.taxAmount = value
      //       ? formatPrice(value * foundItem.quantity * taxRate)
      //       : 0;
      //   }
      //   break;
      case 'directDeliverySupplier':
        if (value === '2') {
          foundItem.position = '-1';
        } else {
          foundItem.position = '';
        }
        break;
      case 'appointSapBatch':
      case 'position':
        if (appointSapBatch && position) {
          await getStockSkuInfo([foundItem]);
          updatePrice();
        }
        break;
      case 'all':
        foundItem = {
          ...foundItem,
          ...value,
        };
        itemList.value[index] = foundItem;
        break;
      default:
        foundItem[type] = value;
    }
    switch (type) {
      case 'quantity':
      case 'taxPrice':
      case 'freeTaxPrice':
      case 'promotionalDiscountRate':
      case 'all':
        updatePrice();
        // setTotalAmount(itemList.value, isTax);
        break;
    }
    // 获取仓网数据
    if (type === 'factory' && foundItem) {
      const records = [foundItem];
      const map = (await getPositionByItem(records)) as CommonPositionMap;
      if (map) {
        positionListMap.value = {
          ...positionListMap.value,
          ...map,
        };
      }
    }
  }

  function removeItemList(data: { value: number[]; isTax: TaxEnum }) {
    const { value } = data;
    (value || []).forEach((n: number) => {
      const idx = itemList.value.findIndex((sku: ItemType) => sku.idx === n);
      if (idx > -1) {
        itemList.value.splice(idx, 1);
        selectedItemList.value.splice(idx, 1);
      }
    });
    updatePrice();
    // setTotalAmount(itemList.value, isTax);
  }

  function assignDiscount(data: any) {
    const { discount } = data;
    const taxedTotalAmount = totalAmount.value.taxedTotalAmount;
    const untaxedTotalAmount = totalAmount.value.untaxedTotalAmount;
    if (
      (isTax.value === '1' && taxedTotalAmount) ||
      (isTax.value === '0' && untaxedTotalAmount)
    ) {
      assign(
        itemList.value,
        discount,
        isTax.value,
        untaxedTotalAmount,
        taxedTotalAmount
      );
      updatePrice();
      // setTotalAmount(itemList.value, isTax.value);
      return true;
    }
    return false;
  }

  async function upload(data: any) {
    const { items, factoryList } = data;
    if (items?.length > 0) {
      items.forEach((item: any) => {
        if (
          item?.fillDefaultCustomProperty !== 'Z' &&
          Array.isArray(item?.customPropertyList)
        ) {
          item.customPropertyList = item.customPropertyList.map(
            (property: any) => ({
              ...property,
              placeholder: property.customPropertyRemark,
              customPropertyRemark: '',
            })
          );
        }
      });
      const params = {
        customerReferenceDate: customerReferenceDate.value,
        orderType: orderType.value,
        isTax: isTax.value,
        taxType: customerStore.cusDetail?.taxType,
        exchangeRate: customerStore.customer?.exchangeRate || 1,
        factoryList,
        dictList: dictList.value,
        currency: customerStore.customer?.currency,
        urgent: orderStore.orderData?.urgent,
        customerDateSensitive: customerStore.customerDateSensitive,
        companyCode: companyCode.value,
        selectedSalesRange: customerStore?.selectedSalesRange,
        items,
      };
      await addItemList(params);
      // setTotalAmount(itemList.value, isTax.value);
    }
    return items;
  }

  const selectedItem = computed(() =>
    selectedIndex.value > -1 ? itemList.value[selectedIndex.value] : null
  );

  const isSomeItemsCreated = computed(() =>
    itemList.value.some((item) => item.itemCreateStatus === 1)
  );

  return {
    itemList,
    positionListMap,
    factoryList,
    totalAmount,
    selectedItemList,
    showSelectedItem,
    selectedIndex,
    selectedItem,
    isSomeItemsCreated,
    clearItemList,
    editStore,
    updateItem,
    initDraftItemList,
    uploadGoods,
    addGoodsFromRow,
    addGoodsFromDetail,
    addNullGoods,
    addItemList,
    getDeliveryDate,
    updateItemProps,
    removeItemList,
    assignDiscount,
    upload,
    importSAP814Goods,
    calculatePrice,
    updatePrice,
    updateSkuByChangeFactory,
    getStockSkuInfo,
  };
});
