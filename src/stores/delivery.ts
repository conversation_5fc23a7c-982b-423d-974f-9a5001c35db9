import { defineStore } from 'pinia';
import { ref } from 'vue';
import { orderServiceFields } from '@/utils/orderService';
import type { ContactType, OrderBooleanType } from '@/types/common';

export interface DeliveryData {
  bidCustomer: string;
  customerReferenceDate: string;
  autoBatching: OrderBooleanType;
  entireOrderRefuseSDD: OrderBooleanType;
  orderReason: string;
  orderNote: string;
  reqContactList: ContactType[];
  orderReqContactList: ContactType[];
  invoiceReqContactList: ContactType[];
  referenceStandardShippingReq: OrderBooleanType;
  receiptTimeCategory: OrderBooleanType;
  backupOrder: OrderBooleanType;
  clearSlackStock: OrderBooleanType;
  hideLogo: OrderBooleanType;
  paid: OrderBooleanType;
  attachOrder: OrderBooleanType;
  specifiedDocument: OrderBooleanType;
  exportProcessingZone: OrderBooleanType;
  virtualReturn: OrderBooleanType;
  autoDelivery: OrderBooleanType;
  attachCoa: OrderBooleanType;
  attachMsds: OrderBooleanType;
  attachTds: OrderBooleanType;
  signingBack: string;
  printNum: number;
  deliverySlipTemplate: string;
  deliveryUnloadingReq: string;
  certificateIdentification: string;
  labelTemplate: string;
  projectNo: string;
  collectionAmount: string;
  supplierAccount: string;
  shippingCondition: string;
  deliveryRequirements: string[];
  scheduleDelivery: string;
  deliveryOtherNote: string;
  paymentNote: string;
  agreementNote: string;
  receiverProvinceCode: string;
  receiverCityCode: string;
  receiverDistrictCode: string;
  receiverAddressId: string;
  sapReturnDnNo?: string;

  dnPaperReq: string;
  dnIncidentalWay: string;
  dnSignatureReq: string[];
  otherLabelReq: string[];
  fastenerLabelReq: string[];
  labelPasteWay: string[];
  fastenerDetect: string;
  fastenerSpecialPackageReq: string;
  specifiedReceiptDayOfWeek: string[];
  specifiedReceiptTime: string[];
  disableShipping: string[];
  designatedShipping: string[];
  vehicleReq: string[];
  signingReq: string;
  packagingReq: string[];
  forkliftRelated: string;
  // 交期敏感
  deliverySensitivity: OrderBooleanType;
  // 交期敏感区间
  deliverySensitivityInterval: string;
  // 交期回复时效
  deliveryReplyTimeliness: string;
  // 送货资料需要仓配信息
  deliveryWarehouseInfo: string[];
  // 发货需与客户确认/预约
  customerDeliveryConfirmed: OrderBooleanType;
  combinedDelivery: string; // 合单发货
  customerAcceptSysDate: OrderBooleanType; // 客户接受标期
  tradeNo: string;
}

export const boolProps = [
  'autoBatching',
  'entireOrderRefuseSDD',
  'refuseSystemDeliveryDate',
  'referenceStandardShippingReq',
  'receiptTimeCategory',
  'backupOrder',
  'clearSlackStock',
  'hideLogo',
  'paid',
  'attachOrder',
  'specifiedDocument',
  // 'scheduleDelivery',
  'exportProcessingZone',
  'virtualReturn',
  'autoDelivery',
  'attachCoa',
  'attachMsds',
  'attachTds',
  'deliverySensitivity',
  'customerDeliveryConfirmed',
  'customerAcceptSysDate',
];

const allDeliveryProps = [
  'bidCustomer',
  'customerReferenceDate',
  'autoBatching',
  'entireOrderRefuseSDD',
  'orderReason',
  'orderNote',
  // 编辑更多交货信息

  'packagingReq',
  'referenceStandardShippingReq',
  'receiptTimeCategory',
  'backupOrder',
  'clearSlackStock',
  'hideLogo',
  'paid',
  'attachOrder',
  'specifiedDocument',
  'exportProcessingZone',
  'virtualReturn',
  'autoDelivery',
  'attachCoa',
  'attachMsds',
  'attachTds',
  'signingBack',
  'printNum',
  'deliverySlipTemplate',
  'deliveryUnloadingReq',
  'certificateIdentification',
  'labelTemplate',
  'projectNo',
  'collectionAmount',
  'supplierAccount',
  'shippingCondition',
  'deliveryRequirements',
  'scheduleDelivery',
  'disableShipping',
  'designatedShipping',
  'deliveryOtherNote',
  'paymentNote',
  'agreementNote',
  'receiverProvinceCode',
  'receiverCityCode',
  'receiverDistrictCode',
  'receiverAddressId',
  'forkliftRelated',
  'sapReturnDnNo',
  'deliveryWarehouseInfo',
  'deliverySensitivity',
  'deliverySensitivityInterval',
  'deliveryReplyTimeliness',
  'customerDeliveryConfirmed',
  'combinedDelivery',
  'customerAcceptSysDate',
  'tradeNo',
];

export const formatInitialBoolean = (prop: string, value: any) => {
  if (boolProps.includes(prop)) {
    if (value === 'X' || value === true) return true;
    return false;
  }
  return value;
};
// const contactProps = ['receiverContactList', 'orderContactList'];
// const formatContactList = (value: ContactType) => {
//   if (Array.isArray(value)) {
//     if (!value.length || (value[0] && value[0].contactName !== '联系人')) {
//       value.unshift(contactHeader);
//     }
//   }
//   return value;
// };
export const useDeliveryStore = defineStore('delivery', () => {
  const customer = ref({
    currency: '',
    currencySymbol: '',
    exchangeRate: 0,
  });

  const deliveryData = ref<DeliveryData>({
    // 交货信息
    bidCustomer: '',
    customerReferenceDate: '',
    autoBatching: false,
    entireOrderRefuseSDD: false,
    orderReason: '',
    orderNote: '',
    // 编辑更多交货信息

    packagingReq: ['0'],
    referenceStandardShippingReq: false,
    receiptTimeCategory: false,
    backupOrder: false,
    clearSlackStock: false,
    hideLogo: false,
    paid: false,
    attachOrder: false,
    specifiedDocument: false,
    exportProcessingZone: false,
    virtualReturn: false,
    autoDelivery: false,
    attachCoa: false,
    attachMsds: false,
    attachTds: false,
    signingBack: '',
    printNum: 1,
    deliverySlipTemplate: '',
    deliveryUnloadingReq: '',
    certificateIdentification: '0',
    labelTemplate: '',
    projectNo: '',
    collectionAmount: '',
    supplierAccount: '',
    shippingCondition: '',
    deliveryRequirements: [],
    scheduleDelivery: '',
    disableShipping: [],
    designatedShipping: [],
    deliveryOtherNote: '',
    paymentNote: '',
    agreementNote: '',
    forkliftRelated: '',
    sapReturnDnNo: '',
    deliverySensitivity: false,
    customerDeliveryConfirmed: false,
    deliverySensitivityInterval: '',
    deliveryReplyTimeliness: '',
    deliveryWarehouseInfo: [],
    combinedDelivery: '',
    customerAcceptSysDate: false,
    tradeNo: '',
  } as unknown as DeliveryData);

  function initDeliveryData(data: any) {
    const temp: any = {};
    for (const prop of Object.keys(data)) {
      if (allDeliveryProps.includes(prop)) {
        temp[prop] = formatInitialBoolean(prop, data[prop]);
      }
    }

    orderServiceFields
      .concat({ field: 'packagingReq', multiple: true })
      .forEach((curr) => {
        const field = curr.field;
        const val =
          curr.multiple && typeof data[field] === 'string'
            ? (data[field] || '').split(',').filter(Boolean)
            : data[field];
        temp[field] = val;
      });

    if (typeof data.deliveryRequirements === 'string') {
      temp.deliveryRequirements = data.deliveryRequirements
        .split(',')
        .filter((item: string) => item !== '');
    }
    deliveryData.value = {
      ...deliveryData.value,
      ...temp,
    };
  }

  function clearDeliveryData() {
    deliveryData.value = {
      bidCustomer: '',
      customerReferenceDate: '',
      autoBatching: false,
      entireOrderRefuseSDD: false,
      orderReason: '',
      orderNote: '',
      packagingReq: ['0'],
      referenceStandardShippingReq: false,
      receiptTimeCategory: false,
      backupOrder: false,
      clearSlackStock: false,
      hideLogo: false,
      paid: false,
      attachOrder: false,
      specifiedDocument: false,
      exportProcessingZone: false,
      virtualReturn: false,
      autoDelivery: false,
      attachCoa: false,
      attachMsds: false,
      attachTds: false,
      signingBack: '',
      printNum: 1,
      deliverySlipTemplate: '',
      deliveryUnloadingReq: '',
      certificateIdentification: '0',
      labelTemplate: '',
      projectNo: '',
      collectionAmount: '',
      supplierAccount: '',
      shippingCondition: '',
      deliveryRequirements: [],
      scheduleDelivery: '',
      disableShipping: [],
      designatedShipping: [],
      deliveryOtherNote: '',
      paymentNote: '',
      agreementNote: '',
      forkliftRelated: '',
      sapReturnDnNo: '',
      deliveryWarehouseInfo: [],
      deliverySensitivity: false,
      customerDeliveryConfirmed: false,
      deliverySensitivityInterval: '',
      deliveryReplyTimeliness: '',
      customerAcceptSysDate: false,
      tradeNo: '',
    } as unknown as DeliveryData;
  }
  function updateDeliveryData(
    this: {
      $patch: (data: any) => void;
    },
    payload: any
  ) {
    this.$patch({
      deliveryData: { ...deliveryData.value, ...payload },
    });
  }
  return {
    customer,
    deliveryData,
    initDeliveryData,
    clearDeliveryData,
    updateDeliveryData,
  };
});
