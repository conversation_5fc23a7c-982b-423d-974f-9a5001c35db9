import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { getClientDetail as getClientDetailAPI } from '@/api/order';
import { useOrderStore } from './order';
import { useInvoiceStore } from './invoice';
import { useDeliveryStore } from './delivery';
import { useItemStore } from './item';
import type { ContactType, OrderBooleanType } from '@/types/common';

export interface CusDetail {
  paymentTerm?: string;
  sellerMap?: any;
  vflag?: boolean;
  customerServiceMap?: any;
  saleOrgVO?: any;
  [x: string]: any;
}

export type SaleMap = {
  [prop: string]: string;
};
export type SaleOrgList = {
  salesOrganizationName: string;
  salesOrganization: string;
  distributionChannel: string;
  distributionChannelName: string;
  productGroup: string;
  productGroupName: string;
};
export type SaleOrgOptions = {
  data: SaleOrgList[] & { idx: string };
  key: string;
  value: string;
};

export interface Customer {
  customerId?: string;
  customerNumber?: string;
  customerName?: string;
  cityName?: string;
  saleMap?: SaleMap;
  saleOrgList?: Array<SaleOrgList>;
  vflag?: boolean;
  currency?: string;
  currencySymbol?: string;
  exchangeRate?: number;
  customerNo?: string;
}

export interface Contact {
  receiverContact: ContactType | null;
  receiverContactList: ContactType[];
  receiverPhone: string;
  receiverAddress: string;
  orderContact: ContactType | null;
  orderContactList: ContactType[];
  orderContactPhone: string;
}

export const useCustomerStore = defineStore('customer', () => {
  const orderStore = useOrderStore();
  const deliveryStore = useDeliveryStore();
  const invoiceStore = useInvoiceStore();
  const itemStore = useItemStore();
  const orderSource = computed(() => orderStore.orderData.orderSource);
  const customer = ref<Customer>({
    customerId: '',
    customerNumber: '',
    customerName: '',
    customerNo: '',
    cityName: '',
    saleMap: {},
    saleOrgList: [],
    vflag: false,
    currency: '',
    currencySymbol: '',
    exchangeRate: 0,
  });

  const contactData = ref<Contact>({
    // 收货联系人、电话、地址
    receiverContact: null,
    receiverContactList: [],
    receiverPhone: '',
    receiverAddress: '',
    // 订单联系人
    orderContact: null,
    orderContactList: [],
    orderContactPhone: '',
  });

  const previousCustomer = ref({}) as Customer;
  const cusDetail = ref({
    paymentTerm: '',
    sellerMap: {},
    vflag: false,
    customerServiceMap: {},
    saleOrgVO: {},
  }) as CusDetail;

  const customerReferenceNo = ref('');
  const customerDateSensitive = ref<OrderBooleanType>(false);
  const customerDateSensitiveIndeterminate = ref(false);
  const selectedSalesRange = computed({
    get: () => cusDetail.value.saleOrgVO,
    set: (val) => {
      cusDetail.value.saleOrgVO = {
        ...val,
      };
      console.log(cusDetail.value.saleOrgVO);
    },
  });
  const creator = computed(() => orderStore.orderData.creator);
  const saleOrgList = computed(() =>
    customer.value && customer.value.saleOrgList
      ? customer.value.saleOrgList
          .map((item: SaleOrgList, idx: number) => {
            const {
              salesOrganization,
              productGroup,
              distributionChannel,
              salesOrganizationName,
              distributionChannelName,
              productGroupName,
            } = item;
            return {
              data: {
                idx,
                ...item,
              },
              key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
              value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`,
            };
          })
          ?.filter(
            (so: { data: SaleOrgList }) =>
              orderStore.companyCode &&
              so.data &&
              so.data.salesOrganization &&
              so.data.salesOrganization.slice(0, 2) ===
                orderStore.companyCode.slice(0, 2)
          )
      : []
  );
  function editStore(
    this: {
      $patch: (data: any) => void;
    },
    data: Record<string, any>
  ) {
    this.$patch({
      ...data,
    });
  }
  function resetStoreInfo(data: any) {
    orderStore.editStore({
      orderData: {
        ...orderStore.orderData,
        ...data,
      },
    });
    cusDetail.value = {
      ...cusDetail.value,
      ...data,
      saleOrgVO: cusDetail.value.saleOrgVO,
      paymentTerm: data.paymentTermCode,
    };
    invoiceStore.initInvoiceData({
      ...invoiceStore.invoiceData,
      ...orderStore.orderData,
      receivingInvoiceContact: invoiceStore.invoiceData.receivingInvoiceContact,
    });
    deliveryStore.initDeliveryData(orderStore.orderData);
    deliveryStore.updateDeliveryData({
      specifiedReceiptDayOfWeek: orderStore.orderData.specifiedReceiptDayOfWeek
        ?.split(',')
        ?.filter(Boolean),
      receiptTimeCategory: orderStore.orderData.receiptTimeCategory === 'X',
    });
    // 更新行上的交货挂起原因
    if (itemStore.itemList.length > 0) {
      itemStore.updateItemProps({
        dnOrderPendingReasons: data.dnOrderPendingReasons
          ?.split(',')
          ?.filter((item: string) => !!item),
      });
    }
  }
  // 销售范围、收货联系人、首行sku变更时调用
  async function getClientDetail(data: any) {
    const {
      customerNumber,
      distributionChannel,
      productGroup,
      salesOrganization,
      receiverContactId,
    } = data;
    try {
      orderStore.editStore({
        pageLoading: true,
      });
      const res = await getClientDetailAPI(
        customerNumber,
        distributionChannel,
        productGroup,
        salesOrganization,
        receiverContactId
      );
      if (res.code === 200) {
        resetStoreInfo(res.data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      orderStore.editStore({
        pageLoading: false,
      });
    }
  }

  async function changeClientDetail(checkMulti?: boolean) {
    // checkMulti为true表示多行sku时也调用，场景：快速导入sku
    if (
      itemStore.itemList.length === 1 ||
      (checkMulti && itemStore.itemList.length > 0)
    ) {
      const goodsItem = itemStore.itemList.find((item) => item.skuNo);
      const { salesOrganization, distributionChannel } =
        selectedSalesRange.value || {};
      if (goodsItem && goodsItem.productGroup) {
        await getClientDetail({
          customerNumber: customer.value?.customerNumber,
          productGroup: goodsItem.productGroup,
          distributionChannel,
          salesOrganization,
          receiverContactId:
            contactData.value?.receiverContact?.contactId || '',
        });
      }
    }
  }
  return {
    customer,
    orderSource,
    cusDetail,
    customerReferenceNo,
    selectedSalesRange,
    previousCustomer,
    customerDateSensitive,
    customerDateSensitiveIndeterminate,
    creator,
    contactData,
    saleOrgList,
    editStore,
    getClientDetail,
    changeClientDetail,
  };
});
