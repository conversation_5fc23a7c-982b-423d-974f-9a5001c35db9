import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { from814OrderBasis } from '@/constants/order';
import request from '@/utils/request';
import { useCommonStore } from './common';

export const useOrderStore = defineStore('order', () => {
  const commonStore = useCommonStore();
  const route = useRoute();
  // 有orderNo视为草稿单
  const isDraftDetail = computed(() => !!route.params.orderNo);

  // 如果单号以BZXDD开头，就是不展示原有的特征
  const isFakeSketch = computed(() => {
    try {
      const sketchOrderFake =
        commonStore.dictList?.sketchOrderFake?.find(
          (item) =>
            item.listKey === 'sketchOrderFake' && item.status === 'normal'
        )?.code || '';
      if (!sketchOrderFake) return false;
      const [prefix, result] = sketchOrderFake?.split('_') || '';
      const orderNo = route.params.orderNo as string;
      if (prefix && orderNo) {
        return orderNo.startsWith(prefix) && result === 'false';
      }
      return false;
    } catch (error) {
      console.log(error);
      return false;
    }
  });

  // 814订单
  const isSAP814 = computed(() =>
    from814OrderBasis.includes(orderData.value?.orderBasis || '')
  );

  const pageLoading = ref(false);

  const bossId = ref('');

  const orderData: Record<string, any> = ref({});

  const companyCode = ref('');
  const categoryCode = ref('');

  const isHeaderDisabled = computed(
    () => orderData.value?.headerEditable === '0'
  );
  function editStore(
    this: {
      $patch: (data: any) => void;
    },
    data: Record<string, any>
  ) {
    this.$patch({
      ...data,
    });
  }

  // 允许选仓白名单，不在白名单内的客户选择直发和库位置灰
  const allowSelectWhsAuth = ref(false);
  async function checkSelectWhsAuth(params: {
    customerNo: string;
    orderBasis: string;
    orderReason: string;
    orderType: string;
    salesOrg: string;
    orderSource: string;
  }) {
    if (!params.customerNo || !params.salesOrg) return;
    const res = await request({
      url: '/api-opc/v2/sketch/checkSelectWhsAuth',
      method: 'get',
      params,
    });
    if (res.code === 200) {
      allowSelectWhsAuth.value = res.data;
    }
  }

  return {
    isDraftDetail,
    isFakeSketch,
    isSAP814,
    pageLoading,
    bossId,
    orderData,
    companyCode,
    categoryCode,
    isHeaderDisabled,
    allowSelectWhsAuth,
    editStore,
    checkSelectWhsAuth,
  };
});
