import { ref } from 'vue';
import { defineStore } from 'pinia';
import {
  getCostCenter,
  getDictVersion,
  getDictionaryList,
  getOpsAuthorization,
  getOrderServiceMap,
  getUser,
  listDict,
  queryAcceptFileType,
  queryButtonAuthConfig,
  queryOrderBasisFactory,
  querySearchSkuSwitch,
  queryTemplateExcelUrls,
} from '@/api/order';
import { patchOrderServiceOptions } from '@/utils/orderService';
import { isPro } from '@/utils/index';
import request from '@/utils/request';
// import json from '../../templateData/库存出清订单/库存出清_转正式单失败.json';
import type {
  DictList,
  MMDictList,
  MMDictType,
  MenuItem,
  OrderBasisConfig,
  OrderFieldsSettings,
  OrderTemplateKeyJson,
  Role,
  SalesDictType,
} from '@/types/common';

const formatDictList = <T>(list: T[], key: keyof T) => {
  const dictList: Record<string, T[]> = {};
  list.forEach((item: T) => {
    if (key) {
      const listKey = item[key] as string;
      if (!dictList[listKey]) {
        dictList[listKey] = [];
      }
      dictList[listKey].push(item);
    }
  });
  return dictList;
};

export const useCommonStore = defineStore('common', () => {
  const dictList = ref<DictList>({});

  async function queryListDict() {
    const res = await listDict();
    if (res.code === 200) {
      localStorage.setItem('OMS_DICT_VALUES_DATA', JSON.stringify(res.data));
      dictList.value = formatDictList<SalesDictType>(res.data, 'listKey');
    }
  }

  async function getDictList() {
    const versionRes = await getDictVersion();
    if (versionRes.code === 200) {
      const dictVersion = versionRes.data && versionRes.data.toString();
      const localDictVersion = localStorage.getItem('OMS_DICT_VALUES_VERSION');
      const localDictData = localStorage.getItem('OMS_DICT_VALUES_DATA');
      if (dictVersion === localDictVersion && localDictData) {
        dictList.value = formatDictList<SalesDictType>(
          JSON.parse(localDictData),
          'listKey'
        );
      } else {
        localStorage.setItem('OMS_DICT_VALUES_VERSION', dictVersion);
        queryListDict();
      }
    } else {
      queryListDict();
    }
  }

  const orderServiceDict = ref<Record<string, any>>({});
  async function getOrderServiceDict() {
    const res = await getOrderServiceMap();
    if (res.code === 200) {
      orderServiceDict.value = patchOrderServiceOptions(res.data);
    }
  }

  const mmDictList = ref<MMDictList>({});

  async function getMMDictList() {
    const res = await getDictionaryList();
    if (res.data) {
      mmDictList.value = formatDictList<MMDictType>(res.data, 'type');
    }
  }

  const searchSkuSwitch = ref(false);
  async function getSearchSkuSwitch() {
    const res = await querySearchSkuSwitch();
    if (res.code === 200) {
      searchSkuSwitch.value = res.data;
    }
  }

  const userName = ref('');
  const rolesInfoList = ref<string[]>([]);
  async function getUserRole() {
    const res = await getUser();
    if (res && res.data) {
      rolesInfoList.value =
        res.data?.roleInfoList?.map(
          (item: Record<string, unknown>) => item.name
        ) || [];
      userName.value = res.data?.username || '';
    }
  }

  const sapReturnOrderValidator = ref(false);
  async function getSapReturnOrderValidator() {
    const params = {
      authKey: 'whiteOperateUser',
      authType: 'whitelist',
      authValue: userName.value,
      businessType: 'SapReturnOrderValidator',
      domain: 'SO',
    };
    const res = await getOpsAuthorization(params);
    if (res.code === 200 && Array.isArray(res.data) && res.data.length > 0) {
      sapReturnOrderValidator.value = true;
    }
  }

  // to-do 合单发货灰度，全量放开后删除
  const isShowCombinedDelivery = ref(false);
  async function getGrayWhiteSwitch() {
    const res = await request({
      url: '/api-opc/v1/so/template/whiteSwitch',
      method: 'get',
    });
    if (res && res.data && res.data === true) {
      isShowCombinedDelivery.value = true;
    } else {
      isShowCombinedDelivery.value = false;
    }
  }

  const pageLoading = ref(false);
  function changeLoading(value: boolean) {
    pageLoading.value = value;
  }
  const costCenterList = ref<Record<string, any>[]>([]);
  function queryCostCenter(salesOrganization: string) {
    getCostCenter({
      companyCode: salesOrganization,
    }).then((res) => {
      if (res) {
        console.log(res);
        costCenterList.value = res.data;
      }
    });
  }

  const excelUrls = ref<Record<string, string>>({});
  function getTemplateExcelUrls() {
    queryTemplateExcelUrls().then((res) => {
      if (res) {
        excelUrls.value = res;
      }
    });
  }

  const acceptFileType = ref<Record<string, string>>({});

  function getAcceptFileType() {
    queryAcceptFileType().then((res) => {
      if (res) {
        acceptFileType.value = res;
      }
    });
  }

  const orderBasisConfig = ref<Record<string, OrderBasisConfig[]>>({});
  // 库存出清销售组织及行工厂配置接口
  function getOrderBasisFactory() {
    queryOrderBasisFactory().then((res) => {
      if (res && res.code === 200) {
        orderBasisConfig.value = res.data;
      }
    });
  }

  const menuList = ref<MenuItem[]>([]);

  const env = isPro() ? 'pro' : 'uat';
  const appids = {
    pro: 138,
    uat: 277,
  };
  const appid = appids[env];

  const setMenu = (root: MenuItem[]): MenuItem[] => {
    return root
      .filter((item: MenuItem) => item.type === 'MENU')
      .map((item: MenuItem) => {
        if (item.icon) {
          return { ...item, icon: item.icon ? `icon-${item.icon}` : '' };
        } else if (item.children) {
          return { ...item, children: setMenu(item.children) };
        } else {
          return item;
        }
      });
  };
  async function getMenuList() {
    console.log(window.location);
    const res = await request(`/security-api/resources?appId=${appid}&level=3`);
    if (res && res.children) {
      menuList.value = setMenu(res.children);
    }
  }

  const roleList = ref<Role[]>([]);

  async function getRoleList() {
    const res = await request({
      url: '/api-opc-csc/api/user/infoByDomain',
      method: 'get',
      params: {
        domain: userName.value,
      },
    });
    if (res?.code === 200) {
      roleList.value = res.data.bizRoleList;
    }
  }

  const holidaysList = ref<string[]>([]);
  async function getHolidays() {
    const res = await request({
      url: '/api-opc/date/holidays',
      method: 'get',
    });
    if (res.data) {
      holidaysList.value = res.data;
    }
  }

  const orderFieldsSettings = ref<Record<string, OrderFieldsSettings>>({});
  async function getOrderFieldsSettings(configData: OrderTemplateKeyJson) {
    try {
      // orderFieldsSettings.value = json.templateInfo;
      // console.log('------', orderFieldsSettings.value);
      if (configData) {
        const res = await request({
          url: '/api-form-center/front-end/display/template/query',
          method: 'post',
          data: configData,
        });
        console.log('订单配置化------', res);
        if (res.code === 200 && res.data?.records) {
          const json = res.data?.records[0]?.templateInfo || {};
          console.log(json);
          orderFieldsSettings.value = json;
        } else {
          orderFieldsSettings.value = {};
        }
      } else {
        orderFieldsSettings.value = {};
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 按钮权限配置化开关
  const buttonAuthConfig = ref<Record<string, boolean>>({});
  async function getButtonAuthConfig() {
    try {
      const res = await queryButtonAuthConfig();
      if (res) {
        buttonAuthConfig.value = res;
      }
    } catch (error) {
      console.log(error);
    }
  }

  return {
    dictList,
    orderServiceDict,
    mmDictList,
    searchSkuSwitch,
    rolesInfoList,
    userName,
    sapReturnOrderValidator,
    pageLoading,
    costCenterList,
    excelUrls,
    acceptFileType,
    menuList,
    roleList,
    holidaysList,
    isShowCombinedDelivery,
    orderFieldsSettings,
    buttonAuthConfig,
    orderBasisConfig,
    getDictList,
    queryListDict,
    getOrderServiceDict,
    getMMDictList,
    getSearchSkuSwitch,
    getUserRole,
    getSapReturnOrderValidator,
    changeLoading,
    queryCostCenter,
    getTemplateExcelUrls,
    getAcceptFileType,
    getMenuList,
    getRoleList,
    getHolidays,
    getGrayWhiteSwitch,
    getOrderFieldsSettings,
    getButtonAuthConfig,
    getOrderBasisFactory,
  };
});
