/**
 * @Author: luozhikai
 * @Date: 2023-09-13 17:17:47
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-11-15 10:42:13
 * @Description: file content
 */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { ContactType } from '@/types/common';

export interface InvoiceState {
  receivingInvoiceContact: ContactType | null | '';
  receivingInvoiceContactList: ContactType[];
  invoicePhone: string;
  invoiceAddress: string;
  corporationTaxNum: string;
  invoiceAddressTelephone: string;
  bankName: string;
  bankNumber: string;
  customerPayAccountTypeName: string;
  shippingInfo: string;
  invoicingByMail: boolean;
  returnOffset: boolean;
  mergeBilling: boolean;
  autoBilling: boolean;
  billingRobot: boolean;
  showDiscount: boolean;
  ifDocMailed: boolean;
  invoiceType: string;
  mergeBillingDemand: string;
  expressCompany: string;
  financialNote: string;
}

export const boolProps = [
  'invoicingByMail',
  'returnOffset',
  'mergeBilling',
  'autoBilling',
  'billingRobot',
  'showDiscount',
  'ifDocMailed',
];
const allInvoiceProps = [
  'receivingInvoiceContact',
  // 收票联系人下拉
  'receivingInvoiceContactList',
  'invoicePhone',
  'invoiceAddress',
  // 编辑更多发票信息
  'corporationTaxNum',
  'invoiceAddressTelephone',
  'bankName',
  'bankNumber',
  'customerPayAccountTypeName',
  'shippingInfo',
  'invoicingByMail',
  'returnOffset',
  'mergeBilling',
  'autoBilling',
  'billingRobot',
  'showDiscount',
  'ifDocMailed',
  'invoiceType',
  'mergeBillingDemand',
  'expressCompany',
  'financialNote',
];

export const formatInitialBoolean = (prop: string, value: any) => {
  if (boolProps.includes(prop)) {
    if (value === 'X' || value === true) return true;
    return false;
  }
  return value;
};

export const useInvoiceStore = defineStore('invoice', () => {
  const invoiceData = ref<InvoiceState>({
    // 其他信息收票信息
    receivingInvoiceContact: null,
    // 收票联系人下拉
    receivingInvoiceContactList: [],
    invoicePhone: '',
    invoiceAddress: '',
    // 编辑更多发票信息
    corporationTaxNum: '',
    invoiceAddressTelephone: '',
    bankName: '',
    bankNumber: '',
    customerPayAccountTypeName: '',
    shippingInfo: '',
    invoicingByMail: false,
    returnOffset: false,
    mergeBilling: false,
    autoBilling: false,
    billingRobot: false,
    showDiscount: false,
    ifDocMailed: false,
    invoiceType: '01',
    mergeBillingDemand: '',
    expressCompany: '1',
    financialNote: '',
  });

  function initInvoiceData(data: any) {
    const temp: any = {};
    for (const prop of Object.keys(data)) {
      if (allInvoiceProps.includes(prop)) {
        temp[prop] = formatInitialBoolean(prop, data[prop]);
      }
    }

    invoiceData.value = {
      ...invoiceData.value,
      ...temp,
    };
  }

  function clearInvoiceData() {
    invoiceData.value = {
      receivingInvoiceContact: null,
      receivingInvoiceContactList: [],
      invoicePhone: '',
      invoiceAddress: '',
      corporationTaxNum: '',
      invoiceAddressTelephone: '',
      bankName: '',
      bankNumber: '',
      customerPayAccountTypeName: '',
      shippingInfo: '',
      invoicingByMail: false,
      returnOffset: false,
      mergeBilling: false,
      autoBilling: false,
      billingRobot: false,
      showDiscount: false,
      ifDocMailed: false,
      invoiceType: '01',
      mergeBillingDemand: '',
      expressCompany: '1',
      financialNote: '',
    };
  }

  function updateInvoiceData(
    this: {
      $patch: (data: any) => void;
    },
    payload: Partial<InvoiceState>
  ) {
    this.$patch({
      invoiceData: { ...invoiceData.value, ...payload },
    });
  }

  return { invoiceData, initInvoiceData, clearInvoiceData, updateInvoiceData };
});
