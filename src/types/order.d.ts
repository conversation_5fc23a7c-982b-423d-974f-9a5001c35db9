export type CustomPropertyList = {
  placeholder: string;
  customProperty: string;
  customPropertyRemark: string;
};

export interface SkuFactoryPrice {
  skuNo: string;
  suggestPrice: string;
  dealerSuggestPrice: string;
  dealerMinPrice: string;
  factory: string;
  taxRate: string;
  taxRateInPoint: number;
}

export type SkuFactoryPriceMap = Record<string, SkuFactoryPrice>;

export type AttributeTagConfig = Record<string, string>;

export type itemValidatorResult = {
  errorLevel: string;
  processKey: string;
  validateCode: string;
  validateMsg: string;
  validateName: string;
};
export type ItemResult = {
  itemValidatorResultList: itemValidatorResult[];
};
export interface ItemAount {
  taxedTotal: number;
  unTaxedTotal: number;
  taxedDiscountTotal: number;
  unTaxedDiscountTotal: number;
}

export interface OrderTotalAount {
  allTaxTotalPrice: number;
  allTaxTotalDiscountPrice: number;
  allFreeTotalPrice: number;
  allFreeTotalDiscountPrice: number;
}

export type ProductSaleOrgExt = {
  mtart: string;
  mtartName: string;
  vkbur: string;
};

export type PackageInfo = {
  skuNo: string;
  unitName: string;
  ruleDes: string;
  conversion: number;
  unit: string;
};

export interface UploadSkuInfoDetailVO {
  brandId: string;
  brandName: string;
  commoditySourceType: string;
  coreSpecification: string;
  customPropertyList: CustomPropertyList[];
  customerDate: string;
  customerMaterialName: string;
  customerMaterialNo: string;
  customerMaterialQuantity: string;
  customerMaterialRelationId: string;
  customerMaterialUnit: string;
  customerMaterialUnitCode: string;
  customerMaterialUnitName: string;
  customerOrderNo: string;
  customerPrice: number;
  customerPriceTax: string;
  customerSpecificationModel: string;
  deliveryCycle: number;
  deliveryDate: string;
  deliveryDateStr: string;
  deliveryFrequency: number;
  demandDepartment: string;
  demandUser: string;
  directDeliverySupplier: string;
  factory: string;
  factoryMtartMap: FactoryMtartMap;
  factoryPriorityList: any[];
  factoryProductPriceVOMap: FactoryProductPriceVomap;
  failReason: string;
  fastenerLogo: boolean;
  fillDefaultCustomProperty: string;
  ifVpi: string;
  largeReduceReason: string;
  largeReduceReasonDesc: string;
  manuDirectoryNo: string;
  manufacturerNo: string;
  materialDescribe: string;
  mtart: string;
  no: number;
  packageInfoList: PackageInfoList[];
  position: string;
  positionModifyDetail: string;
  positionModifyReason: string;
  price: number;
  productGroup: string;
  productName: string;
  productSaleOrgExtList: ProductSaleOrgExtList[];
  purchaseNote: string;
  quantity: number;
  recentDeliveryDate: string;
  remark: string;
  sapItemNo: string;
  sapOrderNo: string;
  skuNo: string;
  soItemNo: string;
  unitCode: string;
  unitName: string;
  versionNo: string;
  websiteName: string;
  ifSalePriceChannelLimit: boolean;
}

export interface CustomPropertyList {
  customProperty: string;
  customPropertyRemark: string;
}

export interface FactoryMtartMap {}

export type FactoryProductPriceVomap = Record<string, AdditionalProperties>;

export interface AdditionalProperties {
  branchCompanyPrice: string;
  dealerMinPrice: string;
  dealerSuggestPrice: string;
  factory: string;
  promotionPrice: string;
  saleAuthPrice: string;
  saleManagerAuthPrice: string;
  skuNo: string;
  suggestPrice: string;
  taxRate: string;
  taxRateInPoint: number;
}

export interface PackageInfoList {
  conversion: number;
  ruleDes: string;
  skuNo: string;
  unit: string;
  unitName: string;
}

export interface ProductSaleOrgExtList {
  mtart: string;
  mtartName: string;
  stateCode: number;
  vkbur: string;
}

export interface ClientDetail {
  dnOrderPendingReasons: string;
}
