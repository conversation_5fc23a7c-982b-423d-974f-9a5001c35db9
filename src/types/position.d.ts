// 库位相关类型

// 仓库类型 1.全量库位;2.可售库位;3.集货库位
export type PositionScope = 1 | 2 | 3;

export interface SupplyPosition {
  code: string;
  factory?: string;
  isAvailable?: string;
  mrpPosition?: string;
  name: string;
  qualityStatus?: string;
  // 自定义attr
  sku?: string;
  warehouseCode?: string;
}

export interface SuppplyData {
  allPosition: SupplyPosition[];
  sku: string;
  warehouseCode: string;
}

export type SimPositionType = 'position' | 'delivery';
