export interface PriceData {
  discountFreeTotalPrice: number;
  discountTotalTaxAmount: number;
  freeTotalPrice: number;
  taxTotalPrice: number;
  totalProductQuantity: number;
  totalPromotionalDiscountRate: number;
  totalTaxDiscountAmount: number;
  totalUntaxedDiscountAmount: number;
  priceItems: PriceItems[];
}

export interface PriceItems {
  discountTaxIncludedUnitPrice: number;
  discountUntaxedUnitPrice: number;
  freeTaxSaleAssessmentUnitPrice: number;
  freeTotalPrice: number;
  promotionalDiscountRate: number;
  taxDiscountAmount: number;
  taxPrice: number;
  taxTotalPrice: number;
  untaxedDiscountAmount: number;
  untaxedUnitPrice: number;
  itemNo: string;
  sku: string;
}
