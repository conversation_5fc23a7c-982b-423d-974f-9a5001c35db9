import type { OrderBooleanType, OrderType } from './common';

export type BooleanType = Extract<OrderBooleanType, 'X' | 'Z'> | '';

export interface ContactInfo {
  orderContact: number;
  orderContactAddress: string;
  orderContactCity: string;
  orderContactCityCode: string;
  orderContactDistrict: string;
  orderContactDistrictCode: string;
  orderContactName: string;
  orderContactPhone: string;
  orderContactPostalCode: string;
  orderContactProvince: string;
  orderContactProvinceCode: string;
  orderContactTown: string;
  orderContactTownCode: string;
  receiverAddress: string;
  receiverCity: string;
  receiverCityCode: string;
  receiverContact: number;
  receiverDistrict: string;
  receiverDistrictCode: string;
  receiverName: string;
  receiverPhone: string;
  receiverPostalCode: string;
  receiverProvince: string;
  receiverProvinceCode: string;
  receiverTown: string;
  receiverTownCode: string;
  receivingInvoiceAddress: string;
  receivingInvoiceCity: string;
  receivingInvoiceCityCode: string;
  receivingInvoiceContact: number;
  receivingInvoiceDistrict: string;
  receivingInvoiceDistrictCode: string;
  receivingInvoiceName: string;
  receivingInvoicePhone: string;
  receivingInvoicePostalCode: string;
  receivingInvoiceProvince: string;
  receivingInvoiceProvinceCode: string;
  receivingInvoiceTown: string;
  receivingInvoiceTownCode: string;
}
export type DetailOrder = {
  acceptSupplierDelivery: number;
  agreementNote: string;
  attributeTagConfig: AttributeTagConfig;
  attributionTitleCode: string;
  attributionTitleName: string;
  autoBatching: string;
  autoBilling: string;
  autoDelivery: string;
  backupOrder: string;
  bidCustomer: string;
  billingAddressAndPhone: string;
  billingRobot: string;
  canAcceptDemand: boolean;
  certificateIdentification: string;
  changeTitle: string;
  clearSlackStock: string;
  clearStatus: string;
  collectionAmount: number;
  creditApplicationReason: string;
  creditApprovalReason: string;
  creditLimitFreeze: string;
  creditPromise: boolean;
  creditWhitelistEndDate: string;
  creditWhitelistStartDate: string;
  customerAddress: string;
  customerCity: string;
  customerDeliveryConfirmed: string;
  customerName: string;
  customerNo: string;
  customerPayAccountTypeName: string;
  customerPurchaseDate: string;
  customerReferenceDate: string;
  customerReferenceNo: string;
  customerTag: string;
  deliveryFreezeDetailList: DeliveryFreezeDetailList[];
  deliveryManagerName: string;
  deliveryManagerPhone: string;
  deliveryOrderNo: string;
  deliveryOtherNote: string;
  deliveryReplyTimeliness: string;
  deliveryRequirements: string;
  deliverySensitivityInterval: string;
  deliverySiteCollection: string;
  deliverySlipTemplate: string;
  deliveryStatus: string;
  deliveryUnloadingReq: string;
  deliveryWarehouseInfo: string;
  demandDepartment: string;
  designatedShipping: string;
  directDeliverySupplier: string;
  directSupplier: string;
  disableShipping: string;
  distributionChannel: string;
  dnIncidentalWay: string;
  dnPaperReq: string;
  dnSignatureReq: string;
  enterprisePaidAmount: number;
  errorMsg: string;
  evmMachineNo: string;
  exchangeRate: string;
  exportProcessingZone: string;
  expressCompany: string;
  fastenerDetect: string;
  fastenerLabelReq: string;
  fastenerSpecialPackageReq: string;
  financialNote: string;
  forkliftRelated: string;
  hasDeliveryManager: string;
  ifDocMailed: string;
  individualPaidAmount: number;
  invoiceReceiver: string;
  invoiceReceiverName: string;
  invoiceStatus: string;
  invoiceType: string;
  invoicingByMail: string;
  isCollectWhiteList: boolean;
  isTax: string;
  labelPasteWay: string;
  labelTemplate: string;
  leftCreditLimit: number;
  mergeBilling: string;
  mergeBillingDemand: string;
  modifyTime: string;
  orderCreateTime: string;
  orderLabel: string;
  orderNo: string;
  orderNote: string;
  orderReason: string;
  orderSource: string;
  orderStatus: string;
  orderType: OrderType;
  otherLabelReq: string;
  overdueFreeze: string;
  packagingReq: string;
  paid: string;
  paymentNote: string;
  paymentTerm: string;
  paymentTermName: string;
  preOrderTag: string;
  printNum: number;
  productGroup: string;
  projectDepartment: string;
  projectNo: string;
  purchaseAgent: string;
  referenceStandardShippingReq: string;
  requestSapStatus: string;
  requestSapStatusMsg: string;
  requestedDeliveryDate: string;
  responsiblePerson: string;
  returnOffset: string;
  returnReason: string;
  salesGroup: string;
  salesOffice: string;
  salesOrganization: string;
  sapDemandDepartment: string;
  sapOrderNo: string;
  sapOrderSource: string;
  sapRespInfo: string;
  sapReturnDnNo: string;
  sapReturnDnNoCanModify: boolean;
  scheduleDelivery: string;
  serviceCenterSelfTransport: string;
  shippingCondition: string;
  shippingConditionName: string;
  shippingInfo: string;
  showDiscount: string;
  signingBack: string;
  signingReq: string;
  sketchOrderCompleted: string;
  sketchOrderNo: string;
  soAddTime: string;
  soNo: string;
  soVoucherId: number;
  specifiedDocument: string;
  specifiedReceiptDayOfWeek: string;
  specifiedReceiptTime: string;
  subOrderSource: string;
  supplierAccount: string;
  supplierCode: string;
  totalCreditLimit: number;
  updateByExcelSwitch: boolean;
  vehicleReq: string;
  virtualReturn: string;
  items: DetailItem[];
  customerServiceId: string;
  customerServicePhone: string;
  customerServiceName: string;
  customerServiceEmail: string;
  sellerId: string;
  sellerEmail: string;
  sellerName: string;
  customerServiceSupervisorName: string;
  salesManagerName: string;
  creator: string;
  billingFreeze: string;
  deliveryFreeze: string;
  attachOrder: BooleanType;
  attachCoa: BooleanType;
  attachMsds: BooleanType;
  attachTds: BooleanType;
  receiptTimeCategory: BooleanType;
  hideLogo: BooleanType;
  deliverySensitivity: BooleanType;
  hasDelivered: string;
  orderBasis: string;
} & ContactInfo;

export interface AttributeTagConfig {}

export interface DeliveryFreezeDetailList {
  deliveryFreeze: string;
  material: string;
  soItemNo: number;
  soNo: string;
}

export interface DetailItem {
  acceptDemand: string;
  appointChannel: string;
  arrivalDate: string;
  baseUnitQty: number;
  batchNumber: string;
  buyerName: string;
  clearStatus: string;
  cleared: boolean;
  clearedQty: number;
  conditionType: string;
  conditionalPricingUnit: string;
  costCenter: string;
  costCenterDesc: string;
  createTime: string;
  currency: string;
  customPropertyList: CustomPropertyList[];
  customerDate: string;
  customerDateSensitive: string;
  customerMaterialName: string;
  customerMaterialNo: string;
  customerMaterialQuantity: string;
  customerMaterialUnit: string;
  customerMaterialUnitName: string;
  customerMaterialUnitPrice: number;
  customerOrderNo: string;
  customerSpecificationModel: string;
  dedicatedMaterial: string;
  deliveryCancelQty: number;
  deliveryDate: string;
  deliveryLeadTime: number;
  deliveryPosition: string;
  deliveryPostStatus: string;
  deliveryQty: number;
  deliveryQuantity: number;
  deliveryReceiverLeadTime: number;
  deliveryShippingLocation: string;
  deliveryStatus: string;
  deliveryWarehouseCode: string;
  demandDepartment: string;
  demandUser: string;
  directDeliverySupplier: string;
  directionalTag: string;
  discountAmount: string;
  discountConditionType: string;
  discountPrice: string;
  discountTaxIncludedUnitPrice: number;
  discountUntaxedUnitPrice: number;
  dnList: DnList[];
  estimateDeliveryTime: string;
  factory: string;
  fastenerLogo: boolean;
  fillDefaultCustomProperty: string;
  firstArrivalDate: string;
  firstDeliveryDate: string;
  firstPlanArrivalDate: string;
  firstPlanDeliveryDate: string;
  fixedAssetsId: string;
  freeTaxPrice: number;
  freeTotalPrice: number;
  generalLedgerAccount: string;
  hasTcSupplier: boolean;
  ifVpi: string;
  inventoryLossQuantity: number;
  invoiceQuantity: number;
  invoiceStatus: string;
  invoiceTaxIncludedUnitPrice: number;
  invoiceUntaxedUnitPrice: number;
  isCollectAll: boolean;
  itemAttributeTags: string[];
  itemType: string;
  leadTimeStatusSign: string;
  manufacturerModel: string;
  materiel: string;
  modifyTime: string;
  needCollect: boolean;
  needScrapingCode: string;
  occupyQty: number;
  omsReferenceOrderItemNo: string;
  orderItemNo: string;
  orderPlanArrivalDate: string;
  orderPlanDeliveryDate: string;
  originSkuArrivalDate: string;
  originSkuDeliveryDate: string;
  originStatusSign: string;
  picUrl: string;
  planArrivalDate: string;
  planDeliveryDate: string;
  position: string;
  positionModifyDetail: string;
  positionModifyReason: string;
  productGroup: string;
  productGroupName: string;
  productSaleName: string;
  productServiceName: string;
  purchaseNote: string;
  quantity: number;
  quantityConversionRel: string;
  quantityUnit: string;
  quantityUnitName: string;
  readyForDnQty: number;
  referType: string;
  referenceOrderItemNo: string;
  referenceOrderNo: string;
  remark: string;
  rentalDueDate: string;
  replenishNum: number;
  sapItemNo: string;
  sapMaterialName: string;
  sapOrderNo: string;
  shippingPosition: string;
  shippingWarehouse: string;
  sketchOrderItemNo: string;
  skuArrivalDate: string;
  skuAttributeTags: string[];
  soItemNo: string;
  soNo: string;
  soVoucherItemId: number;
  status: string;
  supplierPlanArrivalDate: string;
  supplierPlanDeliveryDate: string;
  sysDeliveryDate: string;
  tax: number;
  taxIncludedUnitPrice: number;
  taxPrice: number;
  taxRate: number;
  taxTotalPrice: number;
  transferInQty: number;
  transferLeadTime: number;
  transferOutQty: number;
  unitPrice: number;
  untaxedUnitPrice: number;
  urgent: string;
  warehouseCode: string;
  itemPlanDTOList: ItemPlanDtolist[];
  largeReduceReason: string;
  largeReduceReasonDesc: string;
  skuFactoryPriceMap: SkuFactoryPriceMap;
  mtart: string;
  refuseSystemDeliveryDate: BooleanType;
}

export interface CustomPropertyList {
  customProperty: string;
  customPropertyRemark: string;
}

export interface DnList {
  deliveryStatus: string;
  dnCreateAt: string;
  dnItemNo: string;
  dnNo: string;
  dnQty: string;
  itemInvoiceStatus: string;
  sapDnItemNo: string;
  sapDnNo: string;
  sapItemNo: string;
  sapOrderNo: string;
  soItemNo: string;
  soItemPlanNo: string;
  soNo: string;
  transferStatus: string;
  warehouseStatus: string;
}

export interface ItemPlanDtolist {
  clearedQty: number;
  confirmQuantity: number;
  confirmedQtyType: string;
  customerMaterialName: string;
  customerMaterialNo: string;
  deliveryDate: string;
  deliveryDateType: string;
  deliveryQuantity: number;
  demandDepartment: string;
  demandUser: string;
  factory: string;
  id: string;
  itemPlanType: string;
  itemQuantity: number;
  itemType: string;
  materiel: string;
  materielName: string;
  position: string;
  salesUnit: string;
  sapItemNo: string;
  sapItemPlanNo: string;
  sapOrderNo: string;
  shippingLocation: string;
  soItemNo: string;
  soItemPlanNo: string;
  soNo: string;
  vrkme: string;
}

export interface SkuFactoryPriceMap {
  additionalProperties1: AdditionalProperties1;
}

export interface AdditionalProperties1 {
  branchCompanyPrice: string;
  dealerMinPrice: string;
  dealerSuggestPrice: string;
  factory: string;
  promotionPrice: string;
  saleAuthPrice: string;
  saleManagerAuthPrice: string;
  skuNo: string;
  suggestPrice: string;
  taxRate: string;
  taxRateInPoint: number;
}

export type CostCenterList = {
  costCenter: string;
  description: string;
};
