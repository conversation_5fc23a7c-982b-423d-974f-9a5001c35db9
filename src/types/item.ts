import type { CompanyType, OrderBooleanType, SalesDictType } from './common';

export interface ItemType {
  uuid?: string;
  idx?: number;
  customerDateSensitive?: OrderBooleanType;
  factoryProductPriceVOMap: FactoryProductPriceVOMapType;
  relateType?: number;
  factory?: CompanyType;
  getFactoryList?: () => SalesDictType[];
  _suggestPrice?: number;
  // 基础人民币含税价格
  _taxPrice?: number;
  // 基础人民币未税价格
  _freeTaxPrice?: number;
  [key: string]: any;
}

export enum ItemRowType {
  factory = 'factory',
  quantity = 'quantity',
  taxPrice = 'taxPrice',
  freeTaxPrice = 'freeTaxPrice',
  directDeliverySupplier = 'directDeliverySupplier',
  position = 'position',
  customerDate = 'customerDate',
  refuseSystemDeliveryDate = 'refuseSystemDeliveryDate',
  costCenter = 'costCenter',
  generalLedgerAccount = 'generalLedgerAccount',
  deliveryDate = 'deliveryDate',
  all = 'all',
}

export type FactoryProductPriceVOMapType = {
  [key in CompanyType as number]: FactoryProductPriceVOType;
};

export interface FactoryProductPriceVOType {
  dealerMinPrice: number;
  dealerSuggestPrice: number;
  suggestPrice: number;
  taxRate: number;
  taxRateInPoint: number;
}

export enum DirectDeliverySupplierEnum {
  ZKH = '0', // 震坤行发货
  SUPPLIER = '1', // 供应商直发
  SYS = '2', // 系统自动判断
}

export interface WaveFillItem {
  waveDeliveryDate: string;
  skuArrivalDate: string;
  sysDeliveryDate: string;
  originSkuArrivalDate: string;
  originSkuDeliveryDate: string;
  index: number;
}

export interface WaveDeliveryDateResponse {
  material: string;
  promoteInfo: string;
  quantity: number;
  waveDeliveryDate: string;
  skuArrivalDate: string;
  sysDeliveryDate: string;
  originSkuArrivalDate: string;
  originSkuDeliveryDate: string;
}

export interface CommonPosition {
  code: string | number;
  name: string;
  [key: string]: any;
}

export interface CommonPositionMap {
  [key: string]: Array<CommonPosition>;
}

export interface MaterialRelationRow {
  customerMaterialNo: string;
  customerMaterialSpecification: string;
  customerMaterialName: string;
  customerMaterialStandardQuantity: string;
  customerMaterialUnit: string;
  zkhSkuStandardQuantity: string;
}

export interface ItemAmountType {
  taxedTotalAmount?: number;
  untaxedTotalAmount?: number;
  taxedDiscountTotal?: number;
  unTaxedDiscountTotal?: number;
  [key: string]: any;
}

export interface IDetailData {
  [prop: string]: any;
}

export interface FactoryItem {
  code: string;
  id: number;
  listKey: string;
  listName: string;
  name: string;
  parentCode: string;
  status: string;
}

export type JumpToOuterType = {
  soNo: string;
  unifyApprovalNo: string;
};

export interface SearchItemType {
  skuNo: string;
  materialDescribe: string;
  customerSkuNo: string;
  skuName?: string;
  dataSource: string;
  customerSkuName: string;
  customerSkuUnitCount: number | string;
  customerSkuUnit: string;
  customerSkuSpecification: string;
  referenceHeadId?: string;
  index?: number;
  matchField?: string;
  referenceNo?: string;
  id?: string;
  skuUnitCount?: number;
}

export interface SkuVoType {
  skuNo: string;
  quantity: number;
}

export interface StockSkuInfo {
  batchNo: string;
  factory: CompanyType;
  guaranteePeriod: number;
  leftGuaranteePeriod: number;
  poNo: string;
  position: string;
  qty: number;
  sku: string;
  supplierName: string;
  supplierNo: string;
  unTaxMoveAveragePrice: number;
  taxMoveAveragePrice: number;
  volume: number;
  warehousingDays: number;
  itemNo: number;
}
