export interface CustomerData {
  agreementNote: string;
  autoBilling: string;
  bankName: string;
  bankNumber: string;
  billingRobot: string;
  businessPartnerGroupName: string;
  certificateIdentification: string;
  corporationTaxNum: number;
  customerClassificationName: string;
  customerFreezeReason: string;
  customerId: number;
  customerName: string;
  customerNatureName: string;
  customerNumber: string;
  customerPayAccountTypeName: string;
  customerServiceEmail: string;
  customerServiceId: number;
  customerServiceMap: CustomerServiceMap;
  customerServiceName: string;
  customerSource: number;
  customerSourceName: string;
  deliverParty: string;
  deliveryOtherNote: string;
  deliveryRequirements: string;
  disableShipping: string;
  dnIncidentalWay: string;
  dnPaperReq: string;
  dnSignatureReq: string;
  evmOperator: string;
  evmOperatorId: number;
  expressCompany: string;
  fastenerDetect: string;
  fastenerLabelReq: string;
  fastenerSpecialPackageReq: string;
  financialNote: string;
  hasSaleConfig: boolean;
  ifDocMailed: string;
  invoiceAddressTelephone: string;
  invoiceType: string;
  isTax: string;
  labelPasteWay: string;
  mergeBilling: string;
  mergeBillingDemand: string;
  otherLabelReq: string;
  packagingReq: string;
  paymentNote: string;
  paymentTermCode: string;
  paymentTermName: string;
  printNum: number;
  returnOffset: string;
  saleOrgVO: SaleOrgVo;
  salesGroup: string;
  salesOffice: string;
  scheduleDelivery: string;
  sellParty: string;
  sellerEmail: string;
  sellerId: number;
  sellerMap: SellerMap;
  sellerName: string;
  serviceCenterSelfTransport: string;
  shippingInfo: string;
  showDiscount: string;
  signingReq: string;
  specifiedReceiptDayOfWeek: string;
  specifiedReceiptTime: string;
  vehicleReq: string;
  vflag: boolean;
  referenceStandardShippingReq: string;
  receiptTimeCategory: string;
  autoDelivery: string;
  autoBatching: string;
  acceptSupplierDelivery: number;
  paid: string;
  attachOrder: string;
  specifiedDocument: string;
  exportProcessingZone: string;
  virtualReturn: string;
  bidCustomer: string;
  attachCoa: string;
  attachMsds: string;
  attachTds: string;
  hideLogo: string;
  shippingCondition: string;
  signingBack: string;
  deliverySlipTemplate: string;
  deliveryUnloadingReq: string;
  labelTemplate: string;
  taxType: string;
  billingAddressAndPhone: string;
}

export interface SaleOrgVo {
  salesOrganization: string;
  salesOrganizationName: string;
  distributionChannel: string;
  distributionChannelName: string;
  productGroup: string;
  productGroupName: string;
}

export interface SellerMap {}

export type CustomerServiceMap = Record<string, CustomerService>;

export interface CustomerService {
  userId: string;
  userName: string;
  userEmail: string;
}
