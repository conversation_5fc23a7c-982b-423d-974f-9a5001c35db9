import { UploadUserFile } from 'element-plus';
import {
  PackageInfoList,
  SkuFactoryPriceMap,
  itemValidatorResult,
} from './order';
import { BooleanType, ContactInfo, ItemPlanDtolist } from './detail';
import { PriceData, PriceItems } from './price';
import type { ContactType } from '@/types/common';

export interface EditItemCustom {
  taxRate?: number;
  currency?: string;
  sapMaterialName?: string;
  positionList?: SalesDictType[];
  simPositionList?: SupplyPosition[];
  mtart?: string;
  leadTime?: number;
  costCenter?: string;
  generalLedgerAccount?: string;
  idx?: string;
  skuFactoryPriceMap?: SkuFactoryPriceMap;
  quantityUnit?: string;
  packageInfoList?: PackageInfoList[];
  customerSkuUnitCount?: number;
  skuUnitCount?: number;
  manufacturerNo?: string;
  placeholder?: string;
  replenishNum?: number;
  clearedQty: number;
  discountConditionType?: string;
  // to-do: 后期需要删掉
  isChangeCustomerDate?: boolean; // 是否改变客户期望送达日期
  supplierPlanArrivalDate?: string;
  originSkuArrivalDate?: string;
  suggestPrice?: number;
}

export type EditItem = {
  soItemNo: string;
  quantity: number;
  sapItemNo: string;
  taxPrice: number;
  freeTaxPrice: number;
  factory: string;
} & Partial<{
  materiel: string;
  ifVpi: string;
  acceptDemand: string;
  addType: '1';
  soNo: string;
  customPropertyList: CustomPropertyList[];
  customerDate: string;
  deliveryPosition: string;
  deliveryShippingLocation: string;
  deliveryWarehouseCode: string;
  directDeliverySupplier: string;
  discountAmount: string;
  lastConfirmedPosition: string;
  omsReferenceOrderItemNo: string;
  omsReferenceOrderNo: string;
  purchaseNote: string;
  referType: string;
  urgent: string;
  validateItemNo?: string;
  warehouseCode: string;
  waveDeliveryDate: string;
  customerMaterialNo: string;
  customerMaterialName: string;
  customerMaterialQuantity: number;
  customerMaterialUnit: string;
  customerOrderNo: string;
  customerSpecificationModel: string;
  conditionalUnit: string;
  position: string;
  itemType: string;
  arrivalDate: string;
  deliveryDate: string;
  remark: string;
  positionModifyReason: string;
  positionModifyDetail: string;
  demandUser: string;
  demandDepartment: string;
  receivingInvoiceContact: number;
  largeReduceReason: string;
  largeReduceReasonDesc: string;
  rentalDueDate: string;
  fixedAssetsId: string;
  itemPlanList: ItemPlanDtolist[];
  needScrapingCode: BooleanType;
  autoBatching: BooleanType;
  customerDateSensitive: BooleanType;
  refuseSystemDeliveryDate: BooleanType;
  notAcceptDemandReasons: string;
  dnOrderPendingReasons: string;
  productGroup: string;
  materialReplaceProcessNo: string;
  ifSalePriceChannelLimit: boolean;
}> &
  Partial<PriceItems> &
  EditItemCustom;

export interface CustomPropertyList {
  customProperty: string;
  customPropertyRemark: string;
  // 自定义
  placeholder?: string;
}

export interface EditOrderCustom {
  receiverAddressId?: string;
  wholeCustomerReferenceDate?: string;
  corporationTaxNum?: number;
  bankName?: string;
  bankNumber?: string;
  customerPayAccountTypeName?: string;
  receivingInvoice?: Partial<ContactType>;
  receiverDocMetaData?: DocMetaData; // 更改收票方凭证
}
export type EditOrder = Partial<EditOrderRequired> & {
  items: EditItem[];
} & Partial<ContactInfo> &
  Partial<PriceData> &
  EditOrderCustom;

export interface EditOrderRequired {
  acceptSupplierDelivery: number;
  agreementNote: string;
  backupOrder: string;
  billingAddressAndPhone: string;
  billingRobot: string;
  certificateIdentification: string;
  clearSlackStock: string;
  collectionAmount: number;
  creditPromise: boolean;
  customerDeliveryConfirmed: string;
  customerReferenceNo: string;
  deliveryReplyTimeliness: string;
  deliveryRequirements: string;
  deliverySensitivityInterval: string;
  deliveryWarehouseInfo: string;
  designatedShipping: string;
  directDeliverySupplier: string;
  dnIncidentalWay: string;
  dnPaperReq: string;
  dnSignatureReq: string;
  expressCompany: string;
  fastenerDetect: string;
  fastenerLabelReq: string;
  fastenerSpecialPackageReq: string;
  forkliftRelated: string;
  ifDocMailed: string;
  invoiceReceiver: string;
  invoiceReceiverName: string;
  labelPasteWay: string;
  mergeBilling: string;
  mergeBillingDemand: string;
  orderNo: string;
  orderSource: string;
  orderTracker: string;
  otherLabelReq: string;
  paymentNote: string;
  printNum: number;
  projectDepartment: string;
  requestId: string;
  returnOffset: string;
  sapOrderNo: string;
  sapReturnDnNo: string;
  scheduleDelivery: string;
  serviceCenterSelfTransport: string;
  showDiscount: string;
  signingReq: string;
  specifiedReceiptDayOfWeek: string;
  specifiedReceiptTime: string;
  updateWay: string;
  vehicleReq: string;
  orderReason: string;
  soNo: string;
  deliveryFreeze: string;
  billingFreeze: string;
  customerReferenceDate: string;
  paymentTerm: string;
  orderNote: string;
  receiverContact: number;
  orderContact: number;
  packagingReq: string;
  referenceStandardShippingReq: string;
  autoDelivery: string;
  autoBatching: string;
  paid: string;
  projectNo: string;
  specifiedDocument: string;
  exportProcessingZone: string;
  virtualReturn: string;
  bidCustomer: string;
  shippingCondition: string;
  signingBack: string;
  deliverySlipTemplate: string;
  deliveryUnloadingReq: string;
  supplierAccount: string;
  labelTemplate: string;
  disableShipping: string;
  deliveryOtherNote: string;
  receivingInvoiceContact: number;
  shippingInfo: string;
  invoicingByMail: string;
  autoBilling: string;
  invoiceType: string;
  financialNote: string;
  customerCode: string;
  exchangeRate: string;
  orderLabel: string;
  evmMachineNo: string;
  supplierCode: string;
  invoiceMode: string;
  attachOrder: BooleanType;
  attachCoa: BooleanType;
  attachMsds: BooleanType;
  attachTds: BooleanType;
  receiptTimeCategory: BooleanType;
  hideLogo: BooleanType;
  deliverySensitivity: BooleanType;
  combinedDelivery: string;
  customerAcceptSysDate: BooleanType;
  orderBasis: string;
  sketchOrderNo: string;
  creator: string;
  tradeNo: string;
}

export type BTBResponse = {
  unclearBTBInfo: string;
  deliveryBTBInfo: string;
};

export type itemResultList = {
  addType: '1';
  itemValidatorResultList: itemValidatorResult[];
  orderItemNo: string;
  validateItemNo: string;
};
export type validatorResultAllDTO = {
  headerResultList: itemValidatorResult[];
  itemResultList: itemResultList[];
};

export type SaveDraftOrder = EditOrder &
  Partical<{
    validatorResultDTOList: itemValidatorResult[];
    sketchOrderScene: string;
    validatorResultAllDTO: validatorResultAllDTO;
    actionSource: string;
  }>;

export interface DocMetaData {
  source: string;
  dimension: string;
  businessId: string;
  docMetaDataList: UploadUserFile[];
  docUploadScene: string;
}
