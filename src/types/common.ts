import * as defaults from '@/constants';

export type OrderType = defaults.OrderType;

export type CompanyType = defaults.CompanyType;

export type SalesRangeType = {
  salesOrganization: string;
  distributionChannel: string;
  productGroup: string;
};

export enum TaxEnum {
  Yes = '1',
  No = '0',
}

export interface SalesDictType {
  id?: number;
  code: string;
  listKey?: string;
  listName?: string;
  name: string;
  parentCode: string;
  label?: string;
  disabled?: boolean;
  persistDisabled?: boolean;
  status?: string;
  supportQuery?: string;
  rules?: string;
}

export type DictListType = Record<string, SalesDictType[]>;
export interface DictList {
  [key: string]: SalesDictType[];
}

export interface MMDictList {
  [key: string]: MMDictType[];
}

export interface MMDictType {
  value: string;
  name: string;
  type: string;
  index: number;
  description: string;
}

export interface ContactType {
  address: string;
  addressDetail: string;
  addressId: string;
  city: string;
  cityName: string;
  contactId: string;
  contactName: string;
  contactPhone: string;
  customerCode: string;
  customerName: string;
  distribChannel: string;
  jobTitle: string;
  mobilephone: string;
  parentCode: string;
  province: string;
  provinceName: string;
  region: string;
  regionName: string;
  receiverProvinceCode: string;
  receiverCityCode: string;
  receiverDistrictCode: string;
  receiverAddressId: string;
}

export type DraftOrderData = Record<string, any> & {
  ValidatorResultDTOList: ValidatorResultDTO;
};

export type OrderBooleanType = 'Z' | 'X' | boolean;

export interface ValidatorResultDTO {
  validateMsg: string;
  validateName: string;
}

export type ApiResponseType = {
  code: number;
  data: any;
  message: string;
  success?: boolean;
  msg?: string;
};
export interface MenuItem {
  id?: number;
  type: string;
  link: string;
  name: string;
  icon?: string;
  children?: MenuItem[];
}

export interface Role {
  bizRoleCode: string;
  bizRoleName: string;
  roleNames?: string[];
}

export interface Options {
  value: string;
  label: string;
  code?: string;
  key: string;
  disabled?: boolean;
}
export interface OrderFieldsSettings {
  prop: string;
  label?: string;
  visible?: boolean;
  field?: string;
  style?: string;
  type?: string;
  slot?: string;
  headerSlot?: string;
  width: string;
  fixed?: string;
  showOverflow?: boolean;
  options?: Options[];
  optionKey?: string;
  params?: Record<string, any>;
  title?: string;
  span?: number;
  labelWidth?: string;
  rules?: any;
  disabled?: boolean;
  required?: boolean;
  maxlength?: number;
  placeholder?: string;
  maxLength?: number;
  multiple?: boolean;
  maxProp?: string;
  min?: number;
  max?: number;
  precision?: number;
  showCurrencySymbol?: boolean;
  children?: OrderFieldsSettings[];
}

export type ConfigType = 'OrderCreate' | 'OrderDraft';
export interface OrderTemplateKeyJson {
  configType: ConfigType;
  keyJson: {
    orderType: OrderType;
    configType: ConfigType;
    orderBasis: string;
    stage: string;
  };
}

export interface OrderBasisConfig {
  factoryConfig: string;
  freeTaxSaleAssessmentUnitPriceConfig: 'geZero' | 'gtZero';
}
