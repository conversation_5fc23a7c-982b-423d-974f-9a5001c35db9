import fs from 'node:fs';
import path, { parse } from 'node:path';

// access token
const token =
' bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.Nr4Y-kVoriwybYrniN3RXvNSeZUQMM0pLKsXCzKY-NI'
// 配置地址,注意区别开发环境
const url = 'https://zkh-form-center-uat.zkh360.com/front-end/display/template/add';

const currentDir = '库存出清订单';
try {
  const currentDirPath = path.resolve();
  const dir = path.join(currentDirPath, `../templateData/${currentDir}`);
  fs.readdirSync(dir, { withFileTypes: true }).forEach(function (dirent) {
    const filePath = path.join(dir, dirent.name);
    if (dirent.isFile() && dirent.name.indexOf('.json') > -1) {
      let data = fs.readFileSync(filePath, 'utf8');
      if (data) {
        const parseData = JSON.parse(data);
        const templateInfo = JSON.stringify(parseData.templateInfo);
        delete parseData.appointmentKeyId;
        data = JSON.stringify({
          ...parseData,
          templateInfo,
        });
        console.log(parseData.keyJson)
        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `${token}`,
          },
          body: data,
        })
          .then((res) => res.json())
          .then((res) => {
            console.log(dirent.name, '---->', res);
          });
      }
    }
  });
} catch (err) {
  console.error(err);
}
