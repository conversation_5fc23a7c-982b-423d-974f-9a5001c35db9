import { URL, fileURLToPath } from 'node:url';

import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import UnoCSS from 'unocss/vite';
import arms from '@boss/vite-plugin-arms';
import { compression } from 'vite-plugin-compression2';

const useCDN = process.env.cdn === 'yes';

const target = 'https://boss-uat.zkh360.com';
const webComponents = [];

export default defineConfig({
  base: useCDN ? 'https://files.zkh360.com/assets/sr/' : '/sr/',
  plugins: [
    UnoCSS(),
    arms({}),
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => webComponents.includes(tag),
        },
      },
    }),
    compression({
      algorithm: 'brotliCompress',
    }),
    compression({
      algorithm: 'gzip',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 5177,
    host: '0.0.0.0',
    proxy: {
      '/api/sales': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/data-center': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-boss-opc': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-logistics': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-opc': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/ali-upload': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/documentApi': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-sales': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-boss-product': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-customer-product': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-mm': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-boss': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/internal-api': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/oms-config': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-acm-config': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-oss': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/reconciliation': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-process-center': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-form-center': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-opc-gray': {
        target: 'https://boss-uat-4.zkh360.com/',
        changeOrigin: true,
        secure: false,
      },
      '/api-opc-csc': {
        target: 'https://boss-uat-4.zkh360.com/',
        changeOrigin: true,
        secure: false,
      },
      '/security-api': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-fe-server': {
        target: 'https://boss-uat-4.zkh360.com/',
        changeOrigin: true,
        secure: false,
      },
      '/oss': {
        target: 'https://boss-uat.zkh360.com',
        changeOrigin: true,
        secure: false,
      },
      '/api-cc-gateway': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-agent': {
        target: 'http://local.zkh360.com:3000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
