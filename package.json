{"name": "@boss/so-v1", "version": "0.0.59", "private": true, "type": "module", "packageManager": "pnpm@8.15.7", "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint ."}, "dependencies": {"@boss/layout2": "0.0.1-dev.53", "@boss/refresh": "^0.0.6", "@bpmn/schema-table": "^1.4.8", "@element-plus/icons-vue": "^2.3.1", "@kun-plus/transfer": "0.0.6", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^10.7.2", "ali-oss": "^6.20.0", "axios": "^1.6.7", "dayjs": "^1.11.10", "element-plus": "2.4.4", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mitt": "^3.0.1", "nanoid": "^5.0.5", "pinia": "^2.1.7", "qs": "^6.11.2", "sa-sdk-javascript": "^1.26.10", "shortid": "^2.2.16", "unocss": "^0.58.5", "vue": "^3.4.19", "vue-draggable-plus": "^0.5.3", "vue-router": "^4.2.5", "vxe-pc-ui": "^4.0.82", "vxe-table": "^4.7.77", "xe-utils": "^3.5.20"}, "devDependencies": {"@boss/vite-plugin-arms": "^0.0.5", "@boss/eslint-config": "0.1.0-beta.6", "@boss/prettier-config": "^1.0.0", "@types/ali-oss": "^6.16.11", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20.11.19", "@types/qs": "^6.9.11", "@types/shortid": "^0.0.32", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.5", "sass": "^1.71.0", "typescript": "~5.3.3", "unocss": "^0.58.5", "vite": "^5.1.3", "vite-plugin-compression2": "^1.3.0", "vue-tsc": "^1.8.27"}, "prettier": "@boss/prettier-config"}