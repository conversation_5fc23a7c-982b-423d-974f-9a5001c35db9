FROM newhub.zkh360.com/fe/nodejs:20.12.2-alpine3.19-pnpm8.15.7 as builder

WORKDIR /app
ARG cdn
ENV cdn=${cdn}

COPY . ./
RUN pnpm config set registry "https://registry.npmmirror.com/" && \
  pnpm install && \
  pnpm run build

FROM newhub.zkh360.com/fe/ossutil-boss:1.7.19 AS uploader
ARG cdn
ENV cdn=${cdn}
WORKDIR /usr/local/bin
COPY --from=builder /app ./app
RUN if [ "$cdn" = "yes" ]; then ./ossutil cp -r app/dist/assets/ oss://zkh360-boss/assets/sr/assets -e oss-cn-beijing.aliyuncs.com; else echo 'not use cdn!'; fi

FROM newhub.zkh360.com/fe/nginx:1.27.2-alpine3.20-brotli
ARG ENV
ENV ENV=${ENV}
COPY --from=uploader /usr/local/bin/app/dist/assets /opt/web/assets/
COPY --from=uploader /usr/local/bin/app/dist/index.html /opt/web/
COPY --from=uploader /usr/local/bin/app/web.conf /etc/nginx/conf.d/
